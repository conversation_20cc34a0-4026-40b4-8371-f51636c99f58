import pg_db as db
from sqlalchemy import text
import pandas as pd
import logging
import traceback
import datetime
import os
import csv
import math
import task
from utils import common_util
from pg_db_utils import pg_replenishment_util
from pg_db.analytics_db import AnalyticsDB
import time

logger = logging.getLogger()

def aggregate_sell_data(conn, days):
    query = f"SELECT variants_sku, SUM(quantity) AS sell_{days} FROM {AnalyticsDB.get_variants_trend_table()}  WHERE  order_date_time >= current_date - interval '{days}' day group by variants_sku, product_id;"
    return pd.read_sql(query, conn)

def fetch_aggregated_sell_data(conn):
    query = f"SELECT variants_sku, product_id, parent_sku, SUM(quantity) AS sell_180 FROM {AnalyticsDB.get_variants_trend_table()}  WHERE  order_date_time >= current_date - interval '180' day group by variants_sku, product_id, parent_sku;"
    #logger.info("Aggregating sells data for 180 days")
    df_180 = pd.read_sql(query, conn)
    #logger.info("Aggregating sells data for 90 days")
    df_90 = aggregate_sell_data(conn, 90)
    #logger.info("Aggregating sells data for 60 days")
    df_60 = aggregate_sell_data(conn, 60)
    #logger.info("Aggregating sells data for 45 days")
    df_45 = aggregate_sell_data(conn, 45)
    #logger.info("Aggregating sells data for 30 days")
    df_30 = aggregate_sell_data(conn, 30)

    #logger.info("Merging dataframes")
    df = pd.merge(df_180, df_90, on="variants_sku")
    df = pd.merge(df, df_60, on="variants_sku")
    df = pd.merge(df, df_45, on="variants_sku")
    df = pd.merge(df, df_30, on="variants_sku")

    return df

def create_sell_table(conn):
    
    df = fetch_aggregated_sell_data(conn)

    query = """
                DROP TABLE if exists analytics.sku_sells_new CASCADE;
                DROP INDEX if exists analytics.ix_analytics_sku_sells_new_index CASCADE;
            """
    conn.execute(text(query))
    conn.commit()
    #logger.info("Persisting data to DB")    
    df.to_sql(name='sku_sells_new', schema='analytics', con=conn, if_exists='replace')
    conn.commit()

    #logger.info("Renaming table data to DB")    

    query = """
        DROP TABLE if exists analytics.sku_sells CASCADE;
        ALTER TABLE analytics.sku_sells_new RENAME TO sku_sells;
    """
    conn.execute(text(query))
    conn.commit()

def refresh_all_profitability_tables(store_id):
    refresh_customers_trend_table(store_id)
    time.sleep(5)

    refresh_products_revenue_table(store_id)
    time.sleep(5)

    refresh_customers_trend_shipping_cost_table(store_id)
    time.sleep(5)

    refresh_orders_analytics_table(store_id)
    time.sleep(5)

    refresh_profitability_product_customer_groups_table(store_id)

def refresh_replenishment_tables(store_id):
    conn = None
    try:
        conn = db.get_connection(store_id)

        query = f"""
            DROP TABLE if exists {AnalyticsDB.get_variants_trend_table()}_1 CASCADE;
            DROP TABLE if exists {AnalyticsDB.get_products_trend_table()}_1 CASCADE;
        """
        conn.execute(text(query))
        conn.commit()
        query = f"""
            create table {AnalyticsDB.get_variants_trend_table()}_1 as 
            select o.order_id,od.customer_id,o.product_id,o.variant_id,o.quantity,o.quantity_shipped,
            o.product_price,o.product_price_after_discount,o.price_ex_tax,o.price_inc_tax,
            o.price_tax,o.base_total,o.product_total_cost,o.total_ex_tax,
            o.total_inc_tax,o.total_tax,o.refund_amount,
            v.parent_sku,v.variants_sku,v.product_name,v.variant_options,
			sc.primary_supplier,
            od.order_status_id,date(od.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST') as order_date_time,
            od.order_day,od.order_month,od.order_year,od.order_qtr
            FROM public.orders od
            JOIN public.order_line_items o ON od.order_id = o.order_id
            JOIN public.variants v ON o.variant_id = v.variants_id
            LEFT JOIN public.skuvault_catalog sc ON sc.sku = v.variants_sku
            WHERE od.order_created_date_time > current_date - interval '15' month 
            and od.order_status_id not in (0,5,6,7)
            order by od.order_id asc;
        """
        #logger.info("Creating analytics_variants_trend_1 table")        
        start_time = datetime.datetime.now().timestamp()
        conn.execute(text(query))
        conn.commit()
        end_time = datetime.datetime.now().timestamp()
        total_time = (end_time - start_time)
        #logger.info(f"analytics_variants_trend_1 table has been created. Time: {total_time}")        

        query = f"""
            create table {AnalyticsDB.get_products_trend_table()}_1 as 
            select 
                p.product_id, 
                max(p.parent_sku) as parent_sku, 
                max(p.product_name) as product_name, 
                sum(p.quantity) as quantity, 
                sum(p.base_total) as total, 
                avg(p.product_price) as price,
                max(sc.primary_supplier) as primary_supplier,
                p.order_date_time, 
                p.order_id, 
                p.customer_id,
                EXTRACT(DAY FROM p.order_date_time) as order_day,
                EXTRACT(WEEK FROM p.order_date_time) as order_week,
                EXTRACT(MONTH FROM p.order_date_time) as order_month,
                EXTRACT(QUARTER FROM p.order_date_time) as order_qtr,
                EXTRACT(YEAR FROM p.order_date_time) as order_year
            from {AnalyticsDB.get_variants_trend_table()}_1 p
            LEFT JOIN public.skuvault_catalog sc ON p.variants_sku = sc.sku
            group by p.product_id, p.order_date_time, p.order_id, p.customer_id
            having sum(p.quantity) > 0
            order by p.product_id desc;
        """

        #logger.info("Creating analytics_products_trend_1 table")        
        start_time = datetime.datetime.now().timestamp()
        conn.execute(text(query))
        conn.commit()
        end_time = datetime.datetime.now().timestamp()
        total_time = (end_time - start_time)
        #logger.info(f"analytics_products_trend_1 table has been created. Time: {total_time}") 


        query = f"""
            DROP TABLE if exists {AnalyticsDB.get_variants_trend_table()} CASCADE;
            ALTER TABLE {AnalyticsDB.get_variants_trend_table()}_1 RENAME TO {AnalyticsDB.variants_trend_table};
            DROP TABLE if exists {AnalyticsDB.get_products_trend_table()} CASCADE;
            ALTER TABLE {AnalyticsDB.get_products_trend_table()}_1 RENAME TO {AnalyticsDB.products_trend_table};
        """
        conn.execute(text(query))
        conn.commit()

        create_sell_table(conn)
        update_replenishment_csv_file(conn, store_id)
    except Exception as ex:
        error_msg = str(traceback.format_exc())
        logger.error(error_msg)
        raise Exception("Failed to create analytics table: " + error_msg)
    finally:
        if conn:
            conn.commit()
            conn.close()

    pg_replenishment_util.create_replenishment_table(store_id=store_id)
    task.submit_task('update_analytics_daily_sales_tables', (store_id,))

def refresh_customers_trend_table(store_id):
    conn = None
    try:
        conn = db.get_connection(store_id)

        query = f"""
            DROP TABLE if exists {AnalyticsDB.get_profitability_customers_trend_table()}_1 CASCADE;
        """
        conn.execute(text(query))
        conn.commit()
    
        query = f"""
            create table {AnalyticsDB.get_profitability_customers_trend_table()}_1 as 
            select
                od.customer_id,
                sum(o.total_ex_tax) as revenue, 
				sum(o.sv_cost*o.quantity) as total_cost,
				sum(o.total_ex_tax - (o.sv_cost*o.quantity)) as profit,
                count(distinct o.order_id) as orders,
                EXTRACT(DAY FROM date(od.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) as order_day,
                EXTRACT(WEEK FROM date(od.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) as order_week,
                EXTRACT(MONTH FROM date(od.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) as order_month,
                EXTRACT(QUARTER FROM date(od.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) as order_qtr,
                EXTRACT(YEAR FROM date(od.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) as order_year,
                (od.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST') as order_date
            from public.orders od
            LEFT JOIN public.order_line_items o ON od.order_id = o.order_id
            where od.order_created_date_time > current_date - interval '18' month
            and od.order_status_id not in (0,5,6,7)
            group by od.customer_id, order_date
        """
        #logger.info("Creating analytics_customers_trend_1 table")        
        start_time = datetime.datetime.now().timestamp()
        conn.execute(text(query))
        conn.commit()
        end_time = datetime.datetime.now().timestamp()
        total_time = (end_time - start_time)
        #logger.info(f"analytics_customers_trend_1 table has been created. Time: {total_time}") 


        query = f"""
            DROP TABLE if exists {AnalyticsDB.get_profitability_customers_trend_table()} CASCADE;
            ALTER TABLE {AnalyticsDB.get_profitability_customers_trend_table()}_1 RENAME TO {AnalyticsDB.profitability_customers_trend_table};
        """
        conn.execute(text(query))
        conn.commit()

    except Exception as ex:
        error_msg = str(traceback.format_exc())
        logger.error(error_msg)
        raise Exception("Failed to create analytics table: " + error_msg)
    finally:
        if conn:
            conn.commit()
            conn.close()

def refresh_customers_trend_shipping_cost_table(store_id):
    conn = None
    try:
        conn = db.get_connection(store_id)

        query = f"""
            DROP TABLE if exists {AnalyticsDB.get_profitability_customers_shipping_cost_table()}_1 CASCADE;
        """
        conn.execute(text(query))
        conn.commit()
    
        query = f"""
            create table {AnalyticsDB.get_profitability_customers_shipping_cost_table()}_1 as 
            select
                od.customer_id,
                count(od.order_id) as orders,
                array_agg(distinct od.order_id) as order_ids,
                SUM(od.base_shipping_cost) as shipping_cost,
                SUM(od.coupon_discount) as coupon_discount,
                EXTRACT(DAY FROM date(od.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) as order_day,
                EXTRACT(WEEK FROM date(od.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) as order_week,
                EXTRACT(MONTH FROM date(od.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) as order_month,
                EXTRACT(QUARTER FROM date(od.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) as order_qtr,
                EXTRACT(YEAR FROM date(od.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) as order_year,
                date(od.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST') as order_date
            from public.orders od
            where od.order_created_date_time > current_date - interval '24' month
            and od.order_status_id not in (0,5,6,7)
            group by od.customer_id, order_date
        """
        #logger.info("Creating analytics_customers_trend_1 table")        
        start_time = datetime.datetime.now().timestamp()
        conn.execute(text(query))
        conn.commit()
        end_time = datetime.datetime.now().timestamp()
        total_time = (end_time - start_time)
        #logger.info(f"analytics_customers_trend_1 table has been created. Time: {total_time}") 


        query = f"""
            DROP TABLE if exists {AnalyticsDB.get_profitability_customers_shipping_cost_table()} CASCADE;
            ALTER TABLE {AnalyticsDB.get_profitability_customers_shipping_cost_table()}_1 RENAME TO {AnalyticsDB.profitability_customers_shipping_cost_table};
        """
        conn.execute(text(query))
        conn.commit()

    except Exception as ex:
        error_msg = str(traceback.format_exc())
        logger.error(error_msg)
        raise Exception("Failed to create analytics table: " + error_msg)
    finally:
        if conn:
            conn.commit()
            conn.close()

def refresh_products_revenue_table(store_id):
    conn = None
    try:
        conn = db.get_connection(store_id)

        query = f"""
            DROP TABLE if exists {AnalyticsDB.get_profitability_products_revenue_table()}_1 CASCADE;
        """
        conn.execute(text(query))
        conn.commit()
    
        query = f"""
            create table {AnalyticsDB.get_profitability_products_revenue_table()}_1 as 
            select
                v.parent_sku,
                v.product_id,
                sum(o.total_ex_tax) as revenue, 
				sum(o.sv_cost*o.quantity) as total_cost,
				sum(o.total_ex_tax - (o.sv_cost*o.quantity)) as profit,
                count(distinct o.order_id) as orders,
                EXTRACT(DAY FROM date(od.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) as order_day,
                EXTRACT(WEEK FROM date(od.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) as order_week,
                EXTRACT(MONTH FROM date(od.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) as order_month,
                EXTRACT(QUARTER FROM date(od.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) as order_qtr,
                EXTRACT(YEAR FROM date(od.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) as order_year,
                (od.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST') as order_date
            from public.orders od, public.order_line_items o, public.variants v
            where od.order_id = o.order_id and o.variant_id = v.variants_id 
            and od.order_created_date_time > current_date - interval '24' month
            and od.order_status_id not in (0,5,6,7)
            group by v.parent_sku, v.product_id, order_date
        """
        #logger.info("Creating analytics_customers_trend_1 table")        
        start_time = datetime.datetime.now().timestamp()
        conn.execute(text(query))
        conn.commit()
        end_time = datetime.datetime.now().timestamp()
        total_time = (end_time - start_time)
        #logger.info(f"analytics_customers_trend_1 table has been created. Time: {total_time}") 


        query = f"""
            DROP TABLE if exists {AnalyticsDB.get_profitability_products_revenue_table()} CASCADE;
            ALTER TABLE {AnalyticsDB.get_profitability_products_revenue_table()}_1 RENAME TO {AnalyticsDB.profitability_products_revenue_table};
        """
        conn.execute(text(query))
        conn.commit()

    except Exception as ex:
        error_msg = str(traceback.format_exc())
        logger.error(error_msg)
        raise Exception("Failed to create analytics table: " + error_msg)
    finally:
        if conn:
            conn.commit()
            conn.close()

def refresh_orders_analytics_table(store_id):
    conn = None
    try:
        conn = db.get_connection(store_id)

        query = f"""
            DROP TABLE if exists {AnalyticsDB.get_order_line_items_analytics_table()}_1 CASCADE;
            DROP TABLE if exists {AnalyticsDB.get_order_analytics_table()}_1 CASCADE;
        """
        conn.execute(text(query))
        conn.commit()
    
        query = f"""
            create table {AnalyticsDB.get_order_line_items_analytics_table()}_1 as 
            SELECT
                o.order_id,
                o.customer_id,
                (o.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST') AS order_created_date_time,
                ol.product_id,
                ol.variant_id,
                ol.variant_sku,
                ol.quantity,
                ol.sv_cost,
                ol.product_price,
                ol.total_ex_tax,
                ol.total_inc_tax,
                (ol.sv_cost * ol.quantity) AS total_cost,
                (ol.total_ex_tax - (ol.sv_cost * ol.quantity)) AS profit,
                sc.primary_supplier,
                sc.classification,
                usm.user_name AS purchaser_name,
                p.brand_id,
                EXTRACT(DAY FROM date(o.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) as order_day,
                EXTRACT(WEEK FROM date(o.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) as order_week,
                EXTRACT(MONTH FROM date(o.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) as order_month,
                EXTRACT(QUARTER FROM date(o.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) as order_qtr,
                EXTRACT(YEAR FROM date(o.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) as order_year
            FROM public.orders o
            JOIN public.order_line_items ol ON o.order_id = ol.order_id
            LEFT JOIN public.products p ON p.product_id = ol.product_id
            LEFT JOIN public.variants v ON ol.variant_id = v.variants_id
            LEFT JOIN public.skuvault_catalog sc ON v.variants_sku = sc.sku
            LEFT JOIN public.user_supplier_mapping usm ON sc.primary_supplier = usm.suppliers
            WHERE o.order_created_date_time > current_date - interval '18' month
            AND o.order_status_id NOT IN (0,5,6,7)
        """
        #logger.info("Creating analytics_customers_trend_1 table")        
        start_time = datetime.datetime.now().timestamp()
        conn.execute(text(query))
        conn.commit()
        end_time = datetime.datetime.now().timestamp()
        total_time = (end_time - start_time)
        #logger.info(f"analytics_customers_trend_1 table has been created. Time: {total_time}") 

        query = f"""
            create table {AnalyticsDB.get_order_analytics_table()}_1 as 
            SELECT
                o.order_id,
                MAX(o.customer_id) AS customer_id,
                SUM(ol.total_ex_tax) AS total_revenue,
                SUM(ol.total_cost) AS total_cost,
                SUM(ol.profit) AS total_profit,
                CASE 
                    WHEN SUM(ol.total_ex_tax) IS NULL OR SUM(ol.total_ex_tax) = 0 THEN 0
                    ELSE ROUND(CAST((SUM(ol.profit) / SUM(ol.total_ex_tax) * 100) AS NUMERIC), 2)
                END AS profit_percentage,
                MAX(ol.order_created_date_time) AS order_date,
                MAX(o.order_status_id) AS order_status_id,
                MAX(o.coupon_id) AS coupon_id,
                MAX(o.coupon_code) AS coupon_code,
                MAX(o.coupon_discount) AS coupon_discounts,
                MAX(o.discount_amount) AS discount_amount,
                MAX(o.sub_total_excluding_tax) AS subtotal_ex_tax,
                MAX(o.sub_total_including_tax) AS subtotal_inc_tax,
                MAX(o.total_excluding_tax) AS total_ex_tax,
                MAX(o.total_including_tax) AS total_inc_tax,
                MAX(o.shipping_including_tax) AS shipping_inc_tax,
                MAX(o.shipping_excluding_tax) AS shipping_ex_tax,
                MAX(o.base_shipping_cost) AS base_shipping_cost,
                MAX(o.total_items) AS total_items,
                MAX(o.total_tax) AS total_tax,
                EXTRACT(DAY FROM date(ol.order_created_date_time)) as order_day,
                EXTRACT(WEEK FROM date(ol.order_created_date_time)) as order_week,
                EXTRACT(MONTH FROM date(ol.order_created_date_time)) as order_month,
                EXTRACT(QUARTER FROM date(ol.order_created_date_time)) as order_qtr,
                EXTRACT(YEAR FROM date(ol.order_created_date_time)) as order_year
            FROM {AnalyticsDB.get_order_line_items_analytics_table()}_1 ol
            LEFT JOIN public.orders o ON o.order_id = ol.order_id
            GROUP BY o.order_id, ol.order_created_date_time
        """
        #logger.info("Creating analytics_customers_trend_1 table")        
        start_time = datetime.datetime.now().timestamp()
        conn.execute(text(query))
        conn.commit()
        end_time = datetime.datetime.now().timestamp()
        total_time = (end_time - start_time)
        #logger.info(f"analytics_customers_trend_1 table has been created. Time: {total_time}") 

        query = f"""
            DROP TABLE if exists {AnalyticsDB.get_order_line_items_analytics_table()} CASCADE;
            ALTER TABLE {AnalyticsDB.get_order_line_items_analytics_table()}_1 RENAME TO {AnalyticsDB.order_line_items_analytics_table};
            DROP TABLE if exists {AnalyticsDB.get_order_analytics_table()} CASCADE;
            ALTER TABLE {AnalyticsDB.get_order_analytics_table()}_1 RENAME TO {AnalyticsDB.order_analytics_table};
        """
        conn.execute(text(query))
        conn.commit()

    except Exception as ex:
        error_msg = str(traceback.format_exc())
        logger.error(error_msg)
        raise Exception("Failed to create analytics table: " + error_msg)
    finally:
        if conn:
            conn.commit()
            conn.close()

def update_replenishment_csv_file(conn, store_id):    
    current_month_name = datetime.datetime.now().strftime("%b")    
    try:
        formatted_data = []
        parent_sku_string = ''           

        base_query = """
            select sv.parent_sku, max(pr.product_name) as title, max(sv.primary_supplier) as primary_supplier, max(sv.classification) as classification, 
            max(sv.brand) as brand, max(sv.cost) as cost, max(sv.retail_price) as retail_price, 
            max(sv.sale_price) as sale_price, max(sv.reorder_point) as reorder_point, 
            max(sv.incremental_quantity) as incremental_quantity, 
            sum(sv.quantity_on_hand) as quantity_on_hand, sum(sv.quantity_pending) as quantity_pending, 
            sum(sv.quantity_incoming) as quantity_incoming, sum(sv.quantity_available) as quantity_available, 
            sum(sv.quantity_on_hold) as quantity_on_hold 
            from public.skuvault_catalog sv, public.products pr 
            where sv.parent_sku = pr.sku and sv.primary_supplier <> 'Unknown'               
            group by sv.parent_sku, sv.primary_supplier, sv.classification, sv.brand ORDER BY primary_supplier DESC
            """.replace("\n", "")
        
        # order_by = " ORDER BY primary_supplier DESC "

        final_query = base_query                                         
        rs = conn.execute(text(final_query))
        # ...

        columns = ["parent_sku", "title", "primary_supplier", "classification", "brand", "cost", "retail_price", "sale_price", "reorder_point", "incremental_quantity", "quantity_on_hand", "quantity_pending", "quantity_incoming", "quantity_available", "quantity_on_hold"]

        for row in rs:
            formatted_row = {column: value for column, value in zip(columns, row)}
            formatted_data.append(formatted_row)                                  
        
        # ...
        for res in formatted_data:
            parent_sku_string = parent_sku_string + "'" + res['parent_sku'] + "', "

        parent_sku_string = parent_sku_string.rstrip(', ')

        if parent_sku_string != '':
            monthly_result, month_names, day_difference = common_util.get_six_month_data_using_parent_sku(parent_sku_string, conn, 6, True)               

            # get last 30 days data for total sold
            monthly_result_30, month_names_30, day_difference_30 = common_util.get_six_month_data_using_parent_sku(parent_sku_string, conn, 1, False)

            for res in formatted_data:
                res['suggested_order_qty'] = 0
                res['to_order_qty'] = 0
                res['total_sold'] = 0
                res['total_sold_30'] = 0
                res['weeks_on_hand'] = 0
                res['turn_rate'] = 0
                res['sale_history_months'] = 6
                res['days_to_replenishment'] = 30
                res['days_to_replenishment_45'] = 45
                res['days_to_replenishment_60'] = 60

                for month_key, month_value in month_names.items():
                    res[month_value] = 'NA'
                
                for mdata in monthly_result:
                    if str(mdata['parent_sku']) == str(res['parent_sku']):
                        for month_key, month_value in month_names.items():
                            if month_value == mdata['order_month']:
                                res[month_value] = mdata['sold_quantity']
                                if month_value != current_month_name:
                                    res['total_sold'] = res['total_sold'] + mdata['sold_quantity']
            
                for mdata_30 in monthly_result_30:
                    if str(mdata_30['parent_sku']) == str(res['parent_sku']):
                        for month_key, month_value in month_names_30.items():
                            if month_value == mdata_30['order_month']:
                                res['total_sold_30'] = res['total_sold_30'] + mdata_30['sold_quantity'] 
            
            for res in formatted_data:                   
                future_sales = res['total_sold_30']
                future_sales_45 = future_sales * 1.5
                future_sales_60 = future_sales * 2
                res['suggested_order_qty'] = int((future_sales - res['quantity_available']) - res['quantity_incoming'])
                res['suggested_order_qty_45'] = int((future_sales_45 - res['quantity_available']) - res['quantity_incoming'])
                res['suggested_order_qty_60'] = int((future_sales_60 - res['quantity_available']) - res['quantity_incoming'])

                res['to_order_qty'] = math.ceil(res['suggested_order_qty']/res['incremental_quantity'])*res['incremental_quantity'] if res['suggested_order_qty'] > 0 else 0
                res['turn_rate'] = (
                        ((res['total_sold'] / res['quantity_available'] if res['quantity_available'] != 0 else 1) * 365) / day_difference)
                res['weeks_on_hand'] = (
                        52 / res['turn_rate'] if res['turn_rate'] != 0 else 1)
        
        folder_path = 'reports/replenishment/' + str(store_id) + '/'

        file_path = folder_path + 'replenishment.csv'
        if not os.path.exists(file_path):
            os.makedirs(folder_path)                

        with open(file_path, mode='w', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, fieldnames=formatted_data[0].keys())
            writer.writeheader()
            for row in formatted_data:
                writer.writerow(row)       
    
    except Exception as e:
        logger.error(traceback.format_exc())         

def refresh_profitability_product_customer_groups_table(store_id):
    conn = db.get_connection(store_id)
    try:
        query = f"""
            DROP TABLE if exists {AnalyticsDB.get_profitability_product_customer_groups_table()}_1 CASCADE;
        """
        conn.execute(text(query))
        conn.commit()

        query = f"""
            create table {AnalyticsDB.get_profitability_product_customer_groups_table()}_1 as 
            SELECT
                li.product_id as product_id,
                cu.customer_group_id as customer_group_id,
                SUM(li.total_ex_tax) AS revenue,
                SUM(li.sv_cost * li.quantity) AS total_cost,
                SUM(li.total_ex_tax) - SUM(li.sv_cost * li.quantity) AS profit,
                date(od.order_created_date_time AT TIME ZONE 'UTC' AT TIME ZONE 'CST') as order_date
            FROM public.orders od, public.order_line_items li, public.customers cu
            WHERE od.order_id = li.order_id and od.customer_id = cu.customer_id
            and od.order_created_date_time > current_date - interval '18 month'
            GROUP BY li.product_id, cu.customer_group_id, order_date;
        """
        conn.execute(text(query))
        conn.commit()

        query = f"""
            DROP TABLE if exists {AnalyticsDB.get_profitability_product_customer_groups_table()} CASCADE;
            ALTER TABLE {AnalyticsDB.get_profitability_product_customer_groups_table()}_1 RENAME TO {AnalyticsDB.profitability_product_customer_groups_table};
        """
        conn.execute(text(query))
        conn.commit()
    except Exception as ex:
        logger.error(traceback.format_exc())
        raise Exception("Failed to refresh profitability product customer groups table")
    finally:
        conn.commit()
        conn.close()
