from datetime import datetime, timedelta, timezone
import requests
import time
from utils import redis_util, store_util
from mongo_db import salesforce_db
import logging
import traceback
import pg_db
from pg_db.salesforce_db import SalesforceCustomerRep, SalesforceOrderDetails
from sqlalchemy import text, update
from plugin import bc_products
import json
from pg_db import bulk_orders_db
import re
import task

logger = logging.getLogger()

payment_terms_mapping = {
    "Not Set": "Not Set",
    "Net 15": "15 Days",
    "Net 30 On Delivery": "30 Days",
    "On Delivery": "7 Days",
    "As Shipped": "As Shipped",
    "60 Days": "60 Days"
}

def create_token(store_id, api_data):

    request_body = {
        'client_id': api_data['client_id'],
        'client_secret': api_data['secret'],
        'username': api_data['username'],
        'password': api_data["james"],
        'grant_type': 'password',
        'format': 'json'
    }

    # client_id = api_data['client_id']
    # secret = api_data['secret']
    # uname = api_data['username']
    # james = api_data["james"]
    # query_params = "grant_type=password&client_id=" + client_id + "&client_secret=" + secret + "&username=" + uname + "&password=" + james
    # api_url = "https://login.salesforce.com/services/oauth2/token?" + query_params
    api_url = "https://login.salesforce.com/services/oauth2/token"
    headers = {}
    headers["Accept"] = "application/json"
    headers["Content-Type"] = 'application/x-www-form-urlencoded'
    logger.info(request_body)
    res = requests.post(url=api_url, headers=headers, data=request_body)
    token = None
    if res.status_code < 299:
        token = res.json()['access_token']
        redis_util.update_salesforce_token(store_id, token)
    else:
        raise Exception(res.json())
        #print(res.status_code, res.json())
    return token

def _call_get_api(store_id, api_data, api_url, max_retry=5, time_interval_sec=10):
    retry = 0
    res = None
    auth_retry = 0
    token = redis_util.get_salesforce_token(store_id)
    if not token:
        auth_retry = 1
        token = create_token(store_id, api_data)
        if not token:
            return None
    
    while retry < max_retry:
        headers = {}
        headers["Authorization"] = "Bearer " + token
        headers["Accept"] = "application/json"
        res = requests.get(url=api_url, headers=headers)
        if res.status_code < 299:
            retry = max_retry + 1
        elif res.status_code == 422 or res.status_code >= 500:
            retry = retry + 1
            time.sleep(time_interval_sec)
            logger.error("salesforce._call_get_api: status_code: " + str(res.status_code))
        elif res.status_code == 401:
            if auth_retry == 0:
                token = create_token(store_id, api_data)
                if not token:
                    retry = max_retry + 1
            else:
                retry = max_retry + 1
            logger.error(res)
        else:
            retry = max_retry + 1
            logger.error("salesforce._call_get_api: status_code: " + str(res.status_code))
    return res

def _run_query(store_id, api_data, query):
    base_url = api_data['base_url']
    api_url = base_url + "/services/data/v57.0/query?q=" + query
    res = _call_get_api(store_id, api_data, api_url)
    return res

def fetch_all_records(store_id, api_data, query, processor=None):
    res = _run_query(store_id, api_data, query)
    # if not res:
    #     raise Exception("Failed to fetch data from salesforce...")

    result = {
        "status_code": 200,
        "error": "",
        "totalSize": 0,
        "data": []
    }
    if res.status_code < 299:
        data = res.json()
        totalSize = data['totalSize']
        result['totalSize'] = totalSize
        result['data'] = data['records']
        if processor:
            processor(data['records'])
        done = data['done']
        while not done and res.status_code < 299:
            api_url = data['nextRecordsUrl']
            #print(api_url)
            if api_url:
                res = _call_get_api(store_id, api_data, api_data['base_url'] + api_url)
                if not res:
                    #print("Failed to call salesforce")
                    return
                if res.status_code < 299:
                    data = res.json()
                    result['data'].extend(data['records'])
                    done = data['done']
                    if processor:
                        processor(data['records'])
                else:
                    result['status_code'] = res.status_code
                    result['error'] = res.json()
    else:
        result['status_code'] = res.status_code
        result['error'] = res.json()

    return result

def fetch_all_accounts(store_id, api_data):
    query = """SELECT+Id,Name,BC_CustomerId__c,Sales_Rep__r.User__r.Email,Sales_Rep__r.User__r.Phone,Sales_Rep__r.User__r.Division,Sales_Rep__r.User__r.MobilePhone,Sales_Rep__r.Name,Sales_Rep__r.Type__c,Customer_Type__c,Block_Credit_Card_Payment__c,Store__c,CBD_Store_Customer_ID__c,Payment_Term__c,Credit_Limit__c,Pricelist__c,(SELECT+Expiration_Date__c,Name,License__c,License_Permit_Exhibit_Number_Expiry__c+FROM+File_Approve_Rejection__r)+from+Account"""
    res = fetch_all_records(store_id, api_data, query)
    return res

def build_customer_rep_model(customer_rep):
    customer_id = customer_rep.get("BCId", None)
    if not customer_id:
        return None
    customer_name = customer_rep.get("Name", "")
    sf_id = customer_rep.get("SFId", "")
    customer_type = customer_rep.get("Type", "")
    block_credit_card_payment = customer_rep.get("is_credit_card_payment_block", False)
    payment_term = customer_rep.get("PaymentTerm", None)
    credit_limit = customer_rep.get("CreditLimit", None)
    price_list = customer_rep.get("PriceList", None)
    rep_name = ""
    rep_email = ""
    rep_phone = ""
    rep_mobile = ""
    rep_type = ""
    rep = customer_rep.get("rep", None)
    if rep:
        rep_name = rep.get("Name", "")
        rep_email = rep.get("Email", "")
        rep_phone = rep.get("Phone", "")
        rep_mobile = rep.get("MobilePhone", "")
        rep_type = rep.get("Type", "")

    return {
        "customer_id": customer_id,
        "customer_name": customer_name,
        "type": customer_type,
        "sf_id": sf_id,
        "rep_name": rep_name,
        "rep_email": rep_email,
        "rep_phone": rep_phone,
        "rep_mobile": rep_mobile,
        "is_credit_card_payment_block": block_credit_card_payment,
        "payment_term": payment_term,
        "credit_limit": credit_limit,
        "rep_type": rep_type,
        "price_list": price_list
    }

def update_customer_rep_table(store_id, customer_reps):
    session = pg_db.get_session(store_id)
    try:
        with session.begin():
            for customer_rep in customer_reps:
                model = build_customer_rep_model(customer_rep)
                session.merge(SalesforceCustomerRep(**model))
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        if session:
            session.commit()
            session.close()

def update_purchase_orders_table(store_id, customer_reps):
    session = pg_db.get_session(store_id)
    try:
        with session.begin():
            for customer_rep in customer_reps:
                model = build_customer_rep_model(customer_rep)
                if model:
                    # Update bo_purchase_orders table
                    stmt_po = (
                        update(bulk_orders_db.PurchaseOrders)
                        .where(bulk_orders_db.PurchaseOrders.customer_id == model["customer_id"])
                        .values(
                            # customer_rep_id=model["sf_id"],
                            customer_rep_name=model["rep_name"],
                            customer_rep_email=model["rep_email"]
                        )
                    )
                    session.execute(stmt_po)

                    # Update bo_distribution_lineitems table
                    stmt_dl = (
                        update(bulk_orders_db.distributions_lineitems)
                        .where(bulk_orders_db.distributions_lineitems.customer_id == model["customer_id"])
                        .values(
                            # customer_rep_id=model["sf_id"],
                            customer_rep_name=model["rep_name"],
                            customer_rep_email=model["rep_email"]
                        )
                    )
                    session.execute(stmt_dl)

                    # Update bo_published_distribution_logs table
                    stmt_pdl = (
                        update(bulk_orders_db.bo_published_distribution_logs)
                        .where(bulk_orders_db.bo_published_distribution_logs.customer_id == model["customer_id"])
                        .values(
                            # customer_rep_id=model["sf_id"],
                            customer_rep_name=model["rep_name"]
                        )
                    )
                    session.execute(stmt_pdl)
            
            session.commit()
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        if session:
            session.close()

def update_customer_rep(store_id):
    customers = []
    api_data = store_util.get_salesforce_api_data(store_id)
    if api_data:
        res = fetch_all_accounts(store_id, api_data)
        if res and 'data' in res:
            for customer in res['data']:
                #print(customer)
                try:
                    id = customer.get('BC_CustomerId__c', None)
                    if id:
                        id = int(id)
                        customer_type = customer.get('Customer_Type__c', None)
                        customer_rep = customer.get('Sales_Rep__r', None)
                        Block_Credit_Card_Payment = customer.get('Block_Credit_Card_Payment__c', False)
                        store_img = customer.get('Store__c', None)
                        cbd_customer_id = customer.get('CBD_Store_Customer_ID__c', None)
                        payment_term = customer.get('Payment_Term__c', None)
                        credit_limit = customer.get('Credit_Limit__c', None)
                        price_list = customer.get('Pricelist__c', None)
                        rep = None
                        if customer_rep:
                            rep = {
                                'Name': customer_rep['Name'],
                                'Email': customer_rep['User__r']['Email'],
                                'Phone': customer_rep['User__r']['Phone'],
                                'MobilePhone': customer_rep['User__r']['MobilePhone'],
                                'Type': customer_rep['Type__c']
                            }
                        licenses = []
                        file_approve_rejection = customer.get('File_Approve_Rejection__r', None)
                        if file_approve_rejection:
                            records = file_approve_rejection.get("records", None)
                            if records:
                                for record in records:
                                    license_found = False
                                    for license_entry in licenses:
                                        if license_entry["License"] == record["License__c"]:
                                            # Replace the existing record
                                            license_entry.update({
                                                "Name": record["Name"],
                                                "License": record["License__c"],
                                                "LicensePermitExhibitNumberExpiry": record["License_Permit_Exhibit_Number_Expiry__c"],
                                                "ExpirationDate": record["Expiration_Date__c"]
                                            })
                                            license_found = True
                                            break
                                    if not license_found:
                                        # Add new record if it doesn't already exist
                                        licenses.append({
                                            "Name": record["Name"],
                                            "License": record["License__c"],
                                            "LicensePermitExhibitNumberExpiry": record["License_Permit_Exhibit_Number_Expiry__c"],
                                            "ExpirationDate": record["Expiration_Date__c"]
                                        })

                        data = {
                            '_id': id,
                            'BCId': id,
                            "Type": customer_type,
                            'SFId': customer['Id'],
                            'Name': customer['Name'],
                            'rep': rep,
                            'is_credit_card_payment_block': Block_Credit_Card_Payment,
                            'Licenses': licenses,
                            'CBDCustomerId': cbd_customer_id,
                            'StoreImg':store_img,
                            'PaymentTerm': payment_terms_mapping.get(payment_term, payment_term),
                            'CreditLimit': credit_limit,
                            'PriceList': price_list
                        }
                        customers.append(data)
                        
                except Exception as e:
                    logger.error("salesforce update_customer_rep: Exception caught: " + traceback.format_exc())
                    logger.error(customer)
        
        
        if len(customers) > 0:
            update_customer_rep_table(store_id, customers)
            update_purchase_orders_table(store_id, customers)
            redis_util.update_salesforce_customers(store_id, customers)
            store = store_util.get_store_by_id(store_id)
            task.upsert_documents(store, salesforce_db.CUSTOMER_REPRESENTATIVE_COLLECTION, customers)
            task.submit_task('refresh_all_order_audits', (store_id,))

    return True



def get_customer_rep(store_id, customer_id):
    result = {}
    customer = redis_util.get_salesforce_customer(store_id, customer_id)
    if customer:
        rep = customer['rep']
        if rep:            
            result['name'] = rep['Name']
            result['email'] = rep['Email']           
    return result

def delete_non_consignment_orders(store_id):
    db_conn = pg_db.get_connection(store_id)
    try:
        api_data = store_util.get_salesforce_api_data(store_id)        
        start_date = (datetime.utcnow() - timedelta(hours=6)).strftime("%Y-%m-%dT%H:%M:%SZ")
        query = f"SELECT+Id,Name,Order__r.ID,Order_r.BC_OrderIdc,Productc,Quantityc,Product_IDc+FROM+Product_Familyc+WHERE+Order_Terms_c=''+and+LastModifiedDate>{start_date}"
        res = fetch_all_records(store_id, api_data, query) 
        if res and 'data' in res:
            for order in res['data']:
                consignment_id = order['Id']
                order_id = int(order['Order_r']['BC_OrderId_c'])
                product_id = order['Product_ID__c']    

                delete_sql = f"DELETE FROM {pg_db.order_consignment} WHERE consignment_id = {consignment_id} AND product_id = {product_id} AND order_id = {order_id}"  
                db_conn.execute(text(delete_sql))                  
                db_conn.commit()  
    except Exception as ex:
        logger.error(str(traceback.format_exc()))
    finally:
        db_conn.commit()
        db_conn.close()     

def update_consignment_table(store_id, order_id, product_id, consignment_id):
    db_conn = pg_db.get_connection(store_id)
    store = store_util.get_store_by_id(store_id)
    try:
        query = f"""
                    SELECT oli.order_id, oli.order_line_item_id, oli.product_id, oli.variant_id, oli.quantity, o.customer_id,
                    c.first_name, c.last_name, c.email from order_line_items oli 
                    JOIN orders o ON o.order_id = oli.order_id
                    JOIN customers c ON c.customer_id = o.customer_id
                    WHERE oli.order_id = {order_id} and oli.product_id = {product_id} ;
                """  
        query_result = db_conn.execute(text(query))

        for item in query_result:                     
            option_values = {} 
            orderID = item[0]
            productID = item[2]                   
            variant_id = item[3]
            customer_id = item[5]
            customer_name = item[6] + ' ' + item[7]
            customer_email = item[8]
            order_line_item_id = item[1]
            quantity = item[4]
            current_date = datetime.now(timezone.utc)           

            #get variant information from bc
            variant_data = bc_products.get_variant_details(store, product_id, variant_id)                                        
            if variant_data:
                for item in variant_data['option_values']:
                    option_values[item["option_display_name"]] = item["label"]
            json_option_values = json.dumps(option_values)  
            variant_sku = variant_data.get('sku', '')                  
        
            #get customer rep details
            customer_rep = get_customer_rep(store_id, customer_id)   
            customer_rep_name = customer_rep.get('name', '') 
            customer_rep_email = customer_rep.get('email', '')                
            
            #delete row if exists
            delete_sql = f"DELETE FROM {pg_db.order_consignment} WHERE order_id = {orderID} AND product_id = {productID} AND customer_id = {customer_id} AND order_lineitem_id = {order_line_item_id}"  
            db_conn.execute(text(delete_sql))                  
            db_conn.commit()

            #insert new row 
            insert_sql = f"""
                INSERT INTO {pg_db.order_consignment} 
                (consignment_id, order_id, customer_id, customer_name, customer_email, order_lineitem_id, product_id, parent_sku, variant_id, variant_sku, options, order_type, quantity, rep_name, rep_user_id, created_at, created_by, modified_at, modified_by) 
                VALUES (:consignment_id, :order_id, :customer_id, :customer_name, :customer_email, :order_lineitem_id, :product_id, :parent_sku, :variant_id, :variant_sku, :options, :order_type, :quantity, :rep_name, :rep_user_id, :created_at, :created_by, :modified_at, :modified_by)
            """
            db_conn.execute(
                text(insert_sql),
                {
                    'consignment_id': consignment_id, 'order_id': orderID, 'customer_id': customer_id, 'customer_name': customer_name, 'customer_email': customer_email,
                    'order_lineitem_id': order_line_item_id, 'product_id': productID, 'parent_sku': None, 'variant_id': variant_id, 'variant_sku': variant_sku,
                    'options': json_option_values, 'order_type': "consignment", 'quantity': quantity, 'rep_name': customer_rep_name,
                    'rep_user_id': customer_rep_email, 'created_at': current_date, 'created_by': '', 'modified_at': current_date, 'modified_by': ''
                }
            )                         
    except Exception as ex:
        logger.error(str(traceback.format_exc()))
    finally:
        db_conn.commit()
        db_conn.close()

def fetch_consignment_orders(store_id):  
    api_data = store_util.get_salesforce_api_data(store_id)        
    start_date = (datetime.now(timezone.utc) - timedelta(hours=1)).strftime("%Y-%m-%dT%H:%M:%SZ")
    query = f"SELECT+Id,Name,Order_Terms__c,Order__r.ID,Order__r.BC_OrderId__c,Product__c,Quantity__c,Product_ID__c+FROM+Product_Family__c+WHERE+Order_Terms__c='Consignment'+and+LastModifiedDate>{start_date}"
    res = fetch_all_records(store_id, api_data, query)        
    if res and 'data' in res:
        for order in res['data']:
            order_id = int(order['Order__r']['BC_OrderId__c'])
            product_id = order['Product_ID__c'] 
            consignment_id = order['Id']  
            update_consignment_table(store_id, order_id, product_id, consignment_id)
                       

def update_data(store_id):
    update_customer_rep(store_id)
    fetch_consignment_orders(store_id)
    delete_non_consignment_orders(store_id)

# fetch the orders details from the salesforce and store it
def update_salesforce_orders(store_id):
    orders = []
    api_data = store_util.get_salesforce_api_data(store_id)
    if api_data:
        res = fetch_all_orders(store_id, api_data)
        if res and 'data' in res:
            for order in res['data']:
                try:
                    # Extract numeric order ID using regex
                    match = re.search(r'>(\d+)<', order['BC_Order_Id__c'])
                    order_id = int(match.group(1)) if match else None
                    if order_id:
                        customer_id = order.get('BC_Customer_Id__c', None)
                        xero_amount_paid = order.get('Xero_Amount_Paid__c', 0)
                        xero_amount_due = order.get('Xero_Amount_Due__c', 0)
                        xero_total = order.get("Xero_Total__c", 0)
                        order_total_inc_tax = order.get("Total_Inc_Tax__c", 0)

                        data = {
                            'order_id': order_id,
                            'customer_id': int(customer_id) if customer_id else None,
                            'amount_due': xero_amount_due if xero_amount_due else 0,
                            'amount_paid': xero_amount_paid if xero_amount_paid else 0,
                            'xero_order_total': xero_total if xero_total else 0,
                            'order_total_inc_tax': order_total_inc_tax
                        }
                        orders.append(data)
                        
                except Exception as e:
                    logger.error("salesforce update_salesforce_orders_table: Exception caught: " + traceback.format_exc())
                    logger.error(order)
        
        
        if len(orders) > 0:
            update_salesforce_order_details_table(store_id, orders)

    return True

def build_salesforce_order_details_model(order):
    order_id = order.get("order_id", None)
    if not order_id:
        return None
    customer_id = order.get("customer_id", None)
    xero_amount_paid = order.get("amount_paid", 0)
    xero_amount_due = order.get("amount_due", 0)
    xero_order_total = order.get("xero_order_total", 0)
    order_total_inc_tax = order.get("order_total_inc_tax", 0)

    return {
        "order_id": order_id,
        "customer_id": customer_id,
        "amount_due": xero_amount_due,
        "amount_paid": xero_amount_paid,
        "order_total_inc_tax": order_total_inc_tax,
        "xero_order_total": xero_order_total,
    }

def update_salesforce_order_details_table(store_id, orders):
    session = pg_db.get_session(store_id)
    try:
        with session.begin():
            for order in orders:
                model = build_salesforce_order_details_model(order)
                session.merge(SalesforceOrderDetails(**model))
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        if session:
            session.commit()
            session.close()

def fetch_all_orders(store_id, api_data):
    # start_date = (datetime.now(timezone.utc) - timedelta(days=730)).strftime("%Y-%m-%dT%H:%M:%SZ")
    start_date = datetime(2023, 1, 1, tzinfo=timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
    query = f"SELECT+BC_Order_Id__c,BC_Customer_Id__c,Xero_Amount_Paid__c,Xero_Amount_Due__c,Xero_Total__c,Total_Inc_Tax__c+FROM+Order+WHERE+BC_Created_Date__c>{start_date}+and+BC_OrderStatus__c+IN+('Awaiting Fulfillment', 'Awaiting Pickup', 'Completed', 'Partially Refunded', 'Partially Shipped', 'Shipped')+and+Xero_Amount_Due__c>0"
    res = fetch_all_records(store_id, api_data, query)
    return res

def clear_older_salesforce_orders(store_id):
    orders_to_delete = []  # Store (order_id, customer_id) tuples
    conn = None
    try:
        conn = pg_db.get_connection(store_id)
        api_data = store_util.get_salesforce_api_data(store_id)
        if api_data:
            start_date = datetime(2023, 1, 1, tzinfo=timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
            query = f"SELECT+BC_Order_Id__c,BC_Customer_Id__c+FROM+Order+WHERE+BC_Created_Date__c>{start_date}+and+BC_OrderStatus__c+NOT+IN+('Awaiting Fulfillment', 'Awaiting Pickup', 'Completed', 'Partially Refunded', 'Partially Shipped', 'Shipped')+and+Xero_Amount_Due__c=0"
            res = fetch_all_records(store_id, api_data, query)
            if res and 'data' in res:
                for order in res['data']:
                    try:
                        # Extract order ID safely
                        bc_order_id = order.get('BC_Order_Id__c')
                        bc_customer_id = order.get('BC_Customer_Id__c')

                        if bc_order_id and bc_customer_id:
                            # Check if order ID is numeric
                            if bc_order_id.isdigit():
                                order_id = int(bc_order_id)
                            else:
                                match = re.search(r'>(\d+)<', bc_order_id)
                                order_id = int(match.group(1)) if match else None

                            # Only append if both values are valid
                            if order_id and bc_customer_id:
                                orders_to_delete.append((order_id, bc_customer_id))

                    except Exception:
                        logger.error("salesforce clear_older_salesforce_orders: Error processing order: " + traceback.format_exc())
                        logger.error(f"Problematic order data: {order}")

            if orders_to_delete:
                    query_delete = text("""DELETE FROM salesforce_order_details WHERE (order_id, customer_id) IN :order_customer_pairs""")
                    conn.execute(query_delete, {"order_customer_pairs": tuple(orders_to_delete)})
                    conn.commit()
                    logger.info(f"Deleted {len(orders_to_delete)} records from salesforce_order_details for store {store_id}")
            else:
                logger.info(f"No valid orders found for deletion in store {store_id}")
    except Exception:
        logger.error("salesforce clear_older_salesforce_orders: Deletion error: " + traceback.format_exc())
        if conn:
            conn.rollback() 
    finally:
        if conn:
            conn.close()