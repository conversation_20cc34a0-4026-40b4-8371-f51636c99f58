import logging
import mongo_db
from utils import get_current_timestamp
from pymongo import TEXT
import traceback

logger = logging.getLogger()

PRODUCTS_COLLECTION = "products"
BRANDS_COLLECTION = "brands"
CATEGORY_COLLECTION = "categories"
CATEGORY_TREE_COLLECTION = "category_tree"
WEB_PAGES_COLLECTION="web_pages"
COMPLEX_RULES_COLLECTION = "products_complex_rules"
SKUVAULT_POS_COLLECTION = "skuvault_pos"
SKUVAULT_RECEIVED_POS_COLLECTION = "skuvault_received_pos"
SKUVAULT_COMPLETED_POS_COLLECTION = "skuvault_completed_pos"

def fetch_product_last_modified_date(store):
    last_modified_date = None
    db = mongo_db.get_store_db_client(store)
    cur = db[PRODUCTS_COLLECTION].find({},{"date_modified":1}).sort([('date_modified', -1)]).limit(1)  
    for r in cur:
        last_modified_date = r['date_modified']
    return last_modified_date

def create_index(store):
    try:
        db = mongo_db.get_store_db_client(store)        
        index = db[PRODUCTS_COLLECTION].create_index([('product_search_field', TEXT)], default_language='english')
    except Exception as ex:
        logger.error(traceback.format_exc())

def fetch_all_product_inventory(store):
    skus = {}
    db = mongo_db.get_store_db_client(store)
    cur = db[PRODUCTS_COLLECTION].find({})
    product_count = 0
    sku_count = 0

    for row in cur:
        sku = row['sku']
        inventory = row['inventory_level']
        skus[sku] = inventory
        product_count = product_count + 1
        variants = row['variants']
        for variant in variants:
            sku = variant['sku']
            inventory = variant['inventory_level']
            skus[sku] = inventory
            sku_count = sku_count + 1
    return skus

def fetch_all_brands(store):
    db = mongo_db.get_store_db_client(store)
    cur = db[BRANDS_COLLECTION].find({})
    brands = []
    for row in cur:
        brands.append(mongo_db.process_data(row))

    # if len(brands):
    #     try:
    #         res = db[BRANDS_COLLECTION].create_index([('name', TEXT)], default_language='english')
    #     except Exception as ex:
    #         logger.error(traceback.format_exc())

    return brands

def update_product_inventory(store, product_id, new_inventory):
    db = mongo_db.get_store_db_client(store)
    result = db[PRODUCTS_COLLECTION].update_one({"_id": int(product_id)},
                        { "$set": { 'inventory_level': new_inventory } })
    return result.acknowledged and result.raw_result['updatedExisting']
    
def update_sku_inventory(store, product_id, variant_id, new_inventory):
    db = mongo_db.get_store_db_client(store)
    result = db[PRODUCTS_COLLECTION].update_one({"_id": product_id},
                {"$set":{"variants.$[element].inventory_level": new_inventory}},
                array_filters=[{"element.id": variant_id}])
    return result.acknowledged and result.raw_result['updatedExisting']

def remove_product_by_id(store, product_id):
    db = mongo_db.get_store_db_client(store)
    doc = db[PRODUCTS_COLLECTION].delete_one({'_id': int(product_id)})

def fetch_product_by_id(store, product_id):
    db = mongo_db.get_store_db_client(store)
    product = db[PRODUCTS_COLLECTION].find_one({"_id": int(product_id)})
    return product

def get_products(store, query={}, fields={}):    
    db = mongo_db.get_store_db_client(store)
    cur = db[PRODUCTS_COLLECTION].find(query, fields)   
    products = []  
    for row in cur:
        products.append(mongo_db.process_data(row))   
    return products 

def update_product_complex_rule(store, data):   
    try:     
        rule_id = data['id']
        produt_id = data['product_id']        
        data[mongo_db.UpdateKeys.UPDATED_AT] = get_current_timestamp()
        data[mongo_db.UpdateKeys.UPDATED] = True
        db = mongo_db.get_store_db_client(store)
        db[COMPLEX_RULES_COLLECTION].update_one({"product_id": int(produt_id), "id": int(rule_id)}, { "$set": data }, upsert=True)
    except Exception as e:
        logger.error(str(traceback.format_exc()))

def clear_product_categories_updated_field(store):  
    mongo_db.clear_updated_field(store, CATEGORY_COLLECTION) 

def clear_product_brands_updated_field(store):  
    mongo_db.clear_updated_field(store, BRANDS_COLLECTION) 

def clear_product_complex_rule_updated_field(store):  
    mongo_db.clear_updated_field(store, COMPLEX_RULES_COLLECTION)  

def clear_products_updated_field(store):  
    mongo_db.clear_updated_field(store, PRODUCTS_COLLECTION)