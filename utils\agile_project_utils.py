from sqlalchemy.exc import IntegrityError
from psycopg2.errors import UniqueViolation
from sqlalchemy import text
from datetime import datetime
import pg_db
import re
import traceback
import task
import logging
from utils import admin_app_notification_util
from utils import project_notification_util
import time

logger = logging.getLogger()

def _fetch_project_detail(conn, id, username):    
    query = text (f"""SELECT p.id, p.name, p.bu_id, p.description, p.owner_username, p.due_date, p.is_archived, p.updated_by, p.created_at, p.updated_at, p.project_initials, p.project_type, pptm.pipeline_db_tables_id, pptm.ticket_name, pptm.default_assignee, pptm.db_table_column, pdt.table_name, pdt.title FROM agile_projects p LEFT JOIN pipeline_project_table_mapping pptm ON p.id = pptm.project_id LEFT JOIN  pipeline_db_tables pdt ON pptm.pipeline_db_tables_id = pdt.id WHERE p.id = :id;""")

    query = query.params(id=id)
    result = conn.execute(query).fetchone()

    column_query = text (f"""SELECT id FROM agile_project_columns pc WHERE is_resolved = true AND project_id = :id;""")
    query = column_query.params(id=id)
    column_result = conn.execute(query).fetchone()

    data = []
    if result:         
        row_data = {
            'id': result[0],
            'name': result[1],
            'bu_id': result[2],            
            'description': result[3],
            'owner_username': result[4],
            'due_date': result[5],
            'is_archived': result[6],
            'updated_by': result[7],
            # 'created_date': convert_to_timestamp(result[8]),
            # 'modified_at': convert_to_timestamp(result[9]),
            'project_initials': result[10],
            'is_owner': True if username == result[4] else False,
            'status': 'active' if not result[6] else 'archived',
            'resolved_column_id': column_result[0] if column_result else None
        }
        mapping = {
            'project_type': result[11] if result[11] else 'standard',
            'table_id': result[12],
            'ticket_name': result[13],
            'default_assignee': result[14],
            'column_key': result[15],
            'db_table_name': result[16],
            'title': result[17]
        }
        row_data['project_mapping'] = mapping
        data.append(row_data)
    return data


def create_card(store_id, payload, username, project_id, module_id):
    response = {
        'status': 400
    }
    conn = pg_db.get_connection(store_id)
    try:
        time.sleep(3)
        current_column_id = int(payload.get('current_column_id', 0))
        status = int(payload.get('status', 0)) if payload['status'] != 0 else None
        priority = int(payload.get('priority', 0)) if payload['priority'] != 0 else None
        title = payload.get('title', '')
        description = payload.get('description', '')
        assigned_to = payload.get('assigned_to', '')

        spent_time_value = payload.get('spent_time', '0h')
        spent_time_hours = parse_time(spent_time_value) 
        spent_time = f"{spent_time_hours} hour"

        estimation_value = payload.get('estimation', '0h')
        estimation_time_hours = parse_time(estimation_value)
        estimation = f"{estimation_time_hours} hour"

        # due_date_str = payload.get('due_date', None)
        # due_date = datetime.strptime(due_date_str, '%Y-%m-%d') if due_date_str else None
        due_date = payload.get('due_date', None)

        # start_date_str = payload.get('start_date', None)
        # start_date = datetime.strptime(start_date_str, '%Y-%m-%d') if start_date_str else None
        start_date = payload.get('start_date', None)

        is_archived = payload.get('is_archived', False)

        parent_card_id = payload.get('parent_card_id', None)
        child_card_id = payload.get('child_card_id', None)

        card_index_query = conn.execute(
            text(f"SELECT card_index FROM agile_project_cardindex WHERE project_id = :project_id"),
            {'project_id': project_id}
        )
        card_index = card_index_query.fetchone()[0]
        logger.info(f"card_index: {card_index}")

        project_detail = _fetch_project_detail(conn, project_id, username)

        project_initials = project_detail[0]['project_initials']
        # project_initials = ''.join(word[0].upper() for word in project_name.split())

        new_card_index = card_index + 1

        card_identifier = f"{project_initials}-{new_card_index}"

        # Fetch the highest sort_id for the given project
        sort_id_query = conn.execute(
            text(f"SELECT MAX(sort_id) FROM agile_project_cards WHERE project_id = :project_id AND module_id = :module_id AND current_column_id = :current_column_id"),
            {'project_id': project_id, 'module_id': module_id, 'current_column_id': current_column_id}
        )
        highest_sort_id = sort_id_query.scalar()
        new_sort_id = highest_sort_id + 1 if highest_sort_id is not None else 1

        query = text(
        f"""INSERT INTO agile_project_cards (project_id, module_id, current_column_id, status, priority, title, card_identifier, description, assigned_to, spent_time, estimation, due_date, is_archived, created_by, updated_by, created_at, updated_at, sort_id, start_date, parent_card_id)
            VALUES (:project_id, :module_id, :current_column_id, :status, :priority, :title, :card_identifier, :description, :assigned_to, :spent_time, :estimation, :due_date, :is_archived, :created_by, :updated_by, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, :sort_id, :start_date, :parent_card_id) 
            RETURNING id;
        """
         )
        query = query.params(project_id=project_id, module_id=module_id, current_column_id=current_column_id, status=status, priority=priority, title=title, card_identifier=card_identifier, description=description, assigned_to=assigned_to, spent_time=spent_time, estimation=estimation, due_date=due_date, is_archived=is_archived, created_by=username, updated_by=username, sort_id=new_sort_id, start_date=start_date, parent_card_id=parent_card_id)
        result = conn.execute(query)
        card_id = result.fetchone()[0]

        if result.rowcount > 0:
            if child_card_id:
                message, falg = update_card_hierarchy(conn, card_id, child_card_id)
             # Update the card_index in the agile_project_cardindex table
            conn.execute(
                text(f"UPDATE agile_project_cardindex SET card_index = :new_card_index WHERE project_id = :project_id"),
                {'new_card_index': new_card_index, 'project_id': project_id}
            )
            conn.commit()

        response['status'] = 200
        response['message'] = 'Card created successfully.'

        if card_id:
            time.sleep(2)
            task.submit_task('generate_admin_app_notification', (store_id, admin_app_notification_util.AdminAppNotificationUtil.TICKET_CREATED, card_id))
            task.submit_task('send_project_notification', (store_id, project_notification_util.ProjectNotifications.TICKET_CREATED, card_id))
    except IntegrityError as e:
        logger.error(str(traceback.format_exc()))  
        if isinstance(e.orig, UniqueViolation):
            response['status'] = 409
            response['message'] = "title: Card with the same id or title already exists."
        else:          
            response['status'] = 500
            response['message'] = "Something went wrong."   
    finally:
        conn.commit()
        conn.close()
    return response 


def parse_time(time_str):
    total_hours = 0

    # Regular expression to extract time components (e.g., "2w", "3d", "4h", "30m")
    time_pattern = re.findall(r'(\d+)([wdhm])', time_str)

    for value, unit in time_pattern:
        value = int(value)  # Convert the numeric part to an integer
        if unit == 'w':
            total_hours += value * 5 * 8  # 1 week = 5 days, 1 day = 8 hours
        elif unit == 'd':
            total_hours += value * 8  # 1 day = 8 hours
        elif unit == 'h':
            total_hours += value  # Add hours directly
        elif unit == 'm':
            total_hours += value / 60  # Convert minutes to hours
        else:
            raise ValueError(f"Invalid time unit: {unit}")

    return total_hours

def update_card_hierarchy(conn, p_card_id, card_id):
    """
    :param conn: Database connection object.
    :param p_card_id: Parent card ID to set.
    :param card_id: The current card ID to process.
    """

    if not p_card_id or not card_id:
        return "Parent card ID and Card ID cannot be None or empty.", False

    # **Check 1: Circular reference (A card cannot be its own parent)**
    if p_card_id == card_id:
        return "Circular reference detected: A card cannot be its own parent.", False

    # **Check 2: Prevent Infinite Loops (Ensure parent is not a descendant)**
    def is_descendant(child_id, parent_id):
        """Checks if the parent_id is in the child's hierarchy (to avoid loops)."""
        stack = [child_id]
        while stack:
            current_card_id = stack.pop()
            res = conn.execute(
                text(f"SELECT parent_card_id FROM agile_project_cards WHERE id = :card_id"),
                {'card_id': current_card_id}
            )
            parent_card = res.fetchone()
            if parent_card:
                parent_card_id = parent_card[0]
                if parent_card_id == parent_id:
                    return True  # Cycle detected
                if parent_card_id:
                    stack.append(parent_card_id)
        return False

    if is_descendant(p_card_id, card_id):
        return "Hierarchy cycle detected: The parent card is a descendant of the child.", False

    # **Check 3: Duplicate Relationship (Check if card is already linked)**
    existing_link = conn.execute(
        text(f"SELECT id FROM agile_project_cards WHERE id = :card_id AND parent_card_id = :p_card_id"),
        {'card_id': card_id, 'p_card_id': p_card_id}
    ).fetchone()

    if existing_link:
        return "Duplicate entry detected: This parent-child relationship already exists.", False

    # **Update parent_card_id**
    conn.execute(
        text(f"""
            UPDATE agile_project_cards
            SET parent_card_id = :p_card_id WHERE id = :card_id
        """),
        {'p_card_id': p_card_id, 'card_id': card_id}
    )

    conn.commit()  # Commit changes after update
    return "Updated successfully.", True