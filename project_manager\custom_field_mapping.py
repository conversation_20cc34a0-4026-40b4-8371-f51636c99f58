import pg_db
import logging
import traceback
import project_manager
from sqlalchemy import text

logger = logging.getLogger()

def update_record_using_custom_field(store_id, payload):
    db_conn = pg_db.get_connection(store_id)
    try:
        ticket_id = payload.get('ticket_id')
        project_id = payload.get('project_id')
        custom_field_id = payload.get('custom_field_id')
        custom_field_value = payload.get('custom_field_value')
        updated_by = payload.get('updated_by')

        if not ticket_id or not project_id or not custom_field_id:
            raise Exception('Invalid request')
        
        db_table_name = ''
        pipline_project_table_mapping_id = 0
        record_id = 0
        column_name = ''
        query = text (f"""SELECT pptm.id, pptm.project_id, pdt.table_name, pdt.title, pdt.mapping_column, pdt.related_table_1, pdt.mapping_column_1, pdt.related_table_2, pdt.mapping_column_2, pdt.related_table_3, pdt.mapping_column_3 FROM pipeline_project_table_mapping pptm JOIN pipeline_db_tables pdt ON pdt.id = pptm.pipeline_db_tables_id WHERE pptm.project_id = :project_id;""")
        query = query.params(project_id=project_id)
        table_mapping_data = db_conn.execute(query).fetchone()   
        related_tables = []
        mapping_columns = []
        main_mapping_column = ''
        if table_mapping_data:
            db_table_name = table_mapping_data[2]
            main_mapping_column = table_mapping_data[4]
            pipline_project_table_mapping_id = table_mapping_data[0]
            related_tables = [table_mapping_data[idx] for idx in (5, 7, 9) if table_mapping_data[idx]]
            mapping_columns = [table_mapping_data[idx] for idx in (6, 8, 10) if table_mapping_data[idx]]

            query = text (f"""SELECT id, pipeline_record_id, table_name from agile_project_cards where id = :id and table_name = :db_table_name;""")
            query = query.params(id=ticket_id, db_table_name=db_table_name)
            ticket_data = db_conn.execute(query).fetchone()   
            if ticket_data:
                record_id = ticket_data[1]

            query = text (f"""SELECT db_table_column from pipeline_custom_field_mapping where project_id = :project_id and project_customfield_id = :custom_field_id and project_table_mapping_id = :pipline_project_table_mapping_id;""")
            query = query.params(project_id=project_id, custom_field_id=custom_field_id, pipline_project_table_mapping_id=pipline_project_table_mapping_id)
            custom_field_mapping = db_conn.execute(query).fetchone()   
            if custom_field_mapping:
                column_name = custom_field_mapping[0]

            if record_id and column_name != '' and db_table_name != '':
                column_name_array = []
                comman_column_name = main_mapping_column
                if "." in column_name:
                    column_name_array = column_name.split(".")
                    if len(column_name_array):
                        db_table_name = column_name_array[0]
                        column_name = column_name_array[1]
                        if db_table_name in related_tables:
                            related_table_index = related_tables.index(db_table_name)
                            comman_column_name = mapping_columns[related_table_index]

                if db_table_name == 'replenishment_dashboard' and column_name == 'classified_as':
                    classified_as_query = text(f"""SELECT id FROM replenishment_classified_as WHERE name = :custom_field_value;""")
                    classified_as_query = classified_as_query.params(custom_field_value=custom_field_value)
                    classified_as_data = db_conn.execute(classified_as_query).fetchone()
                    if classified_as_data:
                        classified_as_id = classified_as_data[0]
                        update_query = text(f"""UPDATE replenishment_dashboard SET classified_as_id = :classified_as_id WHERE parent_sku = :record_id;""")
                        update_query = update_query.params(classified_as_id=classified_as_id, record_id=record_id)
                        db_conn.execute(update_query)
                        db_conn.commit()

                query = text (f"""UPDATE {db_table_name} SET {column_name} = :custom_field_value WHERE {comman_column_name} = :record_id;""")
                query = query.params(custom_field_value=custom_field_value, record_id=record_id)
                result = db_conn.execute(query)

    except Exception as e:
        logger.error(traceback.format_exc())
        raise e
    finally:
        if db_conn:
            db_conn.commit()
            db_conn.close()

def update_custom_field_using_resource(store_id, payload):
    db_conn = pg_db.get_connection(store_id)
    try:
        resource_id = payload.get('resource_id')
        table_name = payload.get('table_name')
        custom_field_value = payload.get('custom_field_value')
        column_name = payload.get('column_name')
        updated_by = payload.get('updated_by')
        default_assignee = payload.get('assignee', '')
        if not resource_id or not column_name or not table_name:
            raise Exception('Invalid request')
        
        project_id = 0
        ticket_id = 0
        project_customfield_id = 0
        project_table_mapping_id = 0
        # Fetch the resource details from the agile_project_cards table
        query = text (f"""SELECT id, project_id, title, pipeline_record_id, table_name from agile_project_cards where pipeline_record_id = :resource_id and table_name = :table_name;""")
        query = query.params(resource_id=resource_id, table_name=table_name)
        ticket_details = db_conn.execute(query).fetchone()   

        # create or update custom field value if ticket exists otherwise create ticket and create or update custom field value
        if ticket_details:
            ticket_id = ticket_details[0]
            project_id = ticket_details[1]
            # create_or_update_custom_field_value(db_conn, ticket_id, project_id, custom_field_value, column_name, updated_by)
            query = text (f"""SELECT pcm.project_customfield_id, pptm.id
                        FROM pipeline_custom_field_mapping pcm
                        JOIN pipeline_project_table_mapping pptm ON pcm.project_id = pptm.project_id
                        WHERE pcm.project_id = :project_id AND pcm.db_table_column = :column_name;""")
            query = query.params(project_id=int(project_id), column_name=column_name)
            mapping_data = db_conn.execute(query).fetchone()
            if mapping_data:
                project_customfield_id = mapping_data[0]
                project_table_mapping_id = mapping_data[1]
            
            if ticket_id and project_customfield_id:
                query = text(f"""INSERT INTO agile_customfield_value (project_customfield_id, card_id, project_id, str_value, number_value, created_by, updated_by)
                                VALUES (:project_customfield_id, :ticket_id, :project_id, :custom_field_value, NULL, :updated_by, :updated_by)
                                ON CONFLICT (card_id, project_customfield_id)
                                DO UPDATE SET str_value = EXCLUDED.str_value, updated_by = EXCLUDED.updated_by, updated_at = CURRENT_TIMESTAMP;""")
                query = query.params(custom_field_value=custom_field_value, updated_by=updated_by, ticket_id=ticket_id, project_customfield_id=project_customfield_id, project_id=project_id)
                db_conn.execute(query)
        else:
            query = text("""SELECT id, project_id, pipeline_db_tables_id, ticket_name, default_assignee, db_table_column from pipeline_project_table_mapping where pipeline_db_tables_id = (SELECT id from pipeline_db_tables where table_name = :table_name)""")
            query = query.params(table_name=table_name)
            mapping_data = db_conn.execute(query).fetchall()
            if not mapping_data:
                logger.info(f"No pipeline project table mapping found for table_name: {table_name}")
                return
            
            for row in mapping_data:
                mapping_details = {
                    "id": row[0],
                    "project_id": row[1],
                    "pipeline_db_tables_id": row[2],
                    "ticket_name": row[3],
                    "default_assignee": row[4],
                    "db_table_column": row[5]
                }
                payload = {}
                payload['table_name'] = table_name
                payload['resource_id'] = resource_id
                payload['title'] = resource_id
                payload['description'] = ''
                payload['assigned_to'] = default_assignee if default_assignee and default_assignee != '' else updated_by
                payload['mapping_details'] = mapping_details
                
                # create ticket for the resource
                res = project_manager.create_ticket(store_id, payload, True)

                if res and 'card_id' in res:
                    ticket_id = res['card_id']
                    # Fetch the resource details from the agile_project_cards table
                    query = text (f"""SELECT id, project_id, title, pipeline_record_id, table_name from agile_project_cards where id = :ticket_id and table_name = :table_name;""")
                    query = query.params(ticket_id=ticket_id, table_name=table_name)
                    ticket_details = db_conn.execute(query).fetchone() 
                    if ticket_details:
                        project_id = ticket_details[1]
                        # create_or_update_custom_field_value(db_conn, ticket_id, project_id, column_name, custom_field_value, updated_by)
                        query = text (f"""SELECT pcm.project_customfield_id, pptm.id
                            FROM pipeline_custom_field_mapping pcm
                            JOIN pipeline_project_table_mapping pptm ON pcm.project_id = pptm.project_id
                            WHERE pcm.project_id = :project_id AND pcm.db_table_column = :column_name;""")
                        query = query.params(project_id=int(project_id), column_name=column_name)
                        mapping_data = db_conn.execute(query).fetchone()
                        if mapping_data:
                            project_customfield_id = mapping_data[0]
                            project_table_mapping_id = mapping_data[1]
                        
                        if ticket_id and project_customfield_id:
                            query = text(f"""INSERT INTO agile_customfield_value (project_customfield_id, card_id, project_id, str_value, number_value, created_by, updated_by)
                                            VALUES (:project_customfield_id, :ticket_id, :project_id, :custom_field_value, NULL, :updated_by, :updated_by)
                                            ON CONFLICT (card_id, project_customfield_id)
                                            DO UPDATE SET str_value = EXCLUDED.str_value, updated_by = EXCLUDED.updated_by, updated_at = CURRENT_TIMESTAMP;""")
                            query = query.params(custom_field_value=custom_field_value, updated_by=updated_by, ticket_id=ticket_id, project_customfield_id=project_customfield_id, project_id=project_id)
                            db_conn.execute(query)

    except Exception as e:
        logger.error(traceback.format_exc())
        raise e
    finally:
        if db_conn:
            db_conn.commit()
            db_conn.close()

def create_or_update_custom_field_value(db_conn, ticket_id, project_id, column_name, custom_field_value, updated_by):
    project_customfield_id = 0
    project_table_mapping_id = 0
    query = text (f"""SELECT pcm.project_customfield_id, pptm.id
                        FROM pipeline_custom_field_mapping pcm
                        JOIN pipeline_project_table_mapping pptm ON pcm.project_id = pptm.project_id
                        WHERE pcm.project_id = :project_id AND pcm.db_table_column = :column_name;""")
    
    query = query.params(project_id=int(project_id), column_name=column_name)
    mapping_data = db_conn.execute(query).fetchone()
    if mapping_data:
        project_customfield_id = mapping_data[0]
        project_table_mapping_id = mapping_data[1]
    
    if ticket_id and project_customfield_id:
        query = text(f"""INSERT INTO agile_customfield_value (project_customfield_id, card_id, project_id, str_value, number_value, created_by, updated_by)
                        VALUES (:project_customfield_id, :ticket_id, :project_id, :custom_field_value, NULL, :updated_by, :updated_by)
                        ON CONFLICT (card_id, project_customfield_id)
                        DO UPDATE SET str_value = EXCLUDED.str_value, updated_by = EXCLUDED.updated_by, updated_at = CURRENT_TIMESTAMP;""")
        query = query.params(custom_field_value=custom_field_value, updated_by=updated_by, ticket_id=ticket_id, project_customfield_id=project_customfield_id, project_id=project_id)
        db_conn.execute(query)