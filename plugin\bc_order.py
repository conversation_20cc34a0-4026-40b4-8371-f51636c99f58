from utils import bc_util, store_util

ORDER_API = "v2/orders"

def fetch_order(store, order_id):
    url = ORDER_API + "/" + str(order_id)
    bc_api = store_util.get_bc_api_creds(store)
    return bc_util.call_api(bc_api, "GET", url)

def fetch_all_order_products(store, resource):
    bc_api = store_util.get_bc_api_creds(store)
    return bc_util.call_api(bc_api, "GET", "v2"+resource)

def fetch_order_coupon(store, url):
    bc_api = store_util.get_bc_api_creds(store)
    return bc_util.call_api(bc_api, "GET", url)

def fetch_latest_order(store):
    bc_api = store_util.get_bc_api_creds(store)
    query_params = {
        "limit": 1,
        "page": 1,
        "sort": "date_created:desc"
    }
    res = bc_util.call_api(api_data=bc_api, method="GET", url=ORDER_API, query_params=query_params)
    order = None
    if res.status_code == 200:
        order = res.json()
        if order and len(order) > 0:
            order = order[0]
    return order

def update_channel_id(store, order_id, channel_id):
    bc_api = store_util.get_bc_api_creds(store)
    request_body = {
        "channel_id": channel_id
    }
    bc_util.call_api(api_data=bc_api, method="PUT", url=ORDER_API + "/" + str(order_id), req_body=request_body)

def get_order_products(store, order_id):
    bc_api = store_util.get_bc_api_creds(store)
    api = f"v2/orders/{order_id}/products"
    return bc_util.call_api(api_data=bc_api, method="GET", url=api)