import pg_db
from pg_db import products_db
import logging
import traceback
from plugin import bc_products
from pg_db_utils import bulk_orders_util
from jinja2 import Environment, FileSystemLoader
from utils.email_util import send_html_email

logger = logging.getLogger()


def execute_rules(store, variants): 
   
    db_conn = pg_db.get_connection(store['id'])
    session = pg_db.get_session(store['id'])   
    try:        
        send_mail_variants_rules = {}
        total_variant_ids = list(variants.keys())
        bulk_variant_ids, bop_id_array = bulk_orders_util.get_all_product_using_variant_ids(db_conn, ', '.join(map(str, variants.keys())))
        filtered_variant_ids = [item for item in total_variant_ids if item in bulk_variant_ids]
        
        rules = products_db.Variants.get_variants(store['id'], filtered_variant_ids, session)   
        
        for rule in rules:
            variant_id = rule.variants_id             
            inventory_level = rule.variants_inventory_level                                       
            variant = variants.get(variant_id, None)                 
            if variant:                   
                if inventory_level == 0 and variant['inventory_level'] > 0:                
                    variant['bop_id'] = bop_id_array[variant_id] 
                    variant['stock'] = variant['inventory_level'] 
                    send_mail_variants_rules[variant_id] = variant                                                                     
                
        if len(send_mail_variants_rules) > 0:
            actions_to_do_on_unhide_product(store, send_mail_variants_rules, db_conn)            

    except Exception as ex:
        logger.error(str(traceback.format_exc()))
    finally:        
        db_conn.commit()
        db_conn.close()  
        if session:
            session.commit()
            session.close()                     
    
    

def actions_to_do_on_unhide_product(store, send_mail_variants_rules, db_conn):
    try:                      
        bop_id_str = ''
        variant_id_str = ''
        variant_stock = {}
        for variant in send_mail_variants_rules.values():            
            bop_id = variant['bop_id']
            variant_id = variant['variant_id']
            variant_stock[variant_id] = variant['stock']
            bop_id_str = bop_id_str + str(bop_id) + ','
            variant_id_str = variant_id_str + str(variant_id) + ','
        
        variant_id_str = variant_id_str.rstrip(',')
        bop_id_str = bop_id_str.rstrip(',') 
        data = bulk_orders_util.get_customer_rep_emails(db_conn, bop_id_str, variant_id_str, variant_stock)
        if data:     
            for email, orders in data.items():
                first_order = next(iter(orders.values()))[0]
                rep_name = first_order['customer_rep_name']
                send_restock_variant_email(email, orders, rep_name)
                
    except Exception as ex:
        logger.error(str(traceback.format_exc()))   

def send_restock_variant_email(to_email, line_items_array, rep_name):
    env = Environment(loader=FileSystemLoader('.'))
    template = env.get_template('email_template.html')
    msg_body = template.render(line_items_array=line_items_array, rep_name=rep_name)
    sender_email = '<EMAIL>'
    subject = 'Bulk Order Restock Notification'
    password='rZ21*g3ZEYrV'
    smtp_server = 'smtp.gmail.com'
    smtp_port = 587
    recipients= [to_email]
    send_html_email(smtp_server, smtp_port, sender_email, password, subject, msg_body, sender_email, recipients)  