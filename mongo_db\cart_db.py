
import logging
from bson import ObjectId
import mongo_db

logger = logging.getLogger()

def fetch_cart_by_bc_cart_id_and_customer_id(store, customer_id, bc_cart_id):
    cart = None
    db = mongo_db.get_store_db_client(store)
    cart = db[mongo_db.CART_COLLECTION].find_one({"customer_id": customer_id, "bc_cart_id": bc_cart_id})
    if cart:
        cart = mongo_db.process_data(cart)
    return cart

def delete_cart(store, cart_id):
    db = mongo_db.get_store_db_client(store)
    db[mongo_db.CART_COLLECTION].delete_many({"_id": ObjectId(str(cart_id))})

def fetch_carts(store, query={}, fields={}):
    cart = None
    db = mongo_db.get_store_db_client(store)
    return db[mongo_db.CART_COLLECTION].find(query, fields)    