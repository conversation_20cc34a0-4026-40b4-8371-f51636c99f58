import datetime
from utils import bc_util, store_util
import task
from mongo_db import pricing_db
import mongo_db


def fetch_price_lists(store):
    url = f'v3/pricelists'
    bc_api = store_util.get_bc_api_creds(store)
    res = bc_util.call_api(bc_api, "GET", url, query_params={}) 

    # success ...   
    if res.status_code == 200:
        return res.json(), 200
    else:
        # unprocess able entity...
        return res.json(), res.status_code
    
def fetch_price_list_by_id(store, price_list_id):
    url = f'v3/pricelists/{price_list_id}'
    bc_api = store_util.get_bc_api_creds(store)
    res = bc_util.call_api(bc_api, "GET", url, query_params={}) 

    # success ...   
    if res.status_code == 200:
        return res.json(), 200
    else:
        # unprocess able entity...
        return res.json(), res.status_code

def fetch_price_list_records(store, price_list_id):
    page = 1
    qp = {
        "page": page,
        "limit": 10000,
        "include": "sku"
    }
    api = "v3/pricelists/" + str(price_list_id) + "/records"
    bc_api = store_util.get_bc_api_creds(store)
    start_time = datetime.datetime.now(datetime.timezone.utc)
    price_list_collection = pricing_db.get_price_list_collection(price_list_id)
    record_count = 0
    while True:
        qp["page"] = page
        res = bc_util.call_api(bc_api, "GET", api, query_params=qp)
        if res.status_code == 200:
            res = res.json()
            data = res["data"]
            meta = res["meta"]["pagination"]
            if meta["total"] == 0:
                break
            documents = []
            for row in data:
                if "sku" in row:
                    row['_id'] = row['sku']
                    documents.append(row)
            if len(documents) > 0:
                mongo_db.upsert_documents(store, price_list_collection, documents)
                record_count = record_count + len(documents)
            if meta['total_pages'] == meta['current_page']:
                break
            page = page + 1
        else:
            break
    if record_count > 0:
        mongo_db.delete_documents_not_updated(store, price_list_collection, start_time)

def fetch_records_by_price_list_id(store, price_list_id):
    page = 1
    qp = {
        "page": page,
        "limit": 10000,
        "include": "sku"
    }
    records = []  # Initialize empty list to hold records
    api = "v3/pricelists/" + str(price_list_id) + "/records"
    bc_api = store_util.get_bc_api_creds(store)
    
    while True:
        qp["page"] = page
        res = bc_util.call_api(bc_api, "GET", api, query_params=qp)
        
        if res.status_code == 200:
            res = res.json()
            data = res["data"]
            meta = res["meta"]["pagination"]
            
            if meta["total"] == 0:
                break
            
            # Directly extend records without checking for 'sku' or setting '_id'
            records.extend(data)
            
            # Break the loop if we're on the last page
            if meta['total_pages'] == meta['current_page']:
                break
            
            page = page + 1
        else:
            break
    
    # Map variant_id to a dict containing price and date_modified
    variant_price_map = {
        record["variant_id"]: {
            "price": record["price"],
            "date_modified": record.get("date_modified", None)  # safely handle missing date_modified
        }
        for record in records
    }
    
    # Return the accumulated records
    return variant_price_map


def fetch_price_list_assignments(store):
    start_time = datetime.datetime.now(datetime.timezone.utc)
    req_body = bc_util.get_bc_api_request_object(url="v3/pricelists/assignments")
    bc_api = store_util.get_bc_api_creds(store)
    price_list_assignments = bc_util.fetch_all(bc_api, req_body)
    price_lists = bc_util.fetch_all(bc_api, bc_util.get_bc_api_request_object(url="v3/pricelists"))
    price_list_dic = {}
    if price_lists:
        for price_list in price_lists:
            price_list_dic[price_list["id"]] = price_list["name"]

    documents = []
    for assignment in price_list_assignments:
        assignment["_id"] = assignment["price_list_id"]
        assignment["name"] = price_list_dic.get(assignment["price_list_id"], None)
        documents.append(assignment)
        task.update_price_list.apply_async(args=[store["id"], assignment["price_list_id"], True])
        
    mongo_db.upsert_documents(store, pricing_db.ASSIGNMENT_COLLECTION, documents)
    if len(documents) > 0:
        mongo_db.delete_documents_not_updated(store, pricing_db.ASSIGNMENT_COLLECTION, start_time)
    return True, {"message": "Completed."}


def fetch_records_by_variant_id(store, price_list_id, variant_ids):
    page = 1
    qp = {
        "page": page,
        "limit": 10000,
        "include": "sku",
        "variant_id:in": [variant_ids]
    }
    records = []  # Initialize empty list to hold records
    api = "v3/pricelists/" + str(price_list_id) + "/records"
    bc_api = store_util.get_bc_api_creds(store)
    
    while True:
        qp["page"] = page
        res = bc_util.call_api(bc_api, "GET", api, query_params=qp)
        
        if res.status_code == 200:
            res = res.json()
            data = res["data"]
            meta = res["meta"]["pagination"]
            
            if meta["total"] == 0:
                break
            
            documents = []
            for row in data:
                if "sku" in row:
                    row['_id'] = row['sku']
                    documents.append(row)
            
            # Append documents to the records list to return later
            records.extend(documents)
            
            # Break the loop if we're on the last page
            if meta['total_pages'] == meta['current_page']:
                break
            
            page = page + 1
        else:
            break
    
    # Return the accumulated records
    return records

def delete_price_list(store, variant_ids, price_list_id):
    variant_ids_str = ','.join(map(str, variant_ids))
    url = f'v3/pricelists/{price_list_id}/records'
    bc_api = store_util.get_bc_api_creds(store)
    qp = {"variant_id:in": variant_ids_str}
    res = bc_util.call_api(bc_api, "DELETE", url, query_params=qp) 

    # success ...   
    if res.status_code == 204:
        return res, 204
    else:
        # unprocess able entity...
        return res, res.status_code
    

# def update_price_list(store, price_list_id, req_body):
#     url = f'v3/pricelists/{price_list_id}/records'
#     bc_api = store_util.get_bc_api_creds(store)
#     res = bc_util.call_api(bc_api, "PUT", url, query_params={} ,req_body=req_body) 

#     # success ...   
#     if res.status_code == 200:
#         return res.json(), 200
#     else:
#         # unprocess able entity...
#         return res.json(), res.status_code

def update_price_list(store, price_list_id, req_body):
    url = f'v3/pricelists/{price_list_id}/records'
    bc_api = store_util.get_bc_api_creds(store)
    res = bc_util.call_api(bc_api, "PUT", url, query_params={}, req_body=req_body)

    try:
        response_json = res.json()
    except ValueError:
        # Response is not JSON (likely an error or empty body)
        response_json = {"error": "Invalid or empty JSON response from BigCommerce", "text": res.text}

    return response_json, res.status_code
