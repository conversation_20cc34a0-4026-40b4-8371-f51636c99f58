from datetime import datetime, timedelta, timezone
from sqlalchemy import text
from decimal import Decimal
from pg_db.analytics_db import AnalyticsDB
from pymongo.collation import Collation
from bson import ObjectId
from flask import json
from bson import json_util
import re
from dateutil.relativedelta import relativedelta

month_array = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

# function for parent sku listing in replenishment listing screen...
def get_six_month_data_using_parent_sku(product_skus, conn, sale_history_months, six_month_flag):
    start_date_str = datetime.now(timezone.utc).strftime("%Y-%m-%d")
    current_date = datetime.now(timezone.utc)
    six_month_previous_date = (current_date - timedelta(days=current_date.day - 1)
                               ).replace(day=1) - timedelta(int(sale_history_months) * 30)
    days_in_current_month = current_date.day - 1
    day_difference = (
        current_date - six_month_previous_date).days - days_in_current_month + 1
    month_name_array = {}
    # logic to get months for previous six month from current month
    while current_date >= six_month_previous_date:
        month_name = current_date.strftime("%b")
        year_name = current_date.strftime("%Y")
        month_name_array[month_name] = year_name
        current_date -= timedelta(days=30)  # Move to the previous month

    month_name_array = dict(reversed(month_name_array.items()))
    
    
    if six_month_flag:
        # get first day of previous six month
        six_month_previous_date_str = get_first_date_of_month(month_name_array)
    else:
        # get data of before 30 days
        current_date = datetime.now(timezone.utc)
        thirty_days_ago = current_date - timedelta(days=day_difference)        
        six_month_previous_date_str = thirty_days_ago.strftime('%Y-%m-%d')
    
    # get month names
    month_names = generate_meta_months(month_name_array)

    query = (
        "SELECT parent_sku, sum(quantity) as sold_quantity, order_month, order_year "
        f"FROM {AnalyticsDB.get_products_trend_table()} "
        "WHERE parent_sku IN (" + product_skus + ") "
        "AND order_date_time >= '" + six_month_previous_date_str + "' "
        "AND order_date_time <= '" + start_date_str + "' "
        "GROUP BY parent_sku, order_month, order_year"
    )    

    monthly_rs = conn.execute(text(query))

    def convert_to_json(rs, columns):
        results = []
        for row in rs:
            result_dict = {}
            for key, value in zip(columns, row):
                if isinstance(value, Decimal):
                    if (key == 'order_month'):
                        result_dict[key] = month_array[int(value) - 1]
                    else:
                        result_dict[key] = int(value)  # Convert Decimal to float
                else:
                    result_dict[key] = value
            results.append(result_dict)
        return results


    monthly_result = convert_to_json(monthly_rs, ['parent_sku', 'sold_quantity', 'order_month', 'order_year'])

    return monthly_result, month_names, day_difference


def get_first_date_of_month(month_array):
    first_key, first_value = next(iter(month_array.items()))
    int_month = datetime.strptime(first_key, '%b').month
    year = datetime.strptime(first_value, '%Y').year
    first_date = datetime(year, int_month, 1).strftime("%Y-%m-%d")
    return first_date

def generate_meta_months(month_array):
    months = {}
    for index, (m_key, month) in enumerate(month_array.items(), 0):
        key = 'month_' + str(index + 1)
        months[key] = m_key
    return months

def get_month_array_for_meta(sale_history_months):    
    current_date = datetime.now()
    six_month_previous_date = (current_date - timedelta(days=current_date.day - 1)
                               ).replace(day=1) - timedelta(int(sale_history_months) * 30)
    days_in_current_month = current_date.day - 1
    day_difference = (
        current_date - six_month_previous_date).days - days_in_current_month + 1
    month_name_array = {}
    # logic to get months for previous six month from current month
    while current_date >= six_month_previous_date:
        month_name = current_date.strftime("%b")
        year_name = current_date.strftime("%Y")
        month_name_array[month_name] = year_name
        current_date -= relativedelta(months=1)  # Move to the previous month

    month_name_array = dict(month_name_array.items())           
    
    # get month names
    month_names = generate_meta_months(month_name_array)
    
    return month_names, day_difference

def get_day_array_for_meta():
    current_date = datetime.now()
    start_date = current_date.replace(day=1)

    day_labels = {}
    count = 1

    while start_date <= current_date:
        formatted_day = start_date.strftime("%d / %B %Y")
        day_labels[f"day_{count}"] = formatted_day
        start_date += timedelta(days=1)
        count += 1

    return day_labels

def get_records_price_list(db_client, collection_name, payload, fields, additionalQuery):
    coll = db_client[collection_name]
    sort = {
        'sort_by': payload.get('sort_by') or 'date_created'
    }
    sort['sort_order'] = 1 if payload.get('sort_order') == 'asc' else -1

    def create_reg_ex_query(filterBy, filter):
        query = {"$or": []}
        if filter:
            escaped_filter = re.escape(filter)  # Escape special characters
            for i in filterBy:
                regex_pattern = f".*{escaped_filter}.*"
                query['$or'].append({i: {"$regex": regex_pattern, "$options": "i"}})
        if not query['$or']:
            query = {}
        query.update(additionalQuery)
        return query

    query = create_reg_ex_query(payload.get("filterBy", []), payload.get("filter", "")) if payload.get("filterBy") else additionalQuery
    
    collation = Collation(locale='en', strength=2)
    data = coll.find(query, fields).collation(collation).sort(sort['sort_by'], sort['sort_order'])
    
    data = processList(data)
    document_length = coll.count_documents(query)
    
    return parse_json(data), document_length


def processList(data):
    result = []
    if data:
        for _obj in data:
            result.append(processDocument(_obj))
    return result


def processDocument(obj):
    if obj:
        if '_id' in obj:
            obj['id'] = str(obj['_id'])
            del obj['_id']

        for key, value in obj.items():
            if isinstance(value, datetime) or isinstance(value, ObjectId):
                obj[key] = str(value)

    return obj

def parse_json(data):
    return json.loads(json_util.dumps(data))