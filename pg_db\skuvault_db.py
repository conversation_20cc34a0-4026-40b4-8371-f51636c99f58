from sqlalchemy import Column, DateTime, String, Integer, Boolean, Float, BigInteger
from sqlalchemy import func
import pg_db as db

class SkuvaultDB:
    schema_name = "skuvault"
    skuvault_sales = "skuvault_sales"
    skuvault_cost = "skuvault_cost"

    @classmethod
    def get_skuvault_sales_table(cls):
        return cls.schema_name + "." + cls.skuvault_sales

    @classmethod
    def get_skuvault_cost_table(cls):
        return cls.schema_name + "." + cls.skuvault_cost


class SkuvaultCatalog(db.Base):
    __tablename__ = db.skuvault_catalog
    
    code = Column(String, primary_key=True)
    sku = Column(String)
    parent_sku = Column(String)
    part_number = Column(String)
    title = Column(String)
    note = Column(String)
    moq = Column(Integer)
    moq_info = Column(String)
    cost = Column(Float)
    retail_price = Column(Float)
    sale_price = Column(Float)
    weight = Column(Float)
    weight_unit = Column(String)
    reorder_point = Column(Integer)
    incremental_quantity = Column(Integer)
    brand = Column(String)
    primary_supplier = Column(String)
    primary_supplier_is_active = Column(Boolean)
    primary_supplier_cost = Column(Float)
    primary_supplier_lead_time = Column(Integer)
    primary_supplier_part_number = Column(String)
    classification = Column(String)
    statuses = Column(String)
    created_date = Column(DateTime)
    modified_date = Column(DateTime)
    disable_quantity_sync = Column(Boolean)
    quantity_on_hand = Column(Integer)
    quantity_pending = Column(Integer)
    quantity_incoming = Column(Integer)
    quantity_available = Column(Integer)
    quantity_on_hold = Column(Integer)
    attribute1_name = Column(String)
    attribute1_value = Column(String)
    attribute2_name = Column(String)
    attribute2_value = Column(String)
    attribute3_name = Column(String)
    attribute3_value = Column(String)
    attribute4_name = Column(String)
    attribute4_value = Column(String)
    attribute5_name = Column(String)
    attribute5_value = Column(String)

    def __repr__(self):
        return f'Product SKU {self.code}'

    @classmethod
    def get_last_modified_date(cls, store_id, session=None):
        local_session = None
        if not session:
            session = db.get_session(store_id)
            local_session = session
        try:
            q = session.query(func.max(SkuvaultCatalog.modified_date)).first()
            last_modified_at = None
            if q and len(q) > 0:
                last_modified_at = q[0]
            return last_modified_at
        finally:
            if local_session:
                local_session.close()


class SkuvaultSales(db.Base):
    __tablename__ = SkuvaultDB.skuvault_sales
    __table_args__ = {'schema': SkuvaultDB.schema_name}
    
    skuvault_id = Column(String, primary_key=True)
    market_place = Column(String)
    status = Column(String)
    bc_order_id = Column(BigInteger)
    sale_date = Column(DateTime)
    created_date = Column(DateTime, server_default=func.now())
    modified_date = Column(DateTime, server_default=func.now())
    

    def __repr__(self):
        return f'Sales ID {self.skuvault_id}'

    @classmethod
    def get_max_sales_date(cls, store_id, session=None):
        local_session = None
        if not session:
            session = db.get_session(store_id)
            local_session = session
        try:
            q = session.query(func.max(SkuvaultSales.sale_date)).first()
            max_sales_date = None
            if q and len(q) > 0:
                max_sales_date = q[0]
            return max_sales_date
        finally:
            if local_session:
                local_session.close()


class SkuvaultCost(db.Base):
    __tablename__ = SkuvaultDB.skuvault_cost
    __table_args__ = {'schema': SkuvaultDB.schema_name}
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    skuvault_id = Column(String)
    bc_order_id = Column(BigInteger)
    sku = Column(String)
    cost = Column(Float)
    skuvault_id = Column(String, primary_key=True)
    created_date = Column(DateTime, server_default=func.now())
    modified_date = Column(DateTime, server_default=func.now())
    

    def __repr__(self):
        return f'Cost ID {self.id}'
