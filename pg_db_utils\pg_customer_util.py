import time
import datetime
import pg_db_utils
import pandas as pd
import pg_db as db
from pg_db import customers_db
from utils import store_util
# import ipinfo
from sqlalchemy import update
import logging
import traceback
from sqlalchemy.exc import SQLAlchemyError

logger = logging.getLogger()

def build_customer_model(customer):
    date_format = "%Y-%m-%dT%H:%M:%SZ"
    created_date = datetime.datetime.strptime(customer['date_created'], date_format)
    modified_date = datetime.datetime.strptime(customer['date_modified'], date_format)

    created_date_day = created_date.day
    created_date_month = created_date.month
    created_date_month_str = created_date.strftime("%b")
    created_date_year = created_date.year
    created_date_qtr = 'q' + str(int((created_date_month - 1) / 3) + 1)

    return customers_db.Customers(
        customer_id=int(customer['id']),
        first_name = customer['first_name'],
        last_name = customer['last_name'],
        company = customer['company'],
        email = customer['email'],
        phone = customer['phone'],
        notes = customer['notes'],
        accepts_product_review_abandoned_cart_emails = customer['accepts_product_review_abandoned_cart_emails'],
        tax_exempt_category = customer['tax_exempt_category'],
        registration_ip_address = customer['registration_ip_address'],
        store_credit_in_USD = customer['store_credit_in_USD'],
        customer_group_id = int(customer['customer_group_id']),
        customer_group_name = customer['customer_group_name'],
        date_created = created_date,
        date_modified = modified_date,
        created_day = created_date_day,
        created_month = created_date_month_str,
        created_year = created_date_year,
        created_qtr = created_date_qtr
    )

def build_address_model(address):
    return customers_db.CustomerAddresses(
        customer_id=int(address['customer_id']),
        customer_address_id=int(address['id']),
        first_name = address['first_name'],
        last_name = address['last_name'],
        company = address['company'],
        address_line_1 = address['address1'],
        address_line_2 = address['address2'],
        city = address['city'],
        state = address['state_or_province'],
        postal_code = address['postal_code'],
        address_type = address['address_type'],
        country_code = address['country_code'],
        phone = address['phone']
    )

def build_custom_field_model(field):
    return customers_db.CustomerFormFields(
        customer_id=int(field['customer_id']),
        custom_field_name=field['name'],
        custom_field_value = field.get('value', '')
    )

def fetch_customer_groups(store):
    customer_groups = {}
    page = 1
    while True:
        logger.info(f"Fetching customer group page: {page}")
        query_params = {
            "limit": 250,
            "page": page
        }

        req_body = {
            "query_params": query_params,
            "method": "GET",
            "url": "v2/customer_groups"
        }

        res = pg_db_utils.process_api(req_body, store)

        if res and res['status_code'] == 200 and res['data']:
            if len(res['data']) > 0:
                for row in res['data']:
                    customer_groups[str(row["id"])] = row["name"]
            else:
                break
        else:
            break

        page = page + 1

    return customer_groups

def fetch_customers(store, query_params={}):
    customers = []
    addresses = []
    form_fields = []

    page = 1

    customer_groups = fetch_customer_groups(store)
    query_params['limit'] = 500
    query_params['include'] = "addresses,formfields,storecredit"
    while True:
        logger.info(f"Fetching customer page: {page}")    
        query_params['page'] = page

        req_body = {
            "query_params": query_params,
            "method": "GET",
            "url": "v3/customers"
        }

        res = pg_db_utils.process_api(req_body, store)

        if res and res['status_code'] == 200 and res['data']:
            if len(res['data']['data']) > 0:
                for row in res['data']['data']:
                    store_credit_amounts = 0
                    if "store_credit_amounts" in row and row["store_credit_amounts"]:
                        for amount in row["store_credit_amounts"]:
                            store_credit_amounts = store_credit_amounts + amount['amount']

                    customer_group_name = ""
                    customer_group_id = ""
                    if "customer_group_id" in row:
                        customer_group_id = str(row["customer_group_id"])
                        customer_group_name = customer_groups.get(customer_group_id)

                    row["customer_group_id"] = customer_group_id
                    row["customer_group_name"] = customer_group_name
                    row["store_credit_in_USD"] = store_credit_amounts
                    customers.append(row)

                    if "addresses" in row:
                        for address in row["addresses"]:
                            address["customer_id"] = row["id"]
                            addresses.append(address)
                    
                    if "form_fields" in row:
                        for form_field in row["form_fields"]:
                            form_field["customer_id"] = row["id"]
                            form_fields.append(form_field)
            else:
                break
        time.sleep(0.2)
        page = page + 1
    return customers, addresses, form_fields

def process_customers(store_id, customers):
    session = db.get_session(store_id)
    try:
        with session.begin():
            for customer in customers:
                model = build_customer_model(customer)
                existing_customer = customers_db.Customers.get_customer(store_id, customer["id"], session)
                if existing_customer:
                    session.delete(existing_customer)
                session.add(model)
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        if session:
            session.commit()
            session.close()

def process_addresses(store_id, addresses):
    session = db.get_session(store_id)
    try:
        with session.begin():
            for address in addresses:
                model = build_address_model(address)
                existing_address = customers_db.CustomerAddresses.get_customer_address(store_id, address["id"], session)
                if existing_address:
                    session.delete(existing_address)
                session.add(model)
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        if session:
            session.commit()
            session.close()

def process_form_fields(store_id, form_fields):
    session = db.get_session(store_id)
    try:
        with session.begin():
            for form_field in form_fields:
                model = build_custom_field_model(form_field)
                existing_field = customers_db.CustomerFormFields.get_custom_field(store_id, form_field["customer_id"], form_field["name"], session)
                if existing_field:
                    session.delete(existing_field)
                session.add(model)
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        if session:
            session.commit()
            session.close()

def incremental_update(store_id):
    store = store_util.get_store_by_id(store_id)
    min_date_modified_iso = None
    session = db.get_session(store_id)
    try:
        min_date_modified_iso = customers_db.Customers.get_last_modified_at(store_id, session)
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        session.close()
        
    query_params = {}
    if min_date_modified_iso:
        query_params = {
            "date_modified:min": min_date_modified_iso.isoformat()
        }

    customers, addresses, form_fields = fetch_customers(store, query_params)

    if customers and len(customers) > 0:
        process_customers(store_id, customers)

    if addresses and len(addresses) > 0:
        process_addresses(store_id, addresses)

    if form_fields and len(form_fields) > 0:
        process_form_fields(store_id, form_fields)

def update_customer_data(store):
    min_date_modified_iso = (datetime.datetime.utcnow() - datetime.timedelta(hours=6)).isoformat()

    customers, addresses, form_fields = fetch_customers(store)

    if customers and len(customers) > 0:
        fields = ["customer_id", "first_name", "last_name", "company", "email", "phone",
                                   "notes", "accepts_product_review_abandoned_cart_emails", "date_created",
                                   "date_modified", "tax_exempt_category", "registration_ip_address",
                                   "store_credit_in_USD", "customer_group_id", "customer_group_name"]
        
        df = pd.DataFrame(data=customers, columns=fields)
        df['customer_id'] = df['customer_id'].astype('int64')
        df['customer_group_id'] = df['customer_group_id'].astype('int64')
        df['date_created'] = pd.to_datetime(df['date_created'])
        df['date_modified'] = pd.to_datetime(df['date_modified'])
        pg_db_utils.save_to_db(store['id'], df, 'customers')

    if addresses and len(addresses) > 0:
        fields = ["customer_id", "customer_address_id", "first_name", "last_name", "company",
                "address_line_1", "address_line_2", "city", "state", "postal_code", "country_code",
                "address_type", "phone"]
        df = pd.DataFrame(data=addresses, columns=fields)
        df['customer_id'] = df['customer_id'].astype('int64')
        df['customer_address_id'] = df['customer_address_id'].astype('int64')
        pg_db_utils.save_to_db(store['id'], df, 'customer_addresses')

    if form_fields and len(form_fields) > 0:
        fields = ["customer_id", "custom_field_name", "custom_field_value"]
        df = pd.DataFrame(data=form_fields, columns=fields)
        df['customer_id'] = df['customer_id'].astype('int64')
        pg_db_utils.save_to_db(store['id'], df, 'customer_form_fields')

def process_created_date(store_id):
    session = db.get_session(store_id)
    try:
        with session.begin():
            customers = session.query(customers_db.Customers)
            for customer in customers:
                created_date = customer.date_created
                customer.update({'created_day': created_date.day})
    finally:
        session.commit()
        session.close()

def build_customer_session_model(customer_id, ip_address, timestamp, city, state, country, timezone):
    return customers_db.CustomerLoginSessions(
        customer_id=customer_id,
        login_timestamp=timestamp,
        ip_address=ip_address,
        city=city,
        state=state,
        country=country,
        time_zone=timezone,
    )

def _get_ip_info(ip_address):
    ip_handler = "d072ea0dfa89dc" #ipinfo.getHandler('d072ea0dfa89dc')
    customer_session = ip_handler.getDetails(ip_address)
    ip_info = {
        "city": "",
        "state": "",
        "country": "",
        "timezone": ""
    }
    try:
        ip_info["city"] = customer_session.city
    except Exception as ex:
        pass
    try:
        ip_info["state"] = customer_session.region
    except Exception as ex:
        pass
    
    try:
        ip_info["country"] = customer_session.country
    except Exception as ex:
        pass

    try:
        ip_info["timezone"] = customer_session.timezone
    except Exception as ex:
        pass

    return ip_info

def process_customer_session_details(store_id, customer_id, ip_address, timestamp):
    ip_info = _get_ip_info(ip_address)
    session = db.get_session(store_id)
    try:
        with session.begin():
            if ip_address:
                model = build_customer_session_model(customer_id, ip_address, timestamp, ip_info["city"], \
                                                     ip_info["state"], ip_info["country"], ip_info["timezone"])
                session.add(model)
    except Exception as ex:
        logger.error(traceback.format_exc())

    finally:
        if session:
            session.commit()
            session.close()


def update_pg_customer_points(store_id, data):
    session = db.get_session(store_id)
    try:
        with session.begin():
            customer_id = data['customer_id']
            points = data['loyalty_points']

            session.execute(
                update(customers_db.Customers).
                where(customers_db.Customers.customer_id == customer_id).
                values(store_credit_in_USD=points)
            )
           
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        if session:
            session.commit()
            session.close()

def remove_customer_pgdb(store_id, customer_id):
    session = db.get_session(store_id)
    try:
        customer_id = int(customer_id)
        # Find the customer by customer_id
        customer = session.query(customers_db.Customers).filter(customers_db.Customers.customer_id == customer_id).one_or_none()
        
        if customer is not None:
            # Delete the customer if found
            session.delete(customer)
            session.commit()
    except SQLAlchemyError as e:
        # Rollback in case of error
        session.rollback()
        # Log the error
        print(f"An error occurred while deleting the customer: {str(e)}")
    finally:
        # Close the session
        session.close()