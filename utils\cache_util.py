import logging

logger = logging.getLogger()

class Cache:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            logger.info('Creating the Cache')
            cls._instance = super(Cache, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        logger.info("Entering Cache")
        self.__stores = {}
        logger.info("Exiting Cache")

    def get_store(self, store_id):
        return self.__stores.get(store_id, None)
    
    def set_store(self, store):
        self.__stores[store['id']] = store
        self.__stores[store['store_url']] = store
        self.__stores[store['domain']] = store
        self.__stores[store['bc_config']['store_hash']] = store

    def clear_cache(self, store_id):
        self.__stores = {}

def get_store(store_id):
    store = __cache.get_store(store_id)
    return store

def set_store(store):
    __cache.set_store(store)

def clear_cache(store_id):
    __cache.clear_cache(store_id)

__cache = Cache()