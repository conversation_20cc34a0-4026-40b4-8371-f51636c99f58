import requests
import time
from sqlalchemy import text
import time
import pg_db as db
from utils import store_util
import logging
import traceback
from utils import email_util
from pg_db.analytics_db import AnalyticsDB

logger = logging.getLogger()

def call_api(method, url, query_params=None, req_body=None, is_bc_api=True, bc_config=None):
    headers = {}
    headers["Accept"] = "application/json"
    headers["Content-Type"] = "application/json"
    if is_bc_api and bc_config:
        api_url = "https://api.bigcommerce.com/stores/" + bc_config['store_hash'] + '/' + url
        headers["X-Auth-Client"] = bc_config['client_id']
        headers["X-Auth-Token"] = bc_config['access_token']
    else:
        api_url = url
    method = method.upper()
    retry_count = 0
    res = None
    while retry_count < 5:
        try:
            if method == "GET":
                res = requests.get(url=api_url, params=query_params, headers=headers)
            if method == "POST":
                res = requests.post(url=api_url, json=req_body, headers=headers)
            retry_count = 5
        except Exception as e: 
            logger.error("Caught exception while calling bc api: " + api_url + ": " +traceback.format_exc())
            if retry_count < 4:
                time.sleep(60)
        retry_count = retry_count + 1

    return res

def process_api(req_body, store):
    query_params = {}
    if "query_params" in req_body:
        query_params = req_body["query_params"]

    body = {}
    if "body" in req_body:
        body = req_body["body"]

    bc_api = store_util.get_bc_api_creds(store)
    res = call_api(req_body["method"], 
                    req_body['url'], query_params, body, True, bc_api)
    
    response_body = {}
    try:
        if res.status_code == 200:
            response_body = res.json()
    except Exception as e: 
        logger.error(req_body)
        logger.error(traceback.format_exc())

    return {
        "status_code": res.status_code,
        "data": response_body
    }


def save_to_db(df, store_id, table_name):
    conn = None
    try:
        conn = db.get_connection(store_id)
        start_time = time.time()
        df.to_sql(table_name, con=conn, if_exists='replace', index=False)
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        if conn:
            conn.close()

def update_table_update_time(store_id, table_name):
    conn = None
    try:
        conn = db.get_connection(store_id)
        result = conn.execute(text("SELECT count(*) FROM table_metadata WHERE table_name='" + table_name + "'"))
        for row in result:
            if row[0] == 0:
                pass
            else:
                conn.execute(text("SELECT count(*) FROM table_metadata WHERE table_name='" + table_name + "'"))
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        if conn:
            conn.close()

def upsert_to_db(store_id, df, table_name):
    conn = db.get_connection(store_id)
    try:
        start_time = time.time()
        t_name = "temp_" + table_name
        conn.exec_driver_sql("CREATE TEMPORARY TABLE " + t_name +  " AS SELECT * FROM " + table_name + " WHERE false")
        df.to_sql(t_name, conn, index=False, if_exists="append")
        conn.exec_driver_sql(
            """\
            INSERT INTO  (id, txt) 
            SELECT id, txt FROM temp_table
            ON CONFLICT (id) DO
                UPDATE SET txt = EXCLUDED.txt
            """
        )
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        if conn:
            conn.close()

def terminate_idle_connections(store_id):
    query = """
    WITH inactive_connections AS (
        SELECT pid, rank() over (partition by client_addr order by backend_start ASC) as rank
        FROM pg_stat_activity WHERE pid <> pg_backend_pid()
        AND application_name !~ '(?\:psql)|(?\:pgAdmin.+)' AND datname = current_database() 
        AND state in ('idle', 'idle in transaction', 'idle in transaction (aborted)', 'disabled') 
        AND current_timestamp - state_change > interval '5 minutes' 
    ) SELECT pg_terminate_backend(pid) FROM inactive_connections WHERE rank > 1
    """
    conn = db.get_connection(store_id)
    try:
        conn.execute(text(query))
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        conn.close()

def old_safety_stock_check(store_id):
    conn = db.get_connection(store_id)

    try:
        # Fetch safety stock records, including child_sku and variant_id
        safety_stock_query = text("""
            SELECT qty, child_sku, variant_id 
            FROM replenishment_safety_stock
        """)
        safety_stock_records = conn.execute(safety_stock_query).fetchall()
        
        low_stock_records = []

        for record in safety_stock_records:
            qty, child_sku, variant_id = record[0], record[1], record[2]
            
            # Query to fetch the product details including the parent_sku and product_title from replenishment_products
            variants_query = text(f"""
                SELECT v.quantity_available, v.sku, v.quantity_incoming, p.product_title 
                FROM {AnalyticsDB.get_replenishment_variants_table()} v
                JOIN {AnalyticsDB.get_replenishment_products_table()} p ON v.parent_sku = p.parent_sku
                WHERE v.sku = :sku
            """)
            variants_result = conn.execute(variants_query, {'sku': child_sku}).fetchone()
            
            if variants_result:
                quantity_available = variants_result[0]
                quantity_incoming = variants_result[2]
                product_title = variants_result[3]

                # Fetch variant options from the 'variants' table using variant_id
                variant_options_query = text("""
                    SELECT variant_options 
                    FROM variants 
                    WHERE variants_id = :variant_id
                """)
                variant_options_result = conn.execute(variant_options_query, {'variant_id': variant_id}).fetchone()

                if quantity_available < qty:
                    low_stock_record = {
                        'child_sku': child_sku,
                        'safety_stock': qty,
                        'available_stock': quantity_available,
                        'incoming_stock': quantity_incoming,
                        'product_title': product_title
                    }
                    
                    # If variant options exist, include them in the low stock record
                    if variant_options_result:
                        low_stock_record['variant_options'] = variant_options_result[0]
                    
                    low_stock_records.append(low_stock_record)

        if low_stock_records:
            email_util.send_low_stock_email(store_id, low_stock_records)
    
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        conn.close()

def safety_stock_check(store_id):
    conn = db.get_connection(store_id)

    try:
        # Fetch safety stock records, including child_sku and variant_id
        safety_stock_query = text("""
            SELECT qty, child_sku, variant_id, parent_sku 
            FROM replenishment_safety_stock
        """)
        safety_stock_records = conn.execute(safety_stock_query).fetchall()
        
        low_stock_records = []
        for record in safety_stock_records:
            qty, child_sku, variant_id, parent_sku = record[0], record[1], record[2], record[3]
            
            # Query to fetch the product details including the parent_sku and product_title from replenishment_products
            variants_query = text(f"""
                SELECT v.quantity_available, v.sku, v.quantity_incoming, p.product_title, v2.variant_options
                FROM {AnalyticsDB.get_replenishment_variants_table()} v
                JOIN {AnalyticsDB.get_replenishment_products_table()} p ON v.parent_sku = p.parent_sku
                LEFT JOIN variants v2 ON v.sku = v2.variants_sku                                  
                WHERE v.sku = :sku
            """)
            variants_result = conn.execute(variants_query, {'sku': child_sku}).fetchone()

            supplier_query = text(f"""
                SELECT MAX(RP.parent_sku) AS parent_sku,
                        USM.USER_NAME,
                        USM.EMAIL_ID
                FROM {AnalyticsDB.get_replenishment_products_table()} RP
                LEFT JOIN skuvault_catalog sv ON sv.parent_sku = rp.parent_sku 
                LEFT JOIN USER_SUPPLIER_MAPPING USM ON sv.PRIMARY_SUPPLIER = USM.SUPPLIERS
                WHERE sv.PRIMARY_SUPPLIER <> 'Unknown'
                    AND RP.PARENT_SKU IS NOT NULL
                    AND RP.PARENT_SKU != ''
                    AND RP.PARENT_SKU = :parent_sku
                GROUP BY USM.USER_NAME, USM.EMAIL_ID
            """)
            supplier_result = conn.execute(supplier_query, {'parent_sku': parent_sku}).fetchone()
            
            if variants_result:
                quantity_available = variants_result[0]
                quantity_incoming = variants_result[2]
                product_title = variants_result[3]
                variant_options = variants_result[4]    

                if quantity_available < qty:
                    low_stock_record = {
                        'child_sku': child_sku,
                        'safety_stock': qty,
                        'available_stock': quantity_available,
                        'incoming_stock': quantity_incoming,
                        'product_title': product_title,
                        'variant_options': variant_options,
                        'user_name': supplier_result[1] if supplier_result else None,
                        'user_email': supplier_result[2] if supplier_result else None
                    }
                    
                    low_stock_records.append(low_stock_record)
        if low_stock_records:
            email_util.send_low_stock_email(store_id, low_stock_records)
        
    
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        conn.close()
