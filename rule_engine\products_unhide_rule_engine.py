import pg_db
from pg_db import products_db
import logging
import traceback
from plugin import bc_products

logger = logging.getLogger()


def execute_rules(store, products):    
    db_conn = pg_db.get_connection(store['id'])
    session = pg_db.get_session(store['id'])   
    try:        
        # unhide_products = {}
        unhide_products_rules = {}
        unhide_category_id = [1734, 2069, 147]
        
        rules = products_db.Products.get_products(store['id'], list(products.keys()), session)        

        for rule in rules:  
            product_id = rule.product_id             
            out_of_stock_date = rule.out_of_stock_date
            inventory_level = rule.inventory_level
            is_visible = rule.is_visible                                        
            product = products.get(product_id, None)                   
            if product:      
                if not any(category_id in unhide_category_id for category_id in product['categories']) and product['inventory_level'] > 0 and not is_visible:                                        
                    # unhide_products[product_id] = product_id    
                    unhide_products_rules[product_id] = product                                                                     

        if len(unhide_products_rules) > 0:
            actions_to_do_on_unhide_product(store, unhide_products_rules, db_conn)            

    except Exception as ex:
        logger.error(str(traceback.format_exc()))
    finally:        
        db_conn.commit()
        db_conn.close()  
        if session:
            session.commit()
            session.close()                     
    
    

def actions_to_do_on_unhide_product(store, unhide_products_rules, db_conn):
    # db_conn = pg_db.get_connection()   
    try:                    
        for product in unhide_products_rules.values():            
            categories = ''            
            if 'categories' in product:
                categories = ', '.join(map(str, product['categories']))
            products_db.ProductsUnhideRules.insert_unhide_products(db_conn, product['product_id'], product['name'], categories, True, True)
            bc_products.unhide_product(store, product['product_id'], True)
        
    except Exception as ex:
        logger.error(str(traceback.format_exc()))
    # finally:
    #     db_conn.commit()
    #     db_conn.close()
        