products_variant_query = """
query ProductsWithOptionSelections(
  $productIds: [Int!] = product_id_list
  $variantIds: [Int!] = variant_id_list
) {
  site {
    products: products(entityIds: $productIds, first: 50) {
      edges {
        node {
          ...ProductFields
        }
      }
    }
  }
}

fragment ProductFields on Product {
  entityId
  sku
  name
  minPurchaseQuantity
  maxPurchaseQuantity
  availabilityV2 {
    status
  }
  inventory {
    aggregated {
      availableToSell
    }
  }
  variants(entityIds: $variantIds, first: 250) {
    edges {
      node {
        entityId
        sku
        inventory {
          aggregated {
            availableToSell
          }
        }
        options {
          edges {
            node {
              entityId
              displayName
              isRequired
              values {
                edges {
                  node {
                    entityId
                    label
                  }
                }
              }
            }
          }
        }
        prices {
          price {
              ...MoneyFields
          }
          salePrice {
              ...MoneyFields
          }
          retailPrice {
              ...MoneyFields
          }
      }
      }
    }
  }
}
fragment MoneyFields on Money {
   value
   currencyCode
}
"""

def get_query(product_ids, variant_ids):
  query = products_variant_query
  query = query.replace("product_id_list", str(product_ids))
  query = query.replace("variant_id_list", str(variant_ids))
  query = query.replace("\n", "")
  return query
