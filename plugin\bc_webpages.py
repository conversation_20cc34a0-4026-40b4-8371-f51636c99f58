from mongo_db import catalog_db, store_info_db
import plugin
from pymongo import TEXT
import mongo_db
from urllib.parse import urlparse
import logging
import traceback

logger = logging.getLogger()

def fetch_web_pages_rest_api(store):
    query_params = {
        "limit": 250,
        "page": 1
    }
    api = "v2/pages"
    web_pages = plugin.fetch_all_by_rest_api(store, api, limit_per_req=250, query_params=query_params, 
            db_collection=catalog_db.WEB_PAGES_COLLECTION, db_process_threshold=250)

    if len(web_pages):
        db = mongo_db.get_store_db_client(store)
        res = db[catalog_db.WEB_PAGES_COLLECTION].create_index([('name', TEXT)], default_language='english')
        #logger.debug('index -> ',res)
    return web_pages
    
def redirect_resource_processor(store, data):
    for obj in data:
        try:
            redirect_to = obj.get("to", None)
            if redirect_to:
                obj["to_type"] = redirect_to.get("type", None)
                obj["to_entity_id"] = redirect_to.get("entity_id", None)
                u = urlparse(obj.get("to_url", None))
                if u and u.path:
                    obj["to_path"] = u.path
        except Exception as ex:
            logger.error("redirect_resource_processor: Exception: " + str(traceback.format_exc()))
    return data

def fetch_redirects_rest_api(store):
    query_params = {
        "include": "to_url",
        "limit": 250,
        "page": 1
    }
    api = "v3/storefront/redirects"
    return plugin.fetch_all_by_rest_api(store, api, limit_per_req=250, query_params=query_params, 
            db_collection=store_info_db.REDIRECTS_COLLECTION, db_process_threshold=250, 
            resource_processor=redirect_resource_processor)