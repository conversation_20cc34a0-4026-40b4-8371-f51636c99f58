from redis import Redis
import json
import threading
from datetime import datetime
from pytz import timezone
import utils
import logging
from config import appconfig

logger = logging.getLogger()

GRAPHQL_TOKEN_HSET = "graphql_token"
FEATURED_PRODUCTS_HSET = "featured_products"
NEW_PRODUCTS_HSET = "new_products"
POPULAR_PRODUCTS_HSET = "popular_products"
PREORDER_PRODUCTS_HSET = "preorder_products"
BRANDS_HSET = "brand_"
TOKEN_SECRETS_HSET = "token_secrets"
ACCESS_TOKENS_HSET = "access_token:"
CUSTOMER_GROUP_PRICING = "cgp_"
SKU_INVENTORY = "inventory_"
JWT_SECRET_HSET = "jwt_secrets_"
CART_DELETE_QUEUE = "cart_delete_queue_"
CART_ADD_LINEITEMS_QUEUE = "cart_add_lineitems_queue_"
CUSTOMER_DELETE_QUEUE = "customer_delete_queue:"

GSHEET_MAPPING_HSET = "gsheet_mapping_"

PRODUCT_UPDATE_SET = "wpus_"
PRODUCT_UPDATE_QUEUE = "wpuq_"
CUSTOMER_UPDATE_SET = "wcus_"
CUSTOMER_UPDATE_QUEUE = "wcuq_"

FROGOT_PASSWORD_HSET = "cfp_"
NEW_ACCOUNT_TOKEN_HSET = "cna_"

SALESFORCE_TOKEN_KEY = 'salesforce_token'
SALESFORCE_CUSTOMERS_KEY = 'salesforce_customers'

RULE_ENGINE_VARIANT_VISIBILITY_RULES_KEY = 're_vvr'
RULE_ENGINE_PRODUCT_VISIBILITY_RULES_KEY = 're_pvr'

EXPRESS_CUSTOMER_ACTIVITY_HSET = "ecah_"
MAIN_CUSTOMER_ACTIVITY_HSET = "mcah_"
ZOHO_ACCESS_TOKEN = "zoho_token"

class RedisCLI:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            logger.info('Creating the RedisCLI')
            cls._instance = super(RedisCLI, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        logger.info("Entering RedisCLI")
        self._lock = threading.Lock()
        self._redis_clients = {}
        logger.info("Exiting RedisCLI")
    
    def get_redis_client(self, store_id):
        redis_client = self._redis_clients.get(store_id, None)
        if not redis_client:
            try:
                self._lock.acquire()
                redis_client = self._redis_clients.get(store_id, None)
                if not redis_client:
                    redis_config = utils.get_redis_config(store_id)
                    if redis_config:
                        _host = redis_config.get("host")
                        _port = redis_config.get("port")
                        _db = redis_config.get("cache_db")
                        redis_client = Redis(host=_host, port=_port, db=_db)
                        self._redis_clients[store_id] = redis_client
            finally:
                self._lock.release()
        return redis_client

_redisCli = RedisCLI()

# It's being used in task_helper
def get_celery_redis_client():
    host=appconfig.get_celery_broker_redis_host()
    port=int(appconfig.get_celery_broker_redis_port())
    db=int(appconfig.get_celery_broker_redis_db())
    print(f"get_celery_redis_client: host: {host}, port: {port}, db: {db}")
    return Redis(host=host, port=port, db=db)

def get_redis_client(store_id):
    return _redisCli.get_redis_client(store_id)

def dqueue_webhook_product_update(store_id):
    qkey = PRODUCT_UPDATE_QUEUE + str(store_id)
    cli = get_redis_client(store_id)
    size = cli.llen(qkey)
    result = []
    if size > 0:
        skey = PRODUCT_UPDATE_SET + str(store_id)
        elements = cli.rpop(qkey, size)
        product_set = {}
        for el in elements:
            content = json.loads(el.decode('utf-8'))
            product_id = content['product_id']
            cli.srem(skey, product_id) 
            if not product_id in product_set:
                product_set[product_id] = True
                result.append(content)
    return result

def dqueue_webhook_customer_updates(store_id):
    qkey = CUSTOMER_UPDATE_QUEUE + str(store_id)
    cli = get_redis_client(store_id)
    size = cli.llen(qkey)
    result = []
    if size > 0:
        skey = CUSTOMER_UPDATE_SET + str(store_id)
        elements = cli.rpop(qkey, size)
        if elements:
            customer_set = {}
            for el in elements:
                content = json.loads(el.decode('utf-8'))
                customer_id = content['data']['id']
                cli.srem(skey, customer_id) 
                if not customer_id in customer_set:
                    customer_set[customer_id] = True
                    result.append(content)
    return result

def dqueue_webhook_customer_delete(store_id):
    cli = get_redis_client(store_id)
    qkey = CUSTOMER_DELETE_QUEUE + str(store_id)
    customer_ids = []
    while True:
        customer_id = cli.rpop(qkey)
        if customer_id is None:
            break
        customer_ids.append(customer_id)
    return customer_ids


def flush_db(store_id):
    get_redis_client(store_id).flushdb()

def get_hset_json(store_id, hset_name, key):
    payload = None
    content = get_redis_client(store_id).hget(hset_name, key)
    if content:
        payload = json.loads(content.decode('utf-8'))
    return payload

def get_graphql_token(store_id):
    return get_hset_json(store_id, str(store_id), GRAPHQL_TOKEN_HSET)

def update_graphql_token(store_id, token, expires_at):
    payload = {
        "token": token,
        "expires_at": expires_at
    }
    content = json.dumps(payload)
    client = get_redis_client(store_id)
    client.hset(str(store_id), GRAPHQL_TOKEN_HSET, content)

def get_new_products(store_id):
    return get_hset_json(store_id, str(store_id), NEW_PRODUCTS_HSET)

def get_featured_products(store_id):
    return get_hset_json(store_id, str(store_id), FEATURED_PRODUCTS_HSET)

def get_popular_products(store_id):
    return get_hset_json(store_id, str(store_id), POPULAR_PRODUCTS_HSET)

def get_preorder_products(store_id):
    return get_hset_json(store_id, str(store_id), PREORDER_PRODUCTS_HSET)

def get_home_products(store_id):
    content = {
        "new": get_new_products(store_id),
        "featured": get_featured_products(store_id),
        "popular": get_popular_products(store_id),
        "preorder": get_preorder_products(store_id)
    }
    return content

def update_product_cache(store_id, new_products, featured_products, popular_products, preorder_products):
    cli = get_redis_client(store_id)
    
    if not new_products:
        new_products = []
    cli.hset(str(store_id), NEW_PRODUCTS_HSET, json.dumps(new_products))

    if not featured_products:
        featured_products = []
        
    cli.hset(str(store_id), FEATURED_PRODUCTS_HSET, json.dumps(featured_products))
    
    if not popular_products:
        popular_products = []
    cli.hset(str(store_id), POPULAR_PRODUCTS_HSET, json.dumps(popular_products))
    
    if not preorder_products:
        preorder_products = []
    cli.hset(str(store_id), PREORDER_PRODUCTS_HSET, json.dumps(preorder_products))

def clear_product_cache(store_id):
    cli = get_redis_client(store_id)
    cli.hdel(str(store_id), [NEW_PRODUCTS_HSET, FEATURED_PRODUCTS_HSET, POPULAR_PRODUCTS_HSET, PREORDER_PRODUCTS_HSET])

def update_product_list_pricing(store_id, customer_group_id, pricing):
    hkey = CUSTOMER_GROUP_PRICING + str(store_id)
    content = json.dumps(pricing)
    get_redis_client(store_id).hset(hkey, str(customer_group_id), content)

def update_sku_invetory_cache(store_id, sku_inventory):
    hkey = SKU_INVENTORY + str(store_id)
    cli = get_redis_client(store_id)
    for sku, inventory in sku_inventory.items():
        cli.hset(hkey, sku, inventory)

def update_brand_cache(store_id, brands):
    hkey = BRANDS_HSET + str(store_id)
    client = get_redis_client(store_id)
    for brand in brands:
        content = json.dumps(brand)
        client.hset(hkey, str(brand["id"]), content)

def get_brand_by_id(store_id, brand_id):
    hkey = BRANDS_HSET + str(store_id)
    content = get_redis_client(store_id).hget(hkey, str(brand_id))
    if content:
        content = json.loads(content.decode('utf-8'))
    return content

def update_store_secret(store_id, payload, ttl=0):
    hkey = JWT_SECRET_HSET + str(store_id)
    content = json.dumps(payload)
    client = get_redis_client(store_id)
    client.hset(hkey, str(store_id), content)
    if ttl > 0:
        r.expire(name=hkey, time=ttl)

def get_store_secret(store_id):
    hkey = JWT_SECRET_HSET + str(store_id)
    content = get_redis_client(store_id).hget(hkey, str(store_id))
    if content:
        content = json.loads(content.decode('utf-8'))
    return content

def dequeue_cart_delete(store_id):
    qkey = CART_DELETE_QUEUE + str(store_id)
    cli = get_redis_client(store_id)
    size = cli.llen(qkey)
    result = []
    if size > 0:
        elements = cli.rpop(qkey, size)
        for el in elements:
            result.append(str(el.decode('utf-8')))
    return result

def queue_cart_delete(store_id, cart_id):
    qkey = CART_DELETE_QUEUE + str(store_id)
    get_redis_client(store_id).lpush(qkey, cart_id)

def update_customer_price_mapping(store_id, mapping):
    hkey = GSHEET_MAPPING_HSET + str(store_id)
    cli = get_redis_client(store_id)

    existing_customers = {}
    for customer_id in cli.hgetall(hkey):
        existing_customers[str(customer_id.decode('utf-8'))] = True
    
    for customer_id, products in mapping.items():
        c_id = str(customer_id)
        if c_id in existing_customers:
            del existing_customers[c_id]

        content = json.dumps(products)
        cli.hset(hkey, str(customer_id), content)

    for customer_id in existing_customers:
        cli.hdel(hkey, customer_id)
    
def _remove_forgot_password_token(store_id, email): 
    get_redis_client(store_id).delete(FROGOT_PASSWORD_HSET + email)

def add_forgot_password_token(store_id, email, token, expires_at):
    _remove_forgot_password_token(store_id, email)
    time_to_expire = expires_at - int(datetime.now().timestamp())    
    get_redis_client(store_id).set(FROGOT_PASSWORD_HSET + email, token, ex=time_to_expire)

def remove_new_account_token(store_id, email): 
    get_redis_client(store_id).delete(NEW_ACCOUNT_TOKEN_HSET + email)

def add_new_account_token(store_id, email, token, expires_at):
    remove_new_account_token(store_id, email)
    time_to_expire = expires_at - int(datetime.now().timestamp())    
    get_redis_client(store_id).set(NEW_ACCOUNT_TOKEN_HSET + email, token, ex=time_to_expire)

def update_salesforce_token(store_id, token):
    get_redis_client(store_id).set(SALESFORCE_TOKEN_KEY, token)

def get_salesforce_token(store_id):
    token = get_redis_client(store_id).get(SALESFORCE_TOKEN_KEY)
    if token:
        token = token.decode('utf-8')
    return token

def update_salesforce_customers(store_id, customers={}):
    for customer in customers:
        mapping_json = json.dumps(customer)
        get_redis_client(store_id).hset(SALESFORCE_CUSTOMERS_KEY, str(customer['_id']), mapping_json)

def update_variant_visibility_rules(store_id, rules=[]):
    for rule in rules:
        variant_id = rule['variant_id']
        rule_json = json.dumps(rule)
        get_redis_client(store_id).hset(RULE_ENGINE_VARIANT_VISIBILITY_RULES_KEY, str(variant_id), rule_json)

def rebuild_product_visibility_rules(store_id, rules=[]):
    cli = get_redis_client(store_id)
    cli.delete(RULE_ENGINE_PRODUCT_VISIBILITY_RULES_KEY)
    for rule in rules:
        product_id = rule['product_id']
        rule_json = json.dumps(rule)
        cli.hset(RULE_ENGINE_PRODUCT_VISIBILITY_RULES_KEY, str(product_id), rule_json)

def rebuild_variant_visibility_rules(store_id, rules=[]):
    get_redis_client(store_id).delete(RULE_ENGINE_VARIANT_VISIBILITY_RULES_KEY)
    update_variant_visibility_rules(store_id, rules)

def get_salesforce_customer(store_id, customer_id):
    customer = get_redis_client(store_id).hget(SALESFORCE_CUSTOMERS_KEY, str(customer_id))
    if customer:
        customer = json.loads(customer.decode('utf-8'))
    return customer
        
def get_customer_activities(store_id, channel="express", clear=False):
    client = get_redis_client(store_id)
    hkey = MAIN_CUSTOMER_ACTIVITY_HSET
    if channel == "express":
        hkey = EXPRESS_CUSTOMER_ACTIVITY_HSET

    hkey = hkey + str(store_id)

    if clear:
        client.delete(hkey)
        return {} 

    customers = client.hgetall(hkey)
    activities = {}
    if customers:
        for customer_id, content in customers.items():
            customer_id = customer_id.decode('utf-8')
            activities[customer_id] = json.loads(content.decode('utf-8'))

    return activities

def update_zoho_token(store_id, token):
    get_redis_client(store_id).set(ZOHO_ACCESS_TOKEN, token)

def get_zoho_token(store_id):
    token = get_redis_client(store_id).get(ZOHO_ACCESS_TOKEN)
    if token:
        token = token.decode('utf-8')
    return token