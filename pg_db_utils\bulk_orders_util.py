import datetime
from mongo_db import product_db, store_db
from pg_db_utils.pg_order_util import fetch_order_line_items, fetch_order_shipping_address
from plugin import bc_order
from sqlalchemy import text
import logging
import csv
import io
import pytz
from utils import email_util, store_util
import pg_db
from zoneinfo import ZoneInfo

logger = logging.getLogger()

def get_all_product_using_variant_ids(db_conn, variant_ids):
    query = f"SELECT bop_id, bc_sku, bc_variant_id from bo_product_variants where bc_variant_id IN ({variant_ids});"
    p_ids = db_conn.execute(text(query))
    ids = []
    bop_id_array = {}
    if p_ids:
        for row in p_ids:
            ids.append(row[2])
            bop_id_array[row[2]] = row[0]
    return ids, bop_id_array

def get_customer_rep_emails(db_conn, bop_ids, vairant_ids, variant_stock):
    query = f"""SELECT distinct(pol.bc_variant_id), pol.bc_sku, pol.bop_id, pol.option, po.po_id, po.customer_rep_id, po.customer_rep_name, po.customer_rep_email, bop.bc_sku, bop.bc_name, po.customer_name 
                from bo_purchase_orders as po join bo_purchase_order_lineitems as pol on po.po_id = pol.po_id join bo_bulk_order_products as bop on pol.bop_id = bop.bop_id where pol.bop_id IN ({bop_ids}) and pol.bc_variant_id IN ({vairant_ids})
                group by pol.bc_variant_id, pol.bc_sku, pol.bop_id, pol.option, bop.bc_sku, bop.bc_name, po.po_id, po.customer_rep_id, po.customer_rep_name, po.customer_rep_email, po.customer_name"""
    result = db_conn.execute(text(query))
    data = {}
    if result:
        for row in result:
            test = {
                'variant_id': row[0],
                'variant_sku': row[1],
                'bop_id': row[2],
                'option': row[3],
                'po_id': row[4],
                'customer_rep_id': row[5],
                'customer_rep_name': row[6],
                'customer_rep_email': row[7],
                'product_sku': row[8],
                'product_name': row[9],
                'stock': variant_stock[row[0]],
                'customer_name': row[10]
            }
            if row[7] not in data:
                data[row[7]] = {}
            if row[2] not in data[row[7]]:
                data[row[7]][row[2]] = []
            data[row[7]][row[2]].append(test)
    return data
    

def get_product_customer_tracking_report_csv(store_id, query_params):
    response = {
        "status": 400,
    }
    conn = pg_db.get_connection(store_id)
    try:
        store = store_util.get_store_by_id(store_id)
        user_details = store_db.fetch_user_by_username(store_id, query_params['username'])
        user_name = user_details['name'] if 'name' in user_details and user_details else ''

        search = query_params.get('search', '')
        customer_rep = query_params.get('customer_rep', '')
        tags = query_params.get('tags', '')
        sort_by = query_params.get('sort_by', '').strip()
        sort_array = sort_by.split("/") if sort_by != '' else []
        state = query_params.get('state', '')
        bop_id = query_params.get('bop_id', '')
            
        # get all the variants of product
        total_variant_query = f"""SELECT COUNT(DISTINCT v.variants_id) AS total_variants FROM variants v
                                INNER JOIN bo_bulk_order_products bop ON v.product_id = bop.bc_product_id WHERE bop.bop_id = {bop_id}"""
        total_variant_res = conn.execute(text(total_variant_query)).scalar()
        total_variant_count = str(total_variant_res) if total_variant_res else "0"

        # Conditions to filter the query
        conditions = []

        if customer_rep:
            conditions.append(f"scr.rep_email = '{customer_rep}'")

        if search:
            search = search.strip().replace(" ", "")
            conditions.append(f"(cu.first_name ILIKE '%{search}%' OR cu.last_name ILIKE '%{search}%' OR REPLACE(CONCAT(cu.first_name, ' ', cu.last_name), ' ', '') ILIKE '%{search}%' OR cu.email ILIKE '%{search}%')")

        if tags:
            tags_list = [value.strip() for value in tags.split(',')]
            tags_str = "','".join(tags_list)
            conditions.append(f"gtm.tag_id IN ('{tags_str}')")
        
        if state:
            state = state.strip()
            conditions.append(f"ca.state = '{state}'")

        where_clause = f"AND {' AND '.join(conditions)}" if conditions else ""

        base_query = f"""WITH UniqueOrders AS (
                                SELECT DISTINCT pbo.bc_order_id, pbo.created_at, po.customer_id
                                FROM bo_purchase_order_bc_order_mapping pbo
                                JOIN bo_purchase_orders po ON po.po_id = pbo.po_id
                                JOIN bo_purchase_order_lineitems pol ON pol.po_id = po.po_id
                                JOIN order_line_items ol ON pbo.bc_order_id::INT = ol.order_id
                                JOIN bo_bulk_order_products bop ON ol.product_id = bop.bc_product_id
                                WHERE bop.bop_id = {bop_id} AND pol.fullfilled_qty > 0 
                                AND (pol.status = 'completed' OR (pol.status = 'cancelled' AND pol.fullfilled_qty > 0))
                            ),
                            open_orders AS (
                                SELECT po.customer_id, COUNT(DISTINCT po.po_id) AS open_orders
                                FROM bo_purchase_orders po
                                JOIN bo_purchase_order_lineitems pol ON pol.po_id = po.po_id
                                WHERE pol.bop_id = {bop_id} AND po.status NOT IN ('deleted', 'completed') 
                                AND (pol.status IS NULL OR pol.status NOT IN ('cancelled', 'completed'))
                                GROUP BY po.customer_id
                            ),
                            aggregated_orders AS (
                                SELECT customer_id, COUNT(bc_order_id) AS order_count, MAX(created_at) AS last_order_date
                                FROM UniqueOrders GROUP BY customer_id
                            ),
                            bulk_total_revenue AS (
                                SELECT uo.customer_id, COALESCE(SUM(ol.total_inc_tax), 0) AS order_total
                                FROM UniqueOrders uo
                                JOIN orders o ON uo.bc_order_id::INT = o.order_id
                                JOIN order_line_items ol ON o.order_id = ol.order_id
                                JOIN bo_bulk_order_products bop ON ol.product_id = bop.bc_product_id
                                WHERE bop.bop_id = {bop_id} GROUP BY uo.customer_id
                            ),
                            lifetime_product_orders AS (
                                SELECT o.customer_id, COUNT(DISTINCT o.order_id) AS product_order_count, COALESCE(SUM(ol.total_inc_tax), 0) AS product_order_total, 
                                    MAX(o.order_created_date_time) AS product_last_order_date, COUNT(DISTINCT ol.variant_id) AS purchased_flavours
                                FROM orders o
                                JOIN order_line_items ol ON o.order_id = ol.order_id
                                JOIN bo_bulk_order_products bop ON ol.product_id = bop.bc_product_id
                                WHERE bop.bop_id = {bop_id} AND o.order_status_id NOT IN (0, 4, 5, 6, 7, 13) GROUP BY o.customer_id
                            ),
                            lifetime_total_orders AS (
                                SELECT customer_id, COUNT(DISTINCT o.order_id) AS lifetime_order_count, COALESCE(SUM(o.total_including_tax), 0) AS lifetime_order_total
                                FROM orders o GROUP BY customer_id
                            ),
                            customer_addresses AS (
                                SELECT DISTINCT ON (customer_id) customer_id, state 
                                FROM customer_addresses 
                                ORDER BY customer_id, customer_address_id DESC
                            )
                            SELECT 
                                cu.customer_id, cu.email, cu.first_name, cu.last_name, CONCAT(cu.first_name, ' ', cu.last_name) AS name, 
                                scr.rep_name, scr.rep_email, STRING_AGG(DISTINCT gtm.tag_label, ', ') AS tag_labels, 
                                COALESCE(ao.order_count, 0) AS order_count, COALESCE(btr.order_total, 0) AS order_total, 
                                ca.state, COALESCE(lpo.product_order_count, 0) AS product_order_count, COALESCE(lpo.product_order_total, 0) AS product_order_total, 
                                lpo.product_last_order_date, COALESCE(oo.open_orders, 0) AS open_orders, 
                                COALESCE(lto.lifetime_order_count, 0) AS lifetime_order_count, 
                                COALESCE(lto.lifetime_order_total, 0) AS lifetime_order_total, 
                                COALESCE(lpo.purchased_flavours, 0) AS purchased_flavours,
                                cu.company AS company_name
                            FROM customers cu
                            LEFT JOIN customer_addresses ca ON cu.customer_id = ca.customer_id
                            LEFT JOIN bo_generic_tags_mapping gtm ON gtm.entity_int_id = cu.customer_id AND gtm.entity_type = 'customer'
                            LEFT JOIN salesforce_customer_rep scr ON cu.customer_id = scr.customer_id
                            LEFT JOIN aggregated_orders ao ON cu.customer_id = ao.customer_id
                            LEFT JOIN lifetime_product_orders lpo ON cu.customer_id = lpo.customer_id
                            LEFT JOIN lifetime_total_orders lto ON cu.customer_id = lto.customer_id
                            LEFT JOIN open_orders oo ON cu.customer_id = oo.customer_id
                            LEFT JOIN bulk_total_revenue btr ON cu.customer_id = btr.customer_id
                            WHERE gtm.entity_type = 'customer' {where_clause}
                            GROUP BY cu.customer_id, cu.email, cu.first_name, cu.last_name, ca.state, scr.rep_name, scr.rep_email, ao.order_count, btr.order_total, 
                                lpo.product_order_count, lpo.product_order_total, lpo.product_last_order_date, oo.open_orders, lto.lifetime_order_count, lto.lifetime_order_total, lpo.purchased_flavours """
        
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
            if sort_array[0] in ['name', 'rep_name', 'order_count', 'product_last_order_date', 'tag_labels', 'order_total', 'state', 'lifetime_order_count', 'lifetime_order_total', 'open_orders', 'product_order_count', 'product_order_total', 'purchased_flavours', 'company_name']:                
                if sort_direction == 'ASC':
                    base_query += f" ORDER BY {sort_array[0]} {sort_direction} NULLS FIRST"
                else:
                    base_query += f" ORDER BY {sort_array[0]} {sort_direction} NULLS LAST"
 
        result = conn.execute(text(base_query))

        data = []
        cst = ZoneInfo("America/Chicago")
        for row in result.fetchall():         
            products_data = {
                'CUSTOMER ID': row[0],
                "CUSTOMER EMAIL": row[1],
                "CUSTOMER NAME": row[4],
                "COMPANY NAME": row[18],            
                "STATE": row[10],
                "CUSTOMER REP": row[5],
                "REP EMAIL": row[6],
                "CUSTOMER TAG": row[7],
                "OPEN BULK ORDERS": row[14],
                "FLAVORS": (str(row[17]) if row[17] else "0") + '/' + str(total_variant_count),   
                "THIS PRODUCT ORDERS": row[11],
                "THIS PRODUCT REVENUE - 2 YEARS": round(row[12], 2) if row[12] else 0,
                "TOTAL REVENUE - 2 YEARS": round(row[16], 2) if row[16] else 0,
                "LAST BC ORDER DATE(CST)": row[13].astimezone(cst).strftime("%b %d, %Y, %I:%M %p") if isinstance(row[13], datetime.datetime) else str(row[13])
            }
            data.append(products_data) 
                
        # Create CSV content in memory
        if data:
            utc_now = datetime.datetime.now(pytz.utc)
            cst_now = utc_now.astimezone(pytz.timezone('America/Chicago'))
            created_date = cst_now .strftime("%m-%d-%Y_%H:%M")
            file_name = f'Customer_Tracking_#{bop_id}_{created_date}.csv'
            csv_content = io.StringIO()
            keys = list(data[0].keys())
            writer = csv.DictWriter(csv_content, fieldnames=keys)
            writer.writeheader()
            for row in data:
                writer.writerow(row)

            email_util.send_product_customer_tracking_report_email(store_id, csv_content, query_params['username'], file_name, user_name, created_date)
    
    finally:
        conn.close()

def get_bulk_products_gloabl_report_csv(store_id, query_params):
    conn = pg_db.get_connection(store_id)
    try:
        user_details = store_db.fetch_user_by_username(store_id, query_params['username'])
        user_name = user_details['name'] if 'name' in user_details and user_details else ''

        search = query_params.get('search', '')
        customer_rep = query_params.get('customer_rep', '')
        sort_by = query_params.get('sort_by', '').strip()
        sort_array = sort_by.split("/") if sort_by != '' else []
        state = query_params.get('state', '')
        bop_ids = query_params.get('product_filter', '').strip()
        sorting_bop_id = query_params.get('sorting_bop_id', '')

        customer_data = []
        product_map = {}
        all_products = []
        # Conditions to filter the query
        conditions = []

        if customer_rep:
            conditions.append(f"scr.rep_email = '{customer_rep}'")

        if search:
            search = search.strip().replace(" ", "")
            conditions.append(f"(cu.first_name ILIKE '%{search}%' OR cu.last_name ILIKE '%{search}%' OR REPLACE(CONCAT(cu.first_name, ' ', cu.last_name), ' ', '') ILIKE '%{search}%' OR cu.email ILIKE '%{search}%')")
        
        if state:
            state = state.strip()
            conditions.append(f"ca.state = '{state}'")

        bop_ids_list = None
        if bop_ids:
            bop_ids_list = [int(value.strip()) for value in bop_ids.split(';')]

        where_clause = f"AND {' AND '.join(conditions)}" if conditions else ""

        # conditional append queries for sorting based on product fields
        append_columns = ""
        conditional_query = ""
        conditional_join = ""
        if sorting_bop_id:
            conditional_query = f""", lifetime_product_orders AS (
                            SELECT o.customer_id, COUNT(DISTINCT o.order_id) AS product_order_count, COALESCE(SUM(ol.total_inc_tax), 0) AS product_order_total, 
                                MAX(o.order_created_date_time) AS product_last_order_date, COUNT(DISTINCT ol.variant_id) AS purchased_flavours
                            FROM orders o
                            JOIN order_line_items ol ON o.order_id = ol.order_id
                            JOIN bo_bulk_order_products bop ON ol.product_id = bop.bc_product_id
                            WHERE bop.bop_id = {sorting_bop_id} AND o.order_status_id NOT IN (0, 4, 5, 6, 7, 13) GROUP BY o.customer_id
                        )"""
            append_columns = f""", COALESCE(lpo.product_order_count, 0) AS product_order_count, COALESCE(lpo.product_order_total, 0) AS product_order_total, lpo.product_last_order_date, COALESCE(lpo.purchased_flavours, 0) AS purchased_flavours""" 
            conditional_join = f"""LEFT JOIN lifetime_product_orders lpo ON cu.customer_id = lpo.customer_id"""
        
        # Customers query
        customer_query = f"""WITH customer_addresses AS (
                            SELECT DISTINCT ON (customer_id) customer_id, state FROM customer_addresses ORDER BY customer_id, customer_address_id DESC
                        ),
                        lifetime_total_orders AS (
                            SELECT customer_id, COUNT(DISTINCT o.order_id) AS lifetime_order_count, COALESCE(SUM(o.total_including_tax), 0) AS lifetime_order_total
                            FROM orders o GROUP BY customer_id
                        )
                        {conditional_query}
                        SELECT 
                            cu.customer_id, cu.email, cu.first_name, cu.last_name, CONCAT(cu.first_name, ' ', cu.last_name) AS name, 
                            scr.rep_name, scr.rep_email, ca.state, cu.company AS company_name,
                            COALESCE(lto.lifetime_order_count, 0) AS lifetime_order_count, 
                            COALESCE(lto.lifetime_order_total, 0) AS lifetime_order_total
                            {append_columns}
                        FROM customers cu
                        LEFT JOIN customer_addresses ca ON cu.customer_id = ca.customer_id
                        LEFT JOIN bo_generic_tags_mapping gtm ON gtm.entity_int_id = cu.customer_id AND gtm.entity_type = 'customer'
                        LEFT JOIN salesforce_customer_rep scr ON cu.customer_id = scr.customer_id
                        LEFT JOIN lifetime_total_orders lto ON cu.customer_id = lto.customer_id
                        {conditional_join}
                        WHERE gtm.entity_type = 'customer' {where_clause}"""
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
            if sort_array[0] in ['name', 'rep_name', 'state', 'company_name', 'lifetime_order_count', 'lifetime_order_total', 'product_last_order_date', 'product_order_count', 'product_order_total', 'purchased_flavours']:                
                if sort_direction == 'ASC':
                    customer_query += f" ORDER BY {sort_array[0]} {sort_direction} NULLS FIRST"
                else:
                    customer_query += f" ORDER BY {sort_array[0]} {sort_direction} NULLS LAST"

        customer_result = conn.execute(text(customer_query)).fetchall()

        if bop_ids_list:
            total_variant_counts = {}
            # get all the variants of product
            total_variant_query = f"""SELECT bop.bop_id, COUNT(DISTINCT v.variants_id) AS total_variants FROM variants v
                                    INNER JOIN bo_bulk_order_products bop ON v.product_id = bop.bc_product_id WHERE (:bop_ids IS NULL OR bop.bop_id = ANY(:bop_ids)) GROUP BY bop.bop_id"""
            total_variant_res = conn.execute(text(total_variant_query), {"bop_ids": bop_ids_list}).fetchall()
            if total_variant_res:
                for row in total_variant_res:
                    total_variant_counts[str(row[0])] = str(row[1]) if row[1] else "0"

            # Fetch all unique products
            all_products_query = f"""SELECT bop.bop_id, bop.bc_name 
                                    FROM bo_bulk_order_products bop
                                    WHERE (:bop_ids IS NULL OR bop.bop_id = ANY(:bop_ids))"""
            all_products_result = conn.execute(text(all_products_query), {"bop_ids": bop_ids_list}).fetchall()
            all_products = [{
                "bop_id": row[0],
                "product_name": row[1],
                "product_order_count": 0,
                "product_order_total": 0,
                "product_last_order_date": None,
                "purchased_flavours": "0" + '/' + str(total_variant_counts[str(row[0])])
            } for row in all_products_result]
            # Sort the products by the order of bop_ids in bop_ids_list
            bop_id_order = {bop_id: index for index, bop_id in enumerate(bop_ids_list)}
            all_products.sort(key=lambda x: bop_id_order.get(x["bop_id"], float('inf')))

            # Product query
            product_query = f"""WITH lifetime_product_orders AS (
                                    SELECT o.customer_id, bop.bop_id,  o.order_id, COALESCE(SUM(ol.total_inc_tax), 0) AS product_order_total,
                                        MAX(o.order_created_date_time) AS product_last_order_date
                                    FROM orders o
                                    JOIN order_line_items ol ON o.order_id = ol.order_id
                                    JOIN bo_bulk_order_products bop ON ol.product_id = bop.bc_product_id
                                    WHERE (:bop_ids IS NULL OR bop.bop_id = ANY(:bop_ids)) AND o.order_status_id NOT IN (0, 4, 5, 6, 7, 13) GROUP BY o.customer_id, bop.bop_id, o.order_id
                                ),
                                total_purchased_flavours AS (
                                    SELECT o.customer_id, bop.bop_id, COUNT(DISTINCT ol.variant_id) AS purchased_flavours
                                    FROM orders o
                                    JOIN order_line_items ol ON o.order_id = ol.order_id
                                    JOIN bo_bulk_order_products bop ON ol.product_id = bop.bc_product_id
                                    WHERE (:bop_ids IS NULL OR bop.bop_id = ANY(:bop_ids)) AND o.order_status_id NOT IN (0, 4, 5, 6, 7, 13) GROUP BY o.customer_id, bop.bop_id
                                )
                                SELECT
                                    lpo.customer_id, bop.bop_id, bop.bc_name, COUNT(DISTINCT lpo.order_id) AS product_order_count,
                                    SUM(lpo.product_order_total) as order_total, MAX(lpo.product_last_order_date) as last_order_date, COALESCE(tpf.purchased_flavours, 0) AS purchased_flavours
                                FROM lifetime_product_orders lpo
                                JOIN bo_bulk_order_products bop ON bop.bop_id = lpo.bop_id
                                JOIN total_purchased_flavours tpf ON tpf.customer_id = lpo.customer_id AND tpf.bop_id = lpo.bop_id
                                WHERE (:bop_ids IS NULL OR bop.bop_id = ANY(:bop_ids))
                                GROUP BY lpo.customer_id, bop.bop_id, bop.bc_name, tpf.purchased_flavours"""
            
            product_result = conn.execute(text(product_query), {"bop_ids": bop_ids_list}).fetchall()

            cst = ZoneInfo("America/Chicago")
            for row in product_result:
                customer_id = row[0]
                bop_id = row[1]
                if customer_id not in product_map:
                    product_map[customer_id] = []
                product_map[customer_id].append({
                    "bop_id": bop_id,
                    "product_name": row[2],
                    "product_order_count": row[3],
                    "product_order_total": round(row[4], 2) if row[4] else 0,
                    "product_last_order_date": row[5].astimezone(cst).strftime("%b %d, %Y, %I:%M %p") if isinstance(row[5], datetime.datetime) else str(row[5]),
                    "purchased_flavours": (str(row[6]) if row[6] else "0") + '/' + str(total_variant_counts[str(bop_id)])
                })

        # Fill missing products with dummy data
        if bop_ids_list:
            for customer_id in product_map:
                existing_bop_ids = {product["bop_id"] for product in product_map[customer_id]}
                missing_products = [product for product in all_products if product["bop_id"] not in existing_bop_ids]
                product_map[customer_id].extend(missing_products)
                # Sort the products by the order of bop_ids in bop_ids_list
                bop_id_order = {bop_id: index for index, bop_id in enumerate(bop_ids_list)}
                product_map[customer_id].sort(key=lambda x: bop_id_order.get(x["bop_id"], float('inf')))

        for row in customer_result:     
            customer_id = row[0]    
            customer_data.append({
                'customer_id': row[0],
                "email": row[1],
                "name": row[4],
                "company_name": row[8],
                "state": row[7],  
                "rep_name": row[5],
                "rep_email": row[6],
                "lifetime_order_total": round(row[10], 2) if row[10] else 0,
                "products": product_map.get(customer_id, all_products)       
            })
        
        if customer_data:
            transform_data = transform_customer_data(customer_data)
            utc_now = datetime.datetime.now(pytz.utc)
            cst_now = utc_now.astimezone(pytz.timezone('America/Chicago'))
            created_date = cst_now .strftime("%m-%d-%Y_%H:%M")
            file_name = f'Bulk_Products_Report_{created_date}.csv'
            csv_content = io.StringIO()
            keys = list(transform_data[0].keys()) 
            writer = csv.DictWriter(csv_content, fieldnames=keys)
            writer.writeheader()
            for row in transform_data:
                writer.writerow(row)

            email_util.send_bulk_products_gloabl_report_email(store_id, csv_content, query_params['username'], file_name, user_name, created_date)
        
    finally:
        if conn:
            conn.close()

def transform_customer_data(customers):
    transformed_data = []

    for customer in customers:
        transformed_customer = {
            "CUSTOMER ID": customer["customer_id"],
            "CUSTOMER EMAIL": customer["email"],
            "CUSTOMER NAME": customer["name"],
            "COMPANY NAME": customer["company_name"],
            "STATE": customer["state"],
            "CUSTOMER REP": customer["rep_name"],
            "REP EMAIL": customer["rep_email"],
            "CLR": customer["lifetime_order_total"]
        }

        for product in customer.get("products", []):
            bop_id = product["bop_id"]
            transformed_customer[f"BOP #{bop_id}_FLAVOUR ({product['product_name']})"] = product["purchased_flavours"]
            transformed_customer[f"BOP #{bop_id}_ORDER ({product['product_name']})"] = product["product_order_count"]
            transformed_customer[f"BOP #{bop_id}_REVENUE ({product['product_name']})"] = product["product_order_total"]
            transformed_customer[f"BOP #{bop_id}_LAST ORDER DATE(CST) ({product['product_name']})"] = product["product_last_order_date"]

        transformed_data.append(transformed_customer)

    return transformed_data

def send_order_placed_email_to_compliance_team(store_id, order_id):
    store = store_util.get_store_by_id(store_id)
    if store:
        res = bc_order.fetch_order(store, order_id) 
        if res.status_code < 299:
            order = res.json()
            billing_address = {}
            shipping_address = []
            line_items = []
            if "billing_address" in order:
                billing_address = order["billing_address"]
            
            _shipping_address = fetch_order_shipping_address(order["id"], order['date_modified'], store)
            if _shipping_address and len(_shipping_address) > 0:
                shipping_address = _shipping_address

            _line_items, _discounts = fetch_order_line_items(store, order["id"], order['date_modified'])
            if _line_items and len(_line_items) > 0:
                line_items = _line_items
            
            product_images = product_db.get_product_images_by_id(store, [p['product_id'] for p in line_items])
            product_images_dict = {item["id"]: item.get("images", []) for item in product_images}

            for product in line_items:
                product_id = product.get("product_id")
                images = product_images_dict.get(product_id, [])

                # Find the image with is_thumbnail=True, fallback to first image
                thumbnail_image = next((img for img in images if img.get("is_thumbnail")), images[0] if images else None)

                # Assign the thumbnail URL or an empty string if no image found
                product["image"] = thumbnail_image.get("url_thumbnail", "") if thumbnail_image else ""

            email_util.send_order_placed_email_to_compliance(store_id, order, billing_address, shipping_address, line_items)
