from utils import bc_util, store_util
import plugin
from mongo_db import customer_db
import logging
from pymongo import TEXT

logger = logging.getLogger()

def process_bc_customers(store, customers=[]):
    result = []
    for customer in customers:
        first_name = customer['first_name'].strip()
        last_name = customer['last_name'].strip()
        name = first_name + " " + last_name
        obj = {
            "id": customer["id"],
            "email": customer["email"],
            "first_name": first_name,
            "last_name": last_name,
            "name": name,
            "phone": customer['phone'],
            "company": customer['company'],
            "storecredit": customer.get("store_credit_amounts", None),
            "notes": customer['notes'],
            "date_created": customer['date_created'],
            "search_field": customer['email'] + name + " " + customer['company'] + " " + str(customer['id']),
            "customer_group_id": customer["customer_group_id"]
        }
        result.append(obj)
    return result

def fetch_all_customers(store):
    if not store:
        return None
    query_params = {
        "include": "storecredit",
        "limit": 250,
        "page": 0
    }
    api = "v3/customers"
    return plugin.fetch_all_by_rest_api(store, api, limit_per_req=250, query_params=query_params, 
            db_collection=customer_db.CUSTOMERS_COLLECTION,db_process_threshold=250, max_resource_count=-1, resource_processor=process_bc_customers)

def fetch_all_customer_groups(store):
    query_params = {
        "limit": 250,
        "page": 0
    }
    api = "v2/customer_groups"
    return plugin.fetch_all_by_rest_api(store, api, limit_per_req=250, query_params=query_params, 
            db_collection=customer_db.CUSTOMER_GROUPS_COLLECTION, db_process_threshold=250)

def fetch_customer_by_id(store, customer_id):
    query_params = {
        "include": "storecredit",
        "id:in": str(customer_id)
    }
    url = "v3/customers"
    bc_api = store_util.get_bc_api_creds(store)
    return bc_util.call_api(bc_api, "GET", url, query_params)

def update_customers(store, customers=[]):
    #print(f"update_customers {len(customers)}")
    n = len(customers)
    if n == 0:
        return 
    
    query_params = {
        "include": "storecredit",
        "limit": 250,
        "page": 0
    }
    api = "v3/customers"

    customer_list = []
    batch_size = 50
    
    for idx, id in enumerate(customers):
        customer_list.append(str(id))
        if (len(customer_list) == batch_size) or (idx == n - 1):
            query_params['id:in'] = ",".join(customer_list)
            #print("updating customer ", query_params)
            plugin.fetch_all_by_rest_api(store, api, limit_per_req=batch_size, query_params=query_params, \
                                        db_collection=customer_db.CUSTOMERS_COLLECTION,db_process_threshold=250, \
                                        max_resource_count=-1, resource_processor=process_bc_customers)
            customer_list = []
    
    if len(customer_list) > 0:
        query_params['id:in'] = ",".join(customer_list)
        plugin.fetch_all_by_rest_api(store, api, limit_per_req=batch_size, query_params=query_params, \
                                    db_collection=customer_db.CUSTOMERS_COLLECTION,db_process_threshold=250, \
                                    max_resource_count=-1, resource_processor=process_bc_customers)
