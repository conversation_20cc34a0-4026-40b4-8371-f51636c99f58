from utils import bc_util, store_util

import logging
import traceback

logger = logging.getLogger()

SYSTEMLOGS_API = "v3/store/systemlogs"

def fetch_payment_errors(bc_api, page=1):
    page = 1
    query_params = {
        "page": page,
        "limit": 250,
        "severity:min": 4,
        "sort": "date_created",
        "direction": "desc"
    }
    res = bc_util.call_api(api_data=bc_api, method="GET", url=SYSTEMLOGS_API, query_params=query_params)
    errors = None
    if res:
        if res.status_code == 200:
            errors = res.json()['data']
        elif res.status_code == 204:
            errors = []
    
    return errors   