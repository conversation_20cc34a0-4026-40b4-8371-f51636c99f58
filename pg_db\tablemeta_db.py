import datetime
from sqlalchemy import Column, DateTime, String
import pg_db as db

class TableMetadata(db.Base):
    __tablename__ = 'table_metadata'

    table_name = Column(String, primary_key=True)
    modified_at = Column(DateTime)

    def __repr__(self):
        return f'TableMetadata {self.table_name}'

    @classmethod
    def get_last_updated_at(cls, store_id, table_name, session=None):
        local_session = None
        if not session:
            session = db.get_session(store_id)
            local_session = session
        try:
            q = session.query(TableMetadata).get(table_name)
            last_modified_at = None
            if q:
                last_modified_at = q.modified_at
            return last_modified_at
        finally:
            if local_session:
                local_session.close()