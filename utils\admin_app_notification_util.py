from mongo_db import store_db
import mongo_db
from bson import ObjectId
import logging
import traceback
logger = logging.getLogger()
from datetime import datetime, timezone, timedelta
import logging
from datetime import timezone
import pg_db
from sqlalchemy import text
from utils import store_util
from plugin import bc_webhook

class AdminAppNotificationUtil:
    ORDER_CREATED = "order_created"
    ORDER_UPDATED = "order_updated"
    PRICE_LIST_RULE_CREATED = "price_list_rule_created"
    PRICE_LIST_RULE_UPDATED = "price_list_rule_updated"
    GUEST_PRODUCT_INQUIRY = "guest_product_inquiry"
    REGISTERED_USER_PRODUCT_INQUIRY = "registered_user_product_inquiry"
    TICKET_CREATED = "ticket_created"
    TICKET_UPDATED = "ticket_updated"
    TICKET_ASSIGNED = "ticket_assigned"
    TICKET_ASSIGNED_BY_MAPPING = "ticket_assigned_by_mapping"
    USER_CREATED = "user_created"
    USER_THEMES_UPDATED = "user_themes_updated"
    CUSTOMER_CREATED = "customer_created"
    PO_CREATED = "po_created"
    ORDER_PLACED = "order_placed"
    UNLOCK_PRODUCT = "unlock_product"
    BLOCK_ORDER_CREATED = "block_order_created"
    JOB_FAILED = "job_failed"

def generate_admin_app_notification(store_id, event_type, entity_id, payload=None):
    if event_type == AdminAppNotificationUtil.ORDER_CREATED or event_type == AdminAppNotificationUtil.ORDER_UPDATED:
        order_created_notification(store_id, entity_id, event_type)
    
    if event_type == AdminAppNotificationUtil.PRICE_LIST_RULE_CREATED or event_type == AdminAppNotificationUtil.PRICE_LIST_RULE_UPDATED:
        price_list_rule_notification(store_id, entity_id, event_type)

    if event_type == AdminAppNotificationUtil.GUEST_PRODUCT_INQUIRY or event_type == AdminAppNotificationUtil.REGISTERED_USER_PRODUCT_INQUIRY:
        product_inquiry_notification(store_id, entity_id, event_type)

    if event_type == AdminAppNotificationUtil.TICKET_CREATED or event_type == AdminAppNotificationUtil.TICKET_UPDATED or event_type == AdminAppNotificationUtil.TICKET_ASSIGNED or event_type == AdminAppNotificationUtil.TICKET_ASSIGNED_BY_MAPPING:
        ticket_notification(store_id, entity_id, event_type)

    if event_type == AdminAppNotificationUtil.USER_CREATED:
        user_created_notification(store_id, entity_id, event_type, payload)

    if event_type == AdminAppNotificationUtil.USER_THEMES_UPDATED:
        user_themes_updated_notification(store_id, entity_id, event_type)

    if event_type == AdminAppNotificationUtil.CUSTOMER_CREATED:
        customer_created_notification(store_id, entity_id, event_type)

    if event_type == AdminAppNotificationUtil.PO_CREATED:
        po_created_notification(store_id, entity_id, event_type)

    if event_type == AdminAppNotificationUtil.ORDER_PLACED:
        order_placed_notification(store_id, entity_id, event_type)

    if event_type == AdminAppNotificationUtil.UNLOCK_PRODUCT:
        unlock_product_notification(store_id, entity_id, event_type)

    if event_type == AdminAppNotificationUtil.BLOCK_ORDER_CREATED:
        block_order_created_notification(store_id, entity_id, event_type)

    if event_type == AdminAppNotificationUtil.JOB_FAILED:
        job_failed_notification(store_id, entity_id, event_type)


def order_created_notification(store_id, order_id, event_type):
    # Step 1: Get the MongoDB and PostgreSQL connections
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    conn = pg_db.get_connection(store_id)

    try:
        rep_name = None
        rep_email = None

        # Step 2: Fetch notification module by event_type
        notification_module = db["notification_modules"].find_one({"event_type": event_type})
        if not notification_module:
            logger.error(f"No notification module found for event_type: {event_type}")
            return

        retention_period_hours = int(notification_module.get("retention_period_hours", 720))
        receivers = notification_module.get("receivers", [])
        module = notification_module.get("module", None)

        # Step 3: Extract user information from receivers
        user_receivers = [
            {"user_id": receiver["id"], "email": receiver["email"]}
            for receiver in receivers if receiver["type"] == "user"
        ]

        # Step 4: Fetch order details from PostgreSQL
        query_order = """
            SELECT customer_id, total_items, total_including_tax, updated_by
            FROM orders
            WHERE order_id = :order_id
        """
        order_result = conn.execute(text(query_order), {"order_id": order_id}).fetchone()
        if not order_result:
            logger.error(f"No order found with order_id: {order_id}")
            return

        customer_id, total_items, total_including_tax, updated_by = order_result

        if event_type == AdminAppNotificationUtil.ORDER_CREATED:
            # Step 5: Fetch customer representative and customer details
            query_rep = """
                SELECT rep_name, rep_email
                FROM salesforce_customer_rep
                WHERE customer_id = :customer_id
            """
            rep_result = conn.execute(text(query_rep), {"customer_id": customer_id}).fetchone()
            if rep_result:
                rep_name, rep_email = rep_result
                user_info = store_db.fetch_user_by_username(store_id, rep_email)
                user_receivers.append({
                    "user_id": str(user_info["_id"]) if user_info else None,
                    "email": rep_email
                })

        query_customer = """
            SELECT first_name, last_name, email
            FROM customers
            WHERE customer_id = :customer_id
        """
        customer_result = conn.execute(text(query_customer), {"customer_id": customer_id}).fetchone()
        if not customer_result:
            logger.error(f"No customer found with customer_id: {customer_id}")
            return

        first_name, last_name, customer_email = customer_result
        customer_name = f"{first_name} {last_name}"


        # Step 6: Create the notification entry
        notification_entry = {
            "module": module,
            "event_type": event_type,
            "order_id": order_id,
            "customer_name": customer_name,
            "customer_email": customer_email,
            "order_value": total_including_tax,
            "line_item_count": total_items,
            "admin_user_name": updated_by,
            "rep_name": rep_name,
            "created_at": int(datetime.now(timezone.utc).timestamp()),
            "retention_period_hours": retention_period_hours,
            "receivers": [
                {
                    "user_id": receiver["user_id"],
                    "email": receiver["email"],
                    "is_read": False,
                    "read_time": None
                }
                for receiver in user_receivers
            ]
        }

        # Step 7: Insert notification entry into notifications_admin collection
        db["notifications_admin"].insert_one(notification_entry)

    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"An error occurred: {str(e)}")
        logger.error(f"Traceback details:\n{error_details}")
    finally:
        if conn:
            conn.close()

def price_list_rule_notification(store_id, rule_id, event_type):
    # Step 1: Get the MongoDB connection
    db = mongo_db.get_admin_db_client_for_store_id(store_id)

    try:
        # Step 2: Fetch notification module by event_type
        notification_module = db["notification_modules"].find_one({"event_type": event_type})
        if not notification_module:
            logger.error(f"No notification module found for event_type: {event_type}")
            return

        retention_period_hours = int(notification_module.get("retention_period_hours", 720))
        receivers = notification_module.get("receivers", [])
        module = notification_module.get("module", None)

        # Step 3: Extract user information from receivers
        user_receivers = [
            {"user_id": receiver["id"], "email": receiver["email"]}
            for receiver in receivers if receiver["type"] == "user"
        ]

        # Step 4: Fetch rule details from price_list_rules collection
        rule_details = db["price_list_rules"].find_one({"_id": ObjectId(rule_id)})
        if not rule_details:
            logger.error(f"No rule found with rule_id: {rule_id}")
            return

        rule_name = rule_details.get("rule_name", "Unnamed Rule")
        email = rule_details.get("created_by", None)
        updated_by = rule_details.get("updated_by", None)

        if email:
            user_info = store_db.fetch_user_by_username(store_id, email)
            user_name = user_info["name"] if user_info else None

        updater_user_name = None
        if updated_by:
            updater_user_info = store_db.fetch_user_by_username(store_id, updated_by)
            updater_user_name = updater_user_info["name"] if updater_user_info else None

        # Step 5: Create the notification entry
        notification_entry = {
            "module": module,
            "event_type": event_type,
            "rule_name": rule_name,
            "admin_user_name": user_name,
            "updater_user_name": updater_user_name,
            "created_at": int(datetime.now(timezone.utc).timestamp()),
            "retention_period_hours": retention_period_hours,
            "receivers": [
                {
                    "user_id": receiver.get("user_id", None),
                    "email": receiver.get("email", None),
                    "is_read": False,
                    "read_time": None
                }
                for receiver in user_receivers
            ]
        }

        # Step 6: Insert notification entry into notifications_admin collection
        db["notifications_admin"].insert_one(notification_entry)

    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"An error occurred: {str(e)}")
        logger.error(f"Traceback details:\n{error_details}")

def product_inquiry_notification(store_id, inquiry_id, event_type):
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    conn = pg_db.get_connection(store_id)
    try:

        # Step 2: Fetch notification module by event_type
        notification_module = db["notification_modules"].find_one({"event_type": event_type})
        if not notification_module:
            logger.error(f"No notification module found for event_type: {event_type}")
            return

        retention_period_hours = int(notification_module.get("retention_period_hours", 720))
        receivers = notification_module.get("receivers", [])
        module = notification_module.get("module", None)

        # Step 3: Extract user information from receivers
        user_receivers = [
            {"user_id": receiver["id"], "email": receiver["email"]}
            for receiver in receivers if receiver["type"] == "user"
        ]

        inquiry_query = """
            SELECT id, customer_id, customer_name, customer_email, product_id 
            FROM product_inquiries 
            WHERE id = :inquiry_id
        """
        inquiry_result = conn.execute(text(inquiry_query), {"inquiry_id": inquiry_id})
        inquiry = inquiry_result.fetchone()

        if not inquiry:
            logger.error(f"No inquiry found for inquiry_id: {inquiry_id}")
            return
        
        # Unpack inquiry details
        inquiry_id, customer_id, customer_name, customer_email, product_id = inquiry

        if event_type == AdminAppNotificationUtil.REGISTERED_USER_PRODUCT_INQUIRY:
            # Step 5: Fetch customer representative and customer details
            query_rep = """
                SELECT rep_name, rep_email
                FROM salesforce_customer_rep
                WHERE customer_id = :customer_id
            """
            rep_result = conn.execute(text(query_rep), {"customer_id": customer_id}).fetchone()
            if rep_result:
                rep_name, rep_email = rep_result
                user_info = store_db.fetch_user_by_username(store_id, rep_email)
                user_receivers.append({
                    "user_id": str(user_info["_id"]) if user_info else None,
                    "email": rep_email
                })

        # Fetch product details from products table
        product_query = "SELECT product_name FROM products WHERE product_id = :product_id"
        product_result = conn.execute(text(product_query), {"product_id": product_id})
        product = product_result.fetchone()

        if not product:
            logger.error(f"No product found for product_id: {product_id}")
            return

        product_name = product[0]

        notification_entry = {
            "module": module,
            "event_type": event_type,
            "inquiry_id": inquiry_id,
            "customer_name": customer_name,
            "customer_email": customer_email,
            "product_name": product_name,
            "created_at": int(datetime.now(timezone.utc).timestamp()),
            "retention_period_hours": retention_period_hours,
            "receivers": [
                {
                    "user_id": receiver["user_id"],
                    "email": receiver["email"],
                    "is_read": False,
                    "read_time": None
                }
                for receiver in user_receivers
            ]
        }

        db["notifications_admin"].insert_one(notification_entry)

    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"An error occurred: {str(e)}")
        logger.error(f"Traceback details:\n{error_details}")    
    finally:
        conn.close()

def ticket_notification(store_id, ticket_id, event_type):
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    conn = pg_db.get_connection(store_id)
    try:
        user_receivers = []
        notification_module = db["notification_modules"].find_one({"event_type": event_type})
        if not notification_module:
            logger.error(f"No notification module found for event_type: {event_type}")
            return

        retention_period_hours = int(notification_module.get("retention_period_hours", 720))
        module = notification_module.get("module", None)

        ticket_query = """
            SELECT project_id, created_by, updated_by, assigned_to, card_identifier
            FROM agile_project_cards
            WHERE id = :ticket_id
        """
        ticket_result = conn.execute(text(ticket_query), {"ticket_id": ticket_id}).fetchone()

        if not ticket_result:
            logger.error(f"No ticket found with id: {ticket_id}")
            return
        
        project_id, created_by, updated_by, assigned_to, card_identifier = ticket_result

        created_by_user_data = store_db.fetch_user_by_username(store_id, created_by)
        created_by_user_name = created_by_user_data["name"] if created_by_user_data else None

        updated_by_user_data = store_db.fetch_user_by_username(store_id, updated_by)
        updated_by_user_name = updated_by_user_data["name"] if updated_by_user_data else None
        
        if event_type == AdminAppNotificationUtil.TICKET_CREATED:
            project_members_query = """
                SELECT username
                FROM agile_project_access
                WHERE status = 'active' AND project_id = :project_id
            """
            members_result = conn.execute(text(project_members_query), {"project_id": project_id}).fetchall()
    
            if not members_result:
                logger.error(f"No project members found for project_id: {project_id}")
                return

            members_email = [member[0] for member in members_result]

            for member_email in members_email:
                user_info = store_db.fetch_user_by_username(store_id, member_email)
                user_receivers.append({
                    "user_id": str(user_info["_id"]),
                    "email": member_email
                })
        else:
            if assigned_to:
                user_info = store_db.fetch_user_by_username(store_id, assigned_to)
                user_receivers.append({
                    "user_id": str(user_info["_id"]) if user_info else None,
                    "email": assigned_to
                })

        notification_entry = {
            "module": module,
            "event_type": event_type,
            "ticket_id": int(ticket_id),
            "project_id": project_id,
            "card_identifier": card_identifier,
            "created_by_user_name": created_by_user_name,
            "created_by_user_email": created_by,
            "updated_by_user_name": updated_by_user_name,
            "updated_by_user_email": updated_by,
            "created_at": int(datetime.now(timezone.utc).timestamp()),
            "retention_period_hours": retention_period_hours,
            "receivers": [
                {
                    "user_id": receiver.get("user_id", None),
                    "email": receiver.get("email", None),
                    "is_read": False,
                    "read_time": None
                }
                for receiver in user_receivers
            ] if user_receivers else []
        }
        db["notifications_admin"].insert_one(notification_entry)

    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"An error occurred: {str(e)}")
        logger.error(f"Traceback details:\n{error_details}")
    finally:
        conn.close()

def user_created_notification(store_id, user_id, event_type, payload):
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    try:
        notification_module = db["notification_modules"].find_one({"event_type": event_type})
        if not notification_module:
            logger.error(f"No notification module found for event_type: {event_type}")
            return

        retention_period_hours = int(notification_module.get("retention_period_hours", 720))
        receivers = notification_module.get("receivers", [])
        module = notification_module.get("module", None)

        # Step 3: Extract user information from receivers
        user_receivers = [
            {"user_id": receiver["id"], "email": receiver["email"]}
            for receiver in receivers if receiver["type"] == "user"
        ]
        invitee = payload.get("name", "")
        user_data = mongo_db.fetch_one_document_from_admin_collection(store_id, "users", {"_id": ObjectId(user_id)})
        new_user_name = user_data["name"] if user_data else None

        notification_entry = {
            "module": module,
            "event_type": event_type,
            "invitee": invitee,
            "new_user_name": new_user_name,
            "created_at": int(datetime.now(timezone.utc).timestamp()),
            "retention_period_hours": retention_period_hours,
            "receivers": [
                {
                    "user_id": receiver.get("user_id", None),
                    "email": receiver.get("email", None),
                    "is_read": False,
                    "read_time": None
                } for receiver in user_receivers
            ]
        }
        db["notifications_admin"].insert_one(notification_entry)
    
    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"An error occurred: {str(e)}")
        logger.error(f"Traceback details:\n{error_details}")

def user_themes_updated_notification(store_id, user_id, event_type):
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    try:
        notification_module = db["notification_modules"].find_one({"event_type": event_type})
        if not notification_module:
            logger.error(f"No notification module found for event_type: {event_type}")
            return

        retention_period_hours = int(notification_module.get("retention_period_hours", 720))
        receivers = notification_module.get("receivers", [])
        module = notification_module.get("module", None)

        # Step 3: Extract user information from receivers
        user_receivers = [
            {"user_id": receiver["id"], "email": receiver["email"]}
            for receiver in receivers if receiver["type"] == "user"
        ]
        admin_user = store_db.fetch_user_by_id(store_id, user_id)
        admin_user_name = admin_user["name"] if admin_user else None
        notification_entry = {
            "module": module,
            "event_type": event_type,
            "admin_user_name": admin_user_name,
            "created_at": int(datetime.now(timezone.utc).timestamp()),
            "retention_period_hours": retention_period_hours,
            "receivers": [
                {
                    "user_id": receiver.get("user_id", None),
                    "email": receiver.get("email", None),
                    "is_read": False,
                    "read_time": None
                }
                for receiver in user_receivers
            ]
        }
        db["notifications_admin"].insert_one(notification_entry)

    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"An error occurred: {str(e)}")
        logger.error(f"Traceback details:\n{error_details}")

def customer_created_notification(store_id, customer_id, event_type):
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    conn = pg_db.get_connection(store_id)
    try:
        rep_name = None
        rep_email = None

        # Step 2: Fetch notification module by event_type
        notification_module = db["notification_modules"].find_one({"event_type": event_type})
        if not notification_module:
            logger.error(f"No notification module found for event_type: {event_type}")
            return

        retention_period_hours = int(notification_module.get("retention_period_hours", 720))
        receivers = notification_module.get("receivers", [])
        module = notification_module.get("module", None)

        # Step 3: Extract user information from receivers
        user_receivers = [
            {"user_id": receiver["id"], "email": receiver["email"]}
            for receiver in receivers if receiver["type"] == "user"
        ]
        query_rep = """
                SELECT DISTINCT rep_email
                FROM salesforce_customer_rep
            """
        rep_result = conn.execute(text(query_rep)).fetchall()
        if rep_result:
            for rep in rep_result:
                rep_email = rep[0]
                user_info = store_db.fetch_user_by_username(store_id, rep_email)
                user_receivers.append({
                    "user_id": str(user_info["_id"]) if user_info else None,
                    "email": rep_email
                })

        customer_details = mongo_db.fetch_one_document_from_storefront_collection(store_id, "customers", {"_id": customer_id})
        customer_name = customer_details["first_name"] + " " + customer_details["last_name"] if customer_details else None
        customer_email = customer_details["email"] if customer_details else None

        notification_entry = {
            "module": module,
            "event_type": event_type,
            "customer_id": customer_id,
            "customer_name": customer_name,
            "customer_email": customer_email,
            "created_at": int(datetime.now(timezone.utc).timestamp()),
            "retention_period_hours": retention_period_hours,
            "receivers": [
                {
                    "user_id": receiver.get("user_id", None),
                    "email": receiver.get("email", None),
                    "is_read": False,
                    "read_time": None
                }
                for receiver in user_receivers
            ]
        }
        db["notifications_admin"].insert_one(notification_entry)
    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"An error occurred: {str(e)}")
        logger.error(f"Traceback details:\n{error_details}")
    finally:
        conn.close()

def po_created_notification(store_id, po_id, event_type):
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    conn = pg_db.get_connection(store_id)
    try:
        notification_module = db["notification_modules"].find_one({"event_type": event_type})
        if not notification_module:
            logger.error(f"No notification module found for event_type: {event_type}")
            return

        retention_period_hours = int(notification_module.get("retention_period_hours", 720))
        receivers = notification_module.get("receivers", [])
        module = notification_module.get("module", None)

        # Step 3: Extract user information from receivers
        user_receivers = [
            {"user_id": receiver["id"], "email": receiver["email"]}
            for receiver in receivers if receiver["type"] == "user"
        ]

        query_po = """
            SELECT customer_id, customer_name, customer_rep_email, customer_rep_name, created_by, status
            FROM bo_purchase_orders
            WHERE po_id = :po_id
        """
        po_result = conn.execute(text(query_po), {"po_id": po_id}).fetchone()
        if not po_result:
            logger.error(f"No po found with po_id: {po_id}")
            return

        customer_id, customer_name, customer_rep_email, customer_rep_name, created_by, status = po_result

        # Check status and update if necessary
        if status == "partially fulfilled":
            status = "pending"

        if customer_rep_email:
            user_info = store_db.fetch_user_by_username(store_id, customer_rep_email)
            user_receivers.append({
                "user_id": str(user_info["_id"]) if user_info else None,
                "email": customer_rep_email
            })

        db["notifications_admin"].update_many(
            {"po_id": po_id},
            {"$set": {"status": status}}
        )

        notification_entry = {
            "module": module,
            "event_type": event_type,
            "po_id": po_id,
            "customer_id": customer_id,
            "customer_name": customer_name,
            "customer_rep_name": customer_rep_name,
            "created_by": created_by,
            "status": status,
            "created_at": int(datetime.now(timezone.utc).timestamp()),
            "retention_period_hours": retention_period_hours,
            "receivers": [
                {
                    "user_id": receiver.get("user_id", None),
                    "email": receiver.get("email", None),
                    "is_read": False,
                    "read_time": None
                }
                for receiver in user_receivers
            ] if user_receivers else []
        }
        db["notifications_admin"].insert_one(notification_entry)
    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"An error occurred: {str(e)}")
        logger.error(f"Traceback details:\n{error_details}")
    finally:
        conn.close()

def order_placed_notification(store_id, bc_order_id, event_type):
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    conn = pg_db.get_connection(store_id)
    try:
        notification_module = db["notification_modules"].find_one({"event_type": event_type})
        if not notification_module:
            logger.error(f"No notification module found for event_type: {event_type}")
            return

        retention_period_hours = int(notification_module.get("retention_period_hours", 720))
        receivers = notification_module.get("receivers", [])
        module = notification_module.get("module", None)

        # Step 3: Extract user information from receivers
        user_receivers = [
            {"user_id": receiver["id"], "email": receiver["email"]}
            for receiver in receivers if receiver["type"] == "user"
        ]
        query_order = """
            SELECT pbo.po_id, pbo.created_by, po.status
            FROM bo_purchase_order_bc_order_mapping pbo
            LEFT JOIN bo_purchase_orders po ON pbo.po_id = po.po_id
            WHERE bc_order_id = :bc_order_id
        """
        order_result = conn.execute(text(query_order), {"bc_order_id": str(bc_order_id)}).fetchone()
        if not order_result:
            logger.error(f"No order found with bc_order_id: {bc_order_id}")
            return

        po_id, created_by, status = order_result

        # Check status and update if necessary
        if status == "partially fulfilled":
            status = "pending"
        
        db["notifications_admin"].update_many(
            {"po_id": po_id},
            {"$set": {"status": status}}
        )

        if created_by:
            user_info = store_db.fetch_user_by_username(store_id, created_by)
            created_by_name = user_info["name"] if user_info else None
        
        notification_entry = {
            "module": module,
            "event_type": event_type,
            "bc_order_id": bc_order_id,
            "po_id": po_id,
            "admin_user_name": created_by_name,
            "created_by_email": created_by,
            "status": status,
            "created_at": int(datetime.now(timezone.utc).timestamp()),
            "retention_period_hours": retention_period_hours,
            "receivers": [
                {
                    "user_id": receiver.get("user_id", None),
                    "email": receiver.get("email", None),
                    "is_read": False,
                    "read_time": None
                }
                for receiver in user_receivers
            ] if user_receivers else []
        }
        db["notifications_admin"].insert_one(notification_entry)
    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"An error occurred: {str(e)}")
        logger.error(f"Traceback details:\n{error_details}")
    finally:
        conn.close()

def unlock_product_notification(store_id, bop_id, event_type):
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    conn = pg_db.get_connection(store_id)
    try:
        user_receivers = []
        notification_module = db["notification_modules"].find_one({"event_type": event_type})
        if not notification_module:
            logger.error(f"No notification module found for event_type: {event_type}")
            return

        retention_period_hours = int(notification_module.get("retention_period_hours", 720))
        module = notification_module.get("module", None)

        query_bop = """
            SELECT is_po_locked, type
            FROM bo_bulk_order_products
            WHERE bop_id = :bop_id
        """
        bop_result = conn.execute(text(query_bop), {"bop_id": bop_id}).fetchone()
        if not bop_result:
            logger.error(f"No bop found with bop_id: {bop_id}")
            return

        is_po_locked, type = bop_result

        db["notifications_admin"].update_many(
            {"bop_id": bop_id},
            {"$set": {"type": type}}
        )

        if is_po_locked:
            logger.error(f"PO is locked for bop_id: {bop_id}")
            return
        else:
            query_rep = """
                SELECT DISTINCT rep_email
                FROM salesforce_customer_rep
            """
            rep_result = conn.execute(text(query_rep)).fetchall()
            if rep_result:
                for rep in rep_result:
                    rep_email = rep[0]
                    user_info = store_db.fetch_user_by_username(store_id, rep_email)
                    user_receivers.append({
                        "user_id": str(user_info["_id"]) if user_info else None,
                        "email": rep_email
                    })

            notification_entry = {
                "module": module,
                "event_type": event_type,
                "bop_id": int(bop_id),
                "type": type,
                "created_at": int(datetime.now(timezone.utc).timestamp()),
                "retention_period_hours": retention_period_hours,
                "receivers": [
                    {
                        "user_id": receiver.get("user_id", None),
                        "email": receiver.get("email", None),
                        "is_read": False,
                        "read_time": None
                    }
                    for receiver in user_receivers
                ] if user_receivers else []
            }
            db["notifications_admin"].insert_one(notification_entry)
        
    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"An error occurred: {str(e)}")
        logger.error(f"Traceback details:\n{error_details}")
    finally:
        conn.close()

def block_order_created_notification(store_id, order_id, event_type):
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    conn = pg_db.get_connection(store_id)
    try:
        notification_module = db["notification_modules"].find_one({"event_type": event_type})
        if not notification_module:
            logger.error(f"No notification module found for event_type: {event_type}")
            return

        retention_period_hours = int(notification_module.get("retention_period_hours", 720))
        receivers = notification_module.get("receivers", [])
        module = notification_module.get("module", None)

        user_receivers = [
            {"user_id": receiver["id"], "email": receiver["email"]}
            for receiver in receivers if receiver["type"] == "user"
        ]

        query_block_order = """
            SELECT id, customer_name, blocked_by
            FROM blocked_orders
            WHERE order_id = :order_id
        """
        block_order_result = conn.execute(text(query_block_order), {"order_id": order_id}).fetchone()
        if not block_order_result:
            logger.error(f"No block order found with order_id: {order_id}")
            return

        blocked_order_id, customer_name, blocked_by = block_order_result

        notification_entry = {
            "module": module,
            "event_type": event_type,
            "blocked_order_id": blocked_order_id,
            "order_id": order_id,
            "customer_name": customer_name,
            "admin_user_name": blocked_by,
            "created_at": int(datetime.now(timezone.utc).timestamp()),
            "retention_period_hours": retention_period_hours,
            "receivers": [
                {
                    "user_id": receiver.get("user_id", None),
                    "email": receiver.get("email", None),
                    "is_read": False,
                    "read_time": None
                }
                for receiver in user_receivers
            ] if user_receivers else []
        }
        db["notifications_admin"].insert_one(notification_entry)
    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"An error occurred: {str(e)}")
        logger.error(f"Traceback details:\n{error_details}")
    finally:
        conn.close()

def job_failed_notification(store_id, job_name, event_type):
    from task import task_list
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    try:
        notification_module = db["notification_modules"].find_one({"event_type": event_type})
        if not notification_module:
            logger.error(f"No notification module found for event_type: {event_type}")
            return

        retention_period_hours = int(notification_module.get("retention_period_hours", 720))
        module = notification_module.get("module", None)

        # Extract role_id from receivers where type is "role"
        receivers = notification_module.get("receivers", [])
        role_ids = [r.get("role_id") for r in receivers]
        
        if not role_ids:
            logger.error(f"No role_id found in receivers for event_type: {event_type}")
            return
        
        users = db["users"].find({"role_id": {"$in": role_ids}})
        if not users:
            logger.error(f"No users found")
            return
        
        user_receivers = [
            {"user_id": str(user["_id"]), "email": user["username"]}
            for user in users
        ]

        job_label = next((task["label"] for task in task_list if task["name"] == job_name), None)

        notification_entry = {
            "module": module,
            "event_type": event_type,
            "job_name": job_label,
            "created_at": int(datetime.now(timezone.utc).timestamp()),
            "retention_period_hours": retention_period_hours,
            "receivers": [
                {
                    "user_id": receiver.get("user_id", None),
                    "email": receiver.get("email", None),
                    "is_read": False,
                    "read_time": None
                }
                for receiver in user_receivers
            ] if user_receivers else []
        }
        db["notifications_admin"].insert_one(notification_entry)
    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"An error occurred: {str(e)}")
        logger.error(f"Traceback details:\n{error_details}")

def remove_old_notifications(store_id):
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    thirty_days_ago = int((datetime.now(timezone.utc) - timedelta(days=30)).timestamp())
    result = db["notifications_admin"].delete_many({"created_at": {"$lt": thirty_days_ago}})

def send_webhook_inactive_notification(store_id):
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    try:
        store = store_util.get_store_by_id(store_id)
        webhooks = bc_webhook.fetch_webhooks(store)

        # Get inactive webhooks
        inactive_webhooks = [
            {"scope": webhook["scope"], "id": webhook["id"]}
            for webhook in webhooks.get("data", [])
            if not webhook.get("is_active", True)  # Only include inactive webhooks
        ]
        notification_module = db["notification_modules"].find_one({"event_type": "webhook_inactive"})
        if not notification_module:
            logger.error(f"No notification module found for event_type: webhook_inactive")
            return

        retention_period_hours = int(notification_module.get("retention_period_hours", 720))
        module = notification_module.get("module", None)

        # Extract role_id from receivers where type is "role"
        receivers = notification_module.get("receivers", [])
        role_ids = [r.get("role_id") for r in receivers]
        
        users = db["users"].find({"role_id": {"$in": role_ids}})
        if not users:
            logger.error(f"No users found")
            return
        
        user_receivers = [
            {"user_id": str(user["_id"]), "email": user["username"]}
            for user in users
        ]
        # Create a notification entry for each inactive webhook
        for webhook in inactive_webhooks:
            notification_entry = {
                "module": module,
                "event_type": "webhook_inactive",
                "webhook_scope": webhook["scope"],  # Scope of the inactive webhook
                "webhook_id": webhook["id"],
                "created_at": int(datetime.now(timezone.utc).timestamp()),
                "retention_period_hours": retention_period_hours,
                "receivers": [
                    {
                        "user_id": receiver.get("user_id", None),
                        "email": receiver.get("email", None),
                        "is_read": False,
                        "read_time": None
                    }
                    for receiver in user_receivers
                ] if user_receivers else []
            }
            db["notifications_admin"].insert_one(notification_entry)
    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"An error occurred: {str(e)}")
        logger.error(f"Traceback details:\n{error_details}")