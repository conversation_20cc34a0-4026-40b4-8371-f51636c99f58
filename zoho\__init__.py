import requests
import json
from time import sleep
from datetime import datetime, timedelta
from utils import store_util, redis_util
import logging
import traceback

logger = logging.getLogger()

def generate_access_token(store_id, zoho_creds):
    # Zoho API endpoints
    auth_url = zoho_creds.get("token_api", None)

    # Get credentials from environment or config
    client_id = zoho_creds.get("client_id", None)
    client_secret = zoho_creds.get("client_secret", None)
    refresh_token = zoho_creds.get("refresh_token", None)

    # Request parameters
    params = {
        "client_id": client_id,
        "client_secret": client_secret,
        "refresh_token": refresh_token,
        "grant_type": "refresh_token"
    }

    try:
        # Make request to get access token
        response = requests.post(auth_url, data=params)
        response.raise_for_status()

        # Parse response
        token_data = response.json()
        access_token = token_data.get("access_token")
        
        if not access_token:
            raise Exception("No access token in response")
        
        redis_util.update_zoho_token(store_id=store_id, token=access_token)
        return access_token

    except requests.exceptions.RequestException as e:
        logger.error(f"Error getting Zoho access token: {str(e)}")
        logger.error(str(traceback.format_exc()))
        return None
    except Exception as e:
        logger.error(f"Error processing Zoho token response: {str(e)}")
        logger.error(str(traceback.format_exc()))
        return None
    

def fetch_records(store_id, application_name, report_name, max_record=1000, query_params=None):
    res = []
    zoho_creds = store_util.get_zoho_api_info(store_id=store_id)
    logger.info(zoho_creds)
    if not zoho_creds:
        logger.error(f"Zoho is not configured for store id: {store_id}")
        raise Exception(f"Zoho is not configured for store id: {store_id}")
    workspace = zoho_creds.get("workspace", None)
    access_token = redis_util.get_zoho_token(store_id=store_id)
    logger.info(access_token)
    if not access_token:
        access_token = generate_access_token(store_id=store_id,zoho_creds=zoho_creds)
        if access_token is None:
            logger.error("Failed to generate access token - access_token is None")
        logger.info("<=========access_token========> inside" + access_token)
        if not access_token:
            logger.error(f"Not able to generate Zoho access token for store id: {store_id}") 
            raise Exception(f"Not able to generate Zoho access token for store id: {store_id}") 
        
    base_url = zoho_creds.get("api_base_url", None)
    if not base_url:
        logger.error(f"Zoho api base url is not configured for store id: {store_id}") 
        raise Exception(f"Zoho api base url is not configured for store id: {store_id}") 

    logger.info(access_token)
    endpoint = f"{base_url}/creator/v2.1/data/{workspace}/{application_name}/report/{report_name}"
    headers = {
        "Authorization": f"Zoho-oauthtoken {access_token}",
    }
    retry_count = 0
    terminate = False
    record_cursor = None
    page = 1
    while not terminate:
        try:
            if record_cursor:
                headers["record_cursor"] = record_cursor
            response = requests.get(endpoint, headers=headers, params=query_params)
            records = response.json()
            if response.status_code == 401:
                logger.error(f"Zoho api failed with 401 status: {endpoint}, error: {records}")
                access_token = generate_access_token(store_id=store_id, zoho_creds=zoho_creds)
                logger.info(f"Access token generated: {access_token}")
                retry_count += 1
            elif response.status_code < 200 and response.status_code > 299:
                logger.error(f"Zoho api failed with {response.status_code} status, Endpoint: {endpoint}, Error: {records}")
                retry_count += 1
            else:
                retry_count = 0
                if records and "data" in records:
                    res.extend(records["data"])
                    logger.info(f"Fetched {len(records['data'])} records on page {page}")

                # Get record_cursor from response headers
                record_cursor = response.headers.get("record_cursor")
                page += 1

                if not record_cursor:
                    terminate = True
            
            if retry_count > 2:
                terminate = True
            
            # if retry_count == 0 and records and "data" in records:
            #     data = records["data"]
            #     if len(data) < max_record:
            #         terminate = True
            #     res.append(data)
        except requests.exceptions.RequestException as e:
            logger.error(f"Error getting Zoho access token: {str(e)}")
            logger.error(str(traceback.format_exc()))
            retry_count += 1
        except Exception as e:
            logger.error(f"Error processing Zoho token response: {str(e)}")
            logger.error(str(traceback.format_exc()))
            retry_count += 1

        if retry_count > 2:
            terminate = True
        elif retry_count > 0:
            sleep(5)
        
    return res

