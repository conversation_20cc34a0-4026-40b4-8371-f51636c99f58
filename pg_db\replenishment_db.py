from sqlalchemy import Column, String, Integer, Float, DateTime, text
import pg_db as db
import logging

logger = logging.getLogger()


class ReplenishmentProducts(db.Base):
    __tablename__ = db.replenishment_products_data
    
    id = Column(Integer, primary_key=True)
    product_title = Column(String)
    parent_sku = Column(String)    
    product_id = Column(Integer)
    cost = Column(Float)
    retail_price = Column(Float)
    sale_price = Column(Float)
    reorder_point = Column(Float)
    incremental_quantity = Column(Integer)
    quantity_on_hand = Column(Integer)
    quantity_pending = Column(Integer)
    quantity_incoming = Column(Integer)
    quantity_available = Column(Integer)
    quantity_on_hold = Column(Integer)    
    total_sold_180 = Column(Integer)  
    total_sold_90 = Column(Integer)
    total_sold_60 = Column(Integer)
    total_sold_45 = Column(Integer)
    total_sold_30 = Column(Integer)
    total_sold_15 = Column(Integer)
    total_sold_7 = Column(Integer)     
    suggested_order_qty_7 = Column(Integer)
    suggested_order_qty_15 = Column(Integer)    
    suggested_order_qty_30 = Column(Integer)
    suggested_order_qty_45 = Column(Integer)
    suggested_order_qty_60 = Column(Integer)
    suggested_order_qty_90 = Column(Integer)
    suggested_order_qty_180 = Column(Integer) 
    to_order_qty_7 = Column(Integer)
    to_order_qty_15 = Column(Integer)
    to_order_qty_30 = Column(Integer)
    to_order_qty_45 = Column(Integer)
    to_order_qty_60 = Column(Integer)
    to_order_qty_90 = Column(Integer)
    to_order_qty_180 = Column(Integer)                
    month_7 = Column(Integer)   
    month_6 = Column(Integer)
    month_5 = Column(Integer)
    month_4 = Column(Integer)
    month_3 = Column(Integer)
    month_2 = Column(Integer)
    month_1 = Column(Integer)   


class Replenishment_variants(db.Base):
    __tablename__ = db.replenishment_variants_data
    
    id = Column(Integer, primary_key=True)
    product_title = Column(String)
    product_id = Column(Integer)
    parent_sku = Column(String)    
    sku = Column(String)
    variant_id = Column(Integer)
    cost = Column(Float)
    retail_price = Column(Float)
    sale_price = Column(Float)
    reorder_point = Column(Float)
    incremental_quantity = Column(Integer)
    quantity_on_hand = Column(Integer)
    quantity_pending = Column(Integer)
    quantity_incoming = Column(Integer)
    quantity_available = Column(Integer)
    quantity_on_hold = Column(Integer)    
    total_sold_180 = Column(Integer)  
    total_sold_90 = Column(Integer)
    total_sold_60 = Column(Integer)
    total_sold_45 = Column(Integer)
    total_sold_30 = Column(Integer)
    total_sold_15 = Column(Integer)
    total_sold_7 = Column(Integer)      
    suggested_order_qty_7 = Column(Integer)
    suggested_order_qty_15 = Column(Integer)    
    suggested_order_qty_30 = Column(Integer)
    suggested_order_qty_45 = Column(Integer)
    suggested_order_qty_60 = Column(Integer)
    suggested_order_qty_90 = Column(Integer)
    suggested_order_qty_180 = Column(Integer) 
    to_order_qty_7 = Column(Integer)
    to_order_qty_15 = Column(Integer)
    to_order_qty_30 = Column(Integer)
    to_order_qty_45 = Column(Integer)
    to_order_qty_60 = Column(Integer)
    to_order_qty_90 = Column(Integer)
    to_order_qty_180 = Column(Integer)     
    month_7 = Column(Integer)   
    month_6 = Column(Integer)
    month_5 = Column(Integer)
    month_4 = Column(Integer)
    month_3 = Column(Integer)
    month_2 = Column(Integer)
    month_1 = Column(Integer)    


class ReplenishmentReservedVariants(db.Base):
    __tablename__ = db.replenishment_reserved_variants
    
    order_id = Column(Integer, primary_key=True)
    order_line_item_id = Column(Integer, primary_key=True)
    product_id = Column(Integer)
    sku = Column(String)
    variant_id = Column(Integer)
    quantity = Column(Integer)
    last_updated_datetime = Column(DateTime)

    def __repr__(self):
        return f'ReplenishmentReservedVariants {self.order_id}'
    
    @classmethod
    def clear_table(cls, store_id, session=None):
        local_session = None
        if not session:
            session = db.get_session(store_id)
            local_session = session
        try:
            # Check if the table exists in the current schema
            table_exists_query = text(f"""
                SELECT EXISTS (
                    SELECT 1
                    FROM information_schema.tables 
                    WHERE table_name = '{cls.__tablename__}'
                )
            """)
            result = session.execute(table_exists_query).scalar()

            # Execute a raw SQL TRUNCATE statement
            if result:
                session.execute(text(f'TRUNCATE TABLE {cls.__tablename__} RESTART IDENTITY CASCADE'))
                session.commit()
        finally:
            if local_session:
                local_session.close()
