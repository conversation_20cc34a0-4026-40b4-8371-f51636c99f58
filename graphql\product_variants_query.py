product_variants_query = """
  query varaintsByProductId (
    $productId: Int = product_id
    $cursor: String = end_cursor
  ) {
  site {
    product(entityId: $productId) {
      variants(first: 250, after: $cursor) {
        pageInfo {
          hasNextPage
          startCursor
          endCursor
        }
        edges {
          node {
            entityId
            sku
            inventory {
              aggregated {
                availableToSell
                warningLevel
              }
            }
            prices {
              price {
                ...PriceFields
              }
              salePrice {
                ...PriceFields
              }
              retailPrice {
                ...PriceFields
              }
            }
            productOptions(first: 50) {
              edges {
                node {
                  entityId
                  displayName
                  isRequired
                  ... on CheckboxOption {
                    checkedByDefault
                  }
                  ... on MultipleChoiceOption {
                    values(first: 10) {
                      edges {
                        node {
                          entityId
                          label
                          isDefault
                          ... on SwatchOptionValue {
                            hexColors
                            imageUrl(width: 200)
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
fragment PriceFields on Money {
  value
  currencyCode
}
"""

def get_query(product_id, end_cursor):
    x = product_variants_query.replace("product_id", str(product_id))
    return x.replace("end_cursor", '"' + end_cursor + '"')
    