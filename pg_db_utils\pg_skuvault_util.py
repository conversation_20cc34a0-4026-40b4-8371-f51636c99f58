import time
import datetime
import pg_db_utils
import pg_db as db
from pg_db import skuvault_db
import pandas as pd
import logging
import traceback
from utils import store_util

logger = logging.getLogger()

def _catalog_model(obj):

    created_date = None
    modified_date = None
    date_format = '%Y-%m-%dT%H:%M:%S'

    if obj['CreatedDateUtc']:
        dt = obj['CreatedDateUtc'].split(".")[0]
        created_date = datetime.datetime.strptime(dt, date_format)

    if obj['ModifiedDateUtc']:
        dt = obj['ModifiedDateUtc'].split(".")[0]
        modified_date = datetime.datetime.strptime(dt, date_format)
        
    parent_sku = obj.get("VariationParentSku", None)
    if not parent_sku:
        parent_sku = obj.get("PrimarySku", "")

    return skuvault_db.SkuvaultCatalog(
        code = obj['Code'],
        sku = obj['Sku'],
        parent_sku = parent_sku,
        part_number = obj['PartNumber'],
        title = obj['Description'],
        note = "Not Set",
        moq = int(obj['MOQ']),
        moq_info = obj['MOQInfo'],
        cost = float(obj['Cost']),
        retail_price = float(obj['RetailPrice']),
        sale_price = float(obj['SalePrice']),
        weight = float(obj['WeightValue']),
        weight_unit = obj['WeightUnit'],
        reorder_point = int(obj['ReorderPoint']),
        incremental_quantity = int(obj['IncrementalQuantity']),
        brand = obj['Brand'],
        primary_supplier = obj['primary_supplier'],
        primary_supplier_is_active = obj['primary_supplier_is_active'],
        primary_supplier_cost = float(obj['primary_supplier_cost']),
        primary_supplier_lead_time = int(obj['primary_supplier_lead_time']),
        primary_supplier_part_number = obj['primary_supplier_part_number'],
        classification = obj['Classification'],
        statuses = obj['statuses'],
        created_date = created_date,
        modified_date = modified_date,
        disable_quantity_sync = obj['DisableQuantitySync'],
        quantity_on_hand = int(obj['QuantityOnHand']),
        quantity_pending = int(obj['QuantityPending']),
        quantity_incoming = int(obj['QuantityIncoming']),
        quantity_available = int(obj['QuantityAvailable']),
        quantity_on_hold = int(obj['QuantityOnHold']),
        attribute1_name = obj['Attribute1Name'],
        attribute1_value = obj['Attribute1Value'],
        attribute2_name = obj['Attribute2Name'],
        attribute2_value = obj['Attribute2Value'],
        attribute3_name = obj['Attribute3Name'],
        attribute3_value = obj['Attribute3Value'],
        attribute4_name = obj['Attribute4Name'],
        attribute4_value = obj['Attribute4Value'],
        attribute5_name = obj['Attribute5Name'],
        attribute5_value = obj['Attribute5Value']
    )

def process_product(product):
    product["primary_supplier"] = ""
    product["primary_supplier_is_active"] = False
    product["primary_supplier_cost"] = 0
    product["primary_supplier_lead_time"] = 0
    product["primary_supplier_part_number"] = ""
    product["statuses"] = ""

    for supplier in product["SupplierInfo"]:
        if supplier["IsPrimary"]:
            product["primary_supplier"] = supplier["SupplierName"]
            product["primary_supplier_is_active"] = supplier["IsActive"]
            product["primary_supplier_cost"] = supplier["Cost"]
            product["primary_supplier_lead_time"] = supplier["LeadTime"]
            product["primary_supplier_part_number"] = supplier["SupplierPartNumber"]
            break
    
    product_attributes = product["Attributes"]
    if product_attributes:
        i = 0
        while i < 5:
            attribute_name = ""
            attribute_value = ""
            if len(product_attributes) > i:
                attribute_name = product_attributes[i]["Name"]
                attribute_value = product_attributes[i]["Value"]
            
            product["Attribute" + str(i + 1) + "Name"] = attribute_name
            product["Attribute" + str(i + 1) + "Value"] = attribute_value
            i = i + 1
    else:
        i = 0
        while i < 5:
            product["Attribute" + str(i + 1) + "Name"] = ""
            product["Attribute" + str(i + 1) + "Value"] = ""
            i = i + 1

    return _catalog_model(product)
    

def process_products(store_id, products):
    session = db.get_session(store_id)
    try:
        with session.begin():
            for product in products:
                model = process_product(product)
                session.merge(model)
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        if session:
            session.commit()
            session.close()

def update_sku_vault(store_id):
    api_info = store_util.get_skuvault_api_info(store_id)
    tenant_token = api_info['tenant_token']
    user_token = api_info['user_token']
    min_date_modified_iso = (datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(days=365)).isoformat()

    page_number = 0
    retry_count = 0
    page_size = 10000
    while True:
        try:
            body = {
                "PageNumber": page_number,
                "PageSize": page_size,
                "ModifiedAfterDateTimeUtc": min_date_modified_iso,
                "TenantToken": tenant_token,
                "UserToken": user_token
            }
            logger.info(f"Fetching data from sku vault, pagenumber: {page_number} {body}")
            res = pg_db_utils.call_api("POST", "https://app.skuvault.com/api/products/getProducts",
                    {}, body, False)

            response_body = res.json()
            if response_body and "Products" in response_body:
                _products = response_body["Products"]
                logger.info("Product size: " + str(len(_products)))
                if len(_products) == 0:
                    if retry_count < 3:
                        time.sleep(30)
                        retry_count = retry_count + 1
                        continue
                    else:
                        break

                retry_count = 0
                process_products(store_id, _products)
                    
                if len(_products) < page_size:
                    break
            else:
                break
            page_number = page_number + 1
        except Exception as e:
            logger.error(traceback.format_exc())
            if retry_count < 3:
                retry_count = retry_count + 1
                logger.error(f"Retrying with count {retry_count}")
            else:
                break

def update_sku_vault_classifications(store_id, body):  
    try:  
        api_info = store_util.get_skuvault_api_info(store_id)
        payload = {}
        payload['TenantToken'] = api_info['tenant_token']
        payload['UserToken'] = api_info['user_token']
        payload['Items'] = body             
        res = pg_db_utils.call_api("POST", "https://app.skuvault.com/api/products/updateProducts",
                {}, payload, False)
        response_body = res.json()
    except Exception as e:
        logger.error(traceback.format_exc())     

def catalog_model(obj):

    created_date = None
    modified_date = None
    date_format = '%Y-%m-%dT%H:%M:%S'

    if obj['Create Date']:
        dt = obj['Create Date'].split(".")[0]
        created_date = datetime.datetime.strptime(dt, date_format)

    if obj['Modified Date']:
        dt = obj['Modified Date'].split(".")[0]
        modified_date = datetime.datetime.strptime(dt, date_format)

    return skuvault_db.SkuvaultCatalog(
        code = obj['Code'],
        sku = obj['SKU'],
        parent_sku = obj.get('VariationParentSku', ''),
        part_number = obj['Part Number'],
        title = obj['Title'],
        note = obj['Note'],
        moq = int(obj['MOQ']),
        moq_info = obj['MOQ Info'],
        cost = float(obj['Cost']),
        retail_price = float(obj['Retail Price']),
        sale_price = float(obj['Sale Price']),
        weight = float(obj['Weight Value']),
        weight_unit = obj['Weight Unit'],
        reorder_point = int(obj['Reorder Point']),
        incremental_quantity = int(obj['Incremental Quantity']),
        brand = obj['Brand'],
        primary_supplier = obj['Primary Supplier'],
        primary_supplier_is_active = obj['Primary Supplier Is Active'],
        primary_supplier_cost = float(obj['Primary Supplier Cost']),
        primary_supplier_lead_time = int(obj['Primary Supplier Lead Time']),
        primary_supplier_part_number = obj['Primary Supplier Part Number'],
        classification = obj['Classification'],
        statuses = obj['Statuses'],
        created_date = created_date,
        modified_date = modified_date,
        disable_quantity_sync = obj['Disable Quantity Sync'],
        quantity_on_hand = int(obj['Quantity On Hand']),
        quantity_pending = int(obj['Quantity Pending']),
        quantity_incoming = int(obj['Quantity Incoming']),
        quantity_available = int(obj['Quantity Available']),
        quantity_on_hold = int(obj['Quantity on Hold']),
        attribute1_name = obj['Attribute1Name'],
        attribute1_value = obj['Attribute1Value'],
        attribute2_name = obj['Attribute2Name'],
        attribute2_value = obj['Attribute2Value'],
        attribute3_name = obj['Attribute3Name'],
        attribute3_value = obj['Attribute3Value'],
        attribute4_name = obj['Attribute4Name'],
        attribute4_value = obj['Attribute4Name'],
        attribute5_name = obj['Attribute5Name'],
        attribute5_value = obj['Attribute5Value']
    )

def csv_to_db(store_id, csv_file_path):
    session = db.get_session(store_id)
    try:
        df = pd.read_csv(csv_file_path, header=0)
        for index, row in df.iterrows():
            model = catalog_model(row)
            session.add(model)
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        session.commit()
        session.close()
