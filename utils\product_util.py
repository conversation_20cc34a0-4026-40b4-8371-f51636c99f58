from mongo_db import catalog_db
import mongo_db
import task
from utils import store_util, redis_util, price_list_util
from plugin import bc_products
from bson import ObjectId
import logging
import traceback
from datetime import datetime, timezone
import pg_db
from sqlalchemy import text
from pymongo import UpdateOne
from pg_db_utils.pg_product_util import _product_categories_persist_to_db
logger = logging.getLogger()

def get_all_product_inventory(store):
    return catalog_db.fetch_all_product_inventory(store)

################################################################################################
#  Bigcommerce Webhook Handler
################################################################################################
# update_bc_product_invetory_cache: Update inventory cache for a list of the given bc products
# Calling From: plugin.product_util
def update_bc_product_invetory_cache(store, bc_products=[]):
    inventory = {}
    for product in bc_products:
        sku = product.get("sku", None)
        if sku:
            inventory[sku] = product["inventory_level"]
        variants = product.get("variants", [])
        for variant in variants:
            sku = variant.get("sku", None)
            if sku:
                inventory[sku] = variant["inventory_level"]

    if len(inventory) > 0:
        redis_util.update_sku_invetory_cache(store['id'], inventory)
        
    return inventory   

# process_webhook_product_updates: Process webhook product update
# Calling From: task.webhook_update_product
def process_webhook_product_updates(store_id):
    store = store_util.get_store_by_id(store_id)
    if store:
        updates = redis_util.dqueue_webhook_product_update(store_id)
        if updates and len(updates) > 0:
            products = []
            for update in updates:
                product_id = None
                update_type = update['data']['type']
                if update_type == "sku":
                    product_id = update['data']['sku']['product_id']
                elif update_type == "product":
                    product_id = update['data']['id']
                
                # Check if update is from admin
                redis_key = f"admin_update:{store_id}:{product_id}"
                if redis_util.get_redis_client(store_id).exists(redis_key):
                    logger.info(f"Skipping update for product {product_id} as it originated from admin.")
                    continue 
                
                if product_id and not product_id in products:
                    products.append(product_id)
            logger.info("process_webhook_product_updates: updating products: " + str(len(products)))
            old_data = fetch_old_product_data(store_id, products)
            bc_products.update_products(store, products)
            task.submit_task('update_product_price_list_with_logs', (store_id, products, old_data))
            _update_product_data_in_pgdb(store_id, products)
        else:
            logger.info("process_webhook_product_updates: updating products: No update registered.")
    else:
        raise Exception(f"process_webhook_product_updates: Store with id {store_id} doesn't exist.")

def fetch_old_product_data(store_id, product_ids):
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    old_data = {}

    for product_id in product_ids:
        old_data_entry = db["product_price_lists"].find_one({"parent_product_id": product_id})
        if old_data_entry:
            old_data_entry = mongo_db.process_data(old_data_entry)
            old_data[product_id] = old_data_entry

    return old_data

def product_processor_price_list(store_id, product_ids, old_data):
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    store_db = mongo_db.get_store_db_client_for_store_id(store_id)
    conn = pg_db.get_connection(store_id)
    
    try:
        product_ids_to_create_ticket = set()
        old_data_map = {}
        for product_id in product_ids:
            has_updates = False
            # Fetch the product data from the products collection
            product = store_db["products"].find_one({"id": product_id})
            if not product:
                continue

            # Retrieve old data for the product
            old_data_entry = old_data.get(str(product_id))

            # Determine default_price (prioritize sale_price, fallback to price)
            default_price = product.get("sale_price") or product.get("price")
            old_default_price = old_data_entry.get("default_price") if old_data_entry else None

            # Start a transaction
            conn.begin()

            # update_query = text("""
            #     UPDATE pricelist_change_logs
            #     SET is_active = FALSE
            #     WHERE product_id = :product_id;
            # """)
            # conn.execute(update_query, {"product_id": product_id})

            # Build the variants list with necessary data
            variants = []
            price_log_entries = []

            # Create a dictionary for faster old data lookups (O(1) instead of O(n))
            old_variants_map = {int(v["variant_id"]): v for v in old_data_entry.get("variants", [])} if old_data_entry else {}
            # Fetch current timestamp once to avoid multiple `datetime.utcnow()` calls
            current_timestamp = datetime.now(timezone.utc).isoformat(timespec='seconds').replace('+00:00', 'Z')

            for variant in product.get("variants", []):
                variant_id = variant["id"]
                variant_price = variant.get("sale_price") or variant.get("price")
                variant_name = " - ".join(option["label"] for option in variant.get("option_values", [])) or "Parent Product"

                # Fetch old price from pre-built dictionary
                old_variant = old_variants_map.get(int(variant_id), None)
                old_price = old_variant.get("variant_price") if old_variant else None

                # Preserve existing price_list if available
                # price_list = variant.get("price_list", []) or old_variant.get("price_list", []) if old_variant else []

                if old_price != variant_price:
                    has_updates = True
                    # price_log_entries.append({
                    #     "price_list_id": None,  # Placeholder for price_list_id if needed
                    #     "product_id": product_id,
                    #     "variant_id": variant_id,
                    #     "parent_product_name": product["name"],
                    #     "parent_product_sku": product["sku"],
                    #     "variant_name": variant_name,
                    #     "variant_sku": variant["sku"],
                    #     "old_price": old_price,
                    #     "updated_price": variant_price,
                    #     "is_active": True,
                    #     "updated_by": "BigCommerce",
                    #     "updated_at": current_timestamp
                    # })
                    product_ids_to_create_ticket.add(product_id)

                variants.append({
                    "variant_id": variant["id"],
                    "variant_name": variant_name,
                    "variant_sku": variant["sku"],
                    "variant_price": variant_price
                })

            # Batch insert into PostgreSQL if there are changes
            # if price_log_entries:
                
            #     query = text("""
            #         INSERT INTO pricelist_change_logs (
            #             price_list_id, product_id, variant_id, parent_product_name, parent_product_sku, variant_name, variant_sku, 
            #             old_price, updated_price, is_active, updated_by, updated_at
            #         ) VALUES (:price_list_id, :product_id, :variant_id, :parent_product_name, :parent_product_sku, :variant_name, 
            #             :variant_sku, :old_price, :updated_price, :is_active, :updated_by, :updated_at)
            #     """)
            #     conn.execute(query, price_log_entries)

            # Log default_price change if it differs
            if old_default_price != default_price:
                has_updates = True
                # query = text("""
                #     INSERT INTO pricelist_change_logs (
                #         price_list_id, product_id, variant_id, parent_product_name, parent_product_sku, variant_name, variant_sku, old_price, updated_price, is_active, updated_by, updated_at
                #     ) VALUES (:price_list_id, :product_id, NULL, :parent_product_name, :parent_product_sku, NULL, NULL, :old_price, :updated_price, :is_active, :updated_by, :updated_at)
                # """)
                # query_params = {
                #     "price_list_id": None,
                #     "product_id": product_id,
                #     "parent_product_name": product["name"],
                #     "parent_product_sku": product["sku"],
                #     "old_price": old_default_price,
                #     "updated_price": default_price,
                #     "is_active": True,
                #     "updated_by": "BigCommerce",
                #     "updated_at": datetime.now(timezone.utc).isoformat(timespec='seconds').replace('+00:00', 'Z')
                # }
                # conn.execute(query, query_params)
                product_ids_to_create_ticket.add(product_id)

            conn.commit()
            if has_updates:
                # Prepare the document for product_price_lists collection
                product_price_list_doc = {
                    "parent_product_id": product["id"],
                    "parent_product_name": product["name"],
                    "parent_product_sku": product["sku"],
                    "default_price": default_price
                }

                # Start with product-level update
                operations = [
                    UpdateOne(
                        {"parent_product_id": product["id"]},
                        {"$set": product_price_list_doc},
                        upsert=True
                    )
                ]

                # For each variant, update it if exists, or push if not
                for variant in variants:
                    operations.append(
                        UpdateOne(
                            {
                                "parent_product_id": product["id"],
                                "variants.variant_id": variant["variant_id"]
                            },
                            {
                                "$set": {
                                    "variants.$.variant_name": variant["variant_name"],
                                    "variants.$.variant_sku": variant["variant_sku"],
                                    "variants.$.variant_price": variant["variant_price"]
                                }
                            }
                        )
                    )

                if product_ids_to_create_ticket:
                    old_data_map = price_list_util._fetch_old_data_of_products(store_id, list(product_ids_to_create_ticket))
                # Execute all updates in a single bulk write
                db["product_price_lists"].bulk_write(operations, ordered=False)
        
        if old_data_map != {}:
            logger.info("product_ids_to_create_ticket: ", product_ids_to_create_ticket)
            task.submit_task('check_promo_product_and_create_ticket', (store_id, list(product_ids_to_create_ticket), old_data_map))
    except Exception as e:
        conn.rollback()  # Rollback on error
        logger.error(traceback.format_exc())

    finally:
        if conn:
            conn.commit()
            conn.close()

    return "Products processed successfully"

def _fetch_categories_batch(store_db, category_ids):
    if not category_ids:
        return {}

    try:
        # Convert to integers and remove duplicates
        unique_category_ids = list(set([int(cid) for cid in category_ids if cid]))

        # Batch fetch categories from store database categories collection
        categories_cursor = store_db["cms"].find(
            {"id": {"$in": unique_category_ids}},
            {"id": 1, "name": 1, "_id": 0}
        )

        # Create mapping dictionary
        category_mapping = {}
        for category in categories_cursor:
            category_mapping[category["id"]] = category.get("name", "")

        logger.info(f"Fetched {len(category_mapping)} category names from {len(unique_category_ids)} requested IDs")
        return category_mapping

    except Exception as ex:
        logger.error(f"Error fetching categories batch: {traceback.format_exc()}")
        return {}

def _update_product_data_in_pgdb(store_id, product_ids):
    store_db = mongo_db.get_store_db_client_for_store_id(store_id)
    conn = pg_db.get_connection(store_id)
    try:
        for product_id in product_ids:
            # Fetch the product data from the products collection
            product = store_db["products"].find_one({"id": product_id})

            if not product:
                continue

            inventory_level = product.get("inventory_level", 0)
            brand_id = product.get("brand_id", None)
            brand_name = ''
            if brand_id:
                brand = store_db["brands"].find_one({"id": int(brand_id)})
                if brand:
                    brand_name = brand.get("name", '')

            query = """UPDATE products SET inventory_level = :inventory_level, brand_name = :brand_name, brand_id = :brand_id WHERE product_id = :product_id"""
            conn.execute(text(query), {"product_id": product_id, "inventory_level": inventory_level, "brand_name": brand_name, "brand_id": brand_id})
            conn.commit()
    except:
        conn.rollback()
        logger.error(traceback.format_exc())
    finally:
        if conn:
            conn.close()

    return "Products processed successfully"

def process_webhook_product_inventory_updates(store_id, product_id, variant_id, method, value):
    conn = pg_db.get_connection(store_id)
    try:
        new_inventory_level = 0
        if variant_id:
            if method == "relative":
                get_query = """SELECT variants_inventory_level FROM variants WHERE variants_id = :variant_id and product_id = :product_id"""
                current_inventory_level = conn.execute(text(get_query), {"variant_id": variant_id, "product_id": product_id}).scalar_one_or_none()
                new_inventory_level = current_inventory_level + value
            else:
                new_inventory_level = value
            query = """UPDATE variants SET variants_inventory_level = :inventory_level WHERE variants_id = :variant_id and product_id = :product_id"""
            conn.execute(text(query), {"variant_id": variant_id, "inventory_level": new_inventory_level, "product_id": product_id})
        else:
            if method == "relative":
                get_query = """SELECT inventory_level FROM products WHERE product_id = :product_id"""
                current_inventory_level = conn.execute(text(get_query), {"product_id": product_id}).scalar_one_or_none()
                new_inventory_level = current_inventory_level + value
            else:
                new_inventory_level = value
            query = """UPDATE products SET inventory_level = :inventory_level WHERE product_id = :product_id"""
            conn.execute(text(query), {"product_id": product_id, "inventory_level": new_inventory_level})
    except Exception as e:
        logger.error(traceback.format_exc())
    finally:
        if conn:
            conn.commit()
            conn.close()


