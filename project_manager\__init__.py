import pg_db
import logging
import traceback
from datetime import datetime, timezone
from sqlalchemy import text
from utils import admin_app_notification_util, project_notification_util
import task
import time

logger = logging.getLogger()

def create_ticket(store_id, payload, is_from_custom_field=False, is_from_order=False): 
    db_conn = pg_db.get_connection(store_id)  
    try: 
        mapping_details = payload['mapping_details']
        if mapping_details:
            project = _fetch_project_detail(db_conn, mapping_details['project_id'])
            if project:  
                ticket_title = _fetch_resource_detail(db_conn, payload['resource_id'], payload['table_name'], mapping_details['ticket_name'])
                project_id = project[0]['id']

                column_id = payload.get('column_id', None)
                default_column_id = project[0]['default_column_id'] if column_id is None else column_id
                default_module_id = project[0]['default_module_id']
                owner_username = project[0]['owner_username']

                current_column_id = int(default_column_id) if default_column_id else 0
                current_module_id = int(default_module_id) if default_module_id else 0
                status = project[0]['status']
                priority = project[0]['priority']
                title = ticket_title or payload['title'] or ''
                description = f"<p>{payload['description']}</p>" if payload.get('description') else ''
                if is_from_custom_field:
                    assigned_to = payload['assigned_to'] or mapping_details['default_assignee'] or owner_username
                else:
                    assigned_to = payload['assigned_to'] or mapping_details['default_assignee'] or owner_username
                resource_id = payload['resource_id']

                # ✅ Check if ticket already exists when called from order
                if is_from_order:
                    duplicate_check_query = db_conn.execute(
                        text("""SELECT id FROM agile_project_cards 
                                WHERE title = :title AND pipeline_record_id = :resource_id AND table_name = :table_name"""),
                        {
                            'title': str(title),
                            'resource_id': resource_id,
                            'table_name': payload['table_name']
                        }
                    )
                    existing_ticket = duplicate_check_query.fetchone()
                    if existing_ticket:
                        return None  # Don't create duplicate ticket

                spent_time_value = '0h'
                spent_time_hours = parse_time(spent_time_value) 
                spent_time = f"{spent_time_hours} hour"

                estimation_value = '0h'
                estimation_time_hours = parse_time(estimation_value)
                estimation = f"{estimation_time_hours} hour"

                due_date = payload.get('due_date', None)
                start_date = payload.get('start_date', None)
                is_archived = False

                card_index_query = db_conn.execute(
                    text(f"SELECT card_index FROM agile_project_cardindex WHERE project_id = :project_id"),
                    {'project_id': project_id}
                )
                card_index = card_index_query.fetchone()[0]
                
                project_initials = project[0]['project_initials']
                new_card_index = card_index + 1
                card_identifier = f"{project_initials}-{new_card_index}"

                # Fetch the highest sort_id for the given project
                sort_id_query = db_conn.execute(
                    text(f"SELECT MAX(sort_id) FROM agile_project_cards WHERE project_id = :project_id AND module_id = :module_id AND current_column_id = :current_column_id"),
                    {'project_id': project_id, 'module_id': current_module_id, 'current_column_id': current_column_id}
                )
                highest_sort_id = sort_id_query.scalar()
                new_sort_id = highest_sort_id + 1 if highest_sort_id is not None else 1

                query = text(
                f"""INSERT INTO agile_project_cards (project_id, module_id, current_column_id, status, priority, title, card_identifier, description, assigned_to, spent_time, estimation, due_date, is_archived, created_by, updated_by, created_at, updated_at, sort_id, pipeline_record_id, table_name, start_date)
                    VALUES (:project_id, :module_id, :current_column_id, :status, :priority, :title, :card_identifier, :description, :assigned_to, :spent_time, :estimation, :due_date, :is_archived, :created_by, :updated_by, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, :new_sort_id, :resource_id, :table_name, :start_date) RETURNING id
                """
                )
                query = query.params(project_id=project_id, module_id=current_module_id, current_column_id=current_column_id, status=status, priority=priority, title=title, card_identifier=card_identifier, description=description, assigned_to=assigned_to, spent_time=spent_time, estimation=estimation, due_date=due_date, is_archived=is_archived, created_by=owner_username, updated_by=owner_username, new_sort_id=new_sort_id, resource_id=resource_id, table_name=payload['table_name'], start_date=start_date)
                result = db_conn.execute(query)
                db_conn.commit()
                new_id = result.scalar()
                time.sleep(4)
                if new_id and new_id > 0:
                    admin_app_notification_util.generate_admin_app_notification(store_id, admin_app_notification_util.AdminAppNotificationUtil.TICKET_ASSIGNED_BY_MAPPING, new_id)
                    task.submit_task('send_project_notification', (store_id, project_notification_util.ProjectNotifications.TICKET_ASSIGNED, new_id))
                    # Update the card_index in the agile_project_cardindex table
                    db_conn.execute(
                        text(f"UPDATE agile_project_cardindex SET card_index = :new_card_index WHERE project_id = :project_id"),
                        {'new_card_index': new_card_index, 'project_id': project_id}
                    )
                    if is_from_custom_field:
                        return {'card_id': new_id}
                
    except Exception as ex:
        logger.error(str(traceback.format_exc()))
        raise ex
    finally:        
        if db_conn:
            db_conn.commit()
            db_conn.close()
    return None

def update_ticket(store_id, payload):
    db_conn = pg_db.get_connection(store_id)
    try:
        ticket_id = payload.get('ticket_id')
        project_id = payload.get('project_id')
        title = payload.get('title', '')
        status = payload.get('status', None)
        updated_by = payload.get('updated_by')
        due_date = payload.get('due_date', None)
        if not ticket_id or not project_id:
            raise Exception('Invalid request')
        
        updated_column_id = None
        query = text (f"""SELECT apc.id, apc.module_id, apc.current_column_id FROM agile_project_cards apc WHERE apc.id = :ticket_id and apc.project_id = :project_id;""")
        query = query.params(ticket_id=ticket_id, project_id=project_id)
        existing_card = db_conn.execute(query).fetchone()   
        
        if existing_card and status:
            # query = text (f"""SELECT pptm.pipeline_db_tables_id FROM pipeline_project_table_mapping pptm WHERE pptm.project_id = :project_id;""")
            # query = query.params(project_id=project_id)
            # mapping_data = db_conn.execute(query).fetchone()
            # status_module_table_id = mapping_data[0]
            # if :
            query = text (f"""SELECT pptm.id, pptm.pipeline_db_tables_id, pcm.project_column_id, pcm.project_id
                            FROM pipeline_project_table_mapping pptm
                            JOIN pipeline_column_mapping pcm ON pcm.project_table_mapping_id = pptm.id
                            WHERE pcm.table_column_value = :status AND pptm.project_id = :project_id""")
            # query = text (f"""SELECT pcm.project_column_id, pcm.project_id FROM pipeline_column_mapping pcm JOIN pipeline_project_table_mapping pptm ON pcm.project_table_mapping_id = pptm.id WHERE pcm.table_column_value = :status and pptm.pipeline_db_tables_id = :status_module_table_id;""")
            query = query.params(status=status, project_id=project_id)
            status_column = db_conn.execute(query).fetchone()
            if status_column:
                updated_column_id = status_column[2]
                
        if updated_column_id:
            if due_date:
                query = text (f"""UPDATE agile_project_cards SET current_column_id = :updated_column_id, title = :title, updated_by = :updated_by, updated_at = :updated_at, due_date = :due_date  WHERE id = :ticket_id;""")
                query = query.params(updated_column_id=updated_column_id, title=title, ticket_id=ticket_id, updated_by=updated_by, updated_at= datetime.now(timezone.utc), due_date=due_date)
            else:
                query = text (f"""UPDATE agile_project_cards SET current_column_id = :updated_column_id, title = :title, updated_by = :updated_by, updated_at = :updated_at  WHERE id = :ticket_id;""")
                query = query.params(updated_column_id=updated_column_id, title=title, ticket_id=ticket_id, updated_by=updated_by, updated_at= datetime.now(timezone.utc))
            db_conn.execute(query)

    except Exception as e:
        logger.error(traceback.format_exc())
        raise e
    finally:
        if db_conn:
            db_conn.commit()
            db_conn.close()

def update_resource_from_ticket(store_id, payload):
    db_conn = pg_db.get_connection(store_id)
    try:
        ticket_id = payload.get('ticket_id')
        project_id = payload.get('project_id')
        current_column_id = payload.get('current_column_id')
        updated_by = payload.get('updated_by')
        if not ticket_id or not project_id:
            raise Exception('Invalid request')
        
        # Fetch the resource details from the agile_project_cards table
        query = text (f"""SELECT pipeline_record_id, table_name FROM agile_project_cards WHERE id = :ticket_id and project_id = :project_id;""")
        query = query.params(ticket_id=ticket_id, project_id=project_id)
        card_details = db_conn.execute(query).fetchone()   
        
        if card_details:
            resource_id = card_details[0]
            db_table_name = card_details[1]
            if db_table_name not in ['bo_purchase_orders', 'orders']:
                query = text (f"""SELECT pcm.table_column_value, pptm.db_table_column
                                    FROM pipeline_column_mapping pcm
                                    JOIN pipeline_project_table_mapping pptm ON pcm.project_id = pptm.project_id
                                    WHERE pcm.project_id = :project_id AND pcm.project_column_id = :current_column_id;""")
                # query = text (f"""SELECT table_column_value from pipeline_column_mapping where project_id = :project_id and project_column_id = :current_column_id;""")
                query = query.params(project_id=project_id, current_column_id=current_column_id)
                mapping_data = db_conn.execute(query).fetchone()
                status_value = None
                column_name = None
                if mapping_data:
                    status_value = mapping_data[0]
                    column_name = mapping_data[1]
                    
                if status_value and column_name and column_name == 'status':
                    column_check_query = text("""
                        SELECT column_name
                        FROM information_schema.columns
                        WHERE table_name = :table_name AND column_name = 'id';
                    """)
                    column_check_result = db_conn.execute(column_check_query, {'table_name': db_table_name}).fetchone()
                    if column_check_result:
                        query = text (f"""UPDATE {db_table_name} SET {column_name} = :status_value, updated_by = :updated_by, updated_at = :updated_at  WHERE id = :resource_id;""")
                        query = query.params(resource_id=resource_id, status_value=status_value, updated_by=updated_by, updated_at= datetime.now(timezone.utc))
                        db_conn.execute(query)
                    else:
                        m_result = db_conn.execute(text(f"""SELECT mapping_column FROM pipeline_db_tables WHERE table_name = '{db_table_name}';""")).fetchone()
                        main_column = m_result[0] if m_result else column_name
                        query = text (f"""UPDATE {db_table_name} SET {column_name} = :status_value, updated_by = :updated_by, updated_at = :updated_at  WHERE {main_column} = :resource_id;""")
                        query = query.params(resource_id=resource_id, status_value=status_value, updated_by=updated_by, updated_at= datetime.now(timezone.utc))
                        db_conn.execute(query)

    except Exception as e:
        logger.error(traceback.format_exc())
        raise e
    finally:
        if db_conn:
            db_conn.commit()
            db_conn.close()

def map_all_resource_with_ticket(store_id, payload):
    db_conn = pg_db.get_connection(store_id)
    try:
        project_id = payload.get('project_id')
        if not project_id:
            raise Exception('Invalid request')
        
        # Fetch the mapping info from the pipeline_project_table_mapping table
        query = text (f"""SELECT pptm.id, pptm.project_id, pptm.pipeline_db_tables_id, pptm.ticket_name, pptm.default_assignee, pptm.db_table_column, pdt.table_name, pdt.title FROM pipeline_project_table_mapping pptm JOIN pipeline_db_tables pdt ON pptm.pipeline_db_tables_id = pdt.id  WHERE pptm.project_id = :project_id;""")
        query = query.params(project_id=project_id)
        mapping_details = db_conn.execute(query).fetchone()   
        
        if mapping_details:
            project_id = mapping_details[1]
            ticket_name = mapping_details[3]
            default_assignee = mapping_details[4]
            db_table_column = mapping_details[5]
            db_table_name = mapping_details[6]
            # title = mapping_details[7]

            # Fetch the project details
            project_details = _fetch_project_detail(db_conn, mapping_details[1])
            if project_details:
                project_id = project_details[0]['id']
                default_column_id = project_details[0]['default_column_id']
                default_module_id = project_details[0]['default_module_id']
                owner_username = project_details[0]['owner_username']
                project_initials = project_details[0]['project_initials']

                current_column_id = int(default_column_id) if default_column_id else 0
                current_module_id = int(default_module_id) if default_module_id else 0
                status = project_details[0]['status']
                priority = project_details[0]['priority']
                assigned_to = default_assignee or owner_username

                spent_time_value = '0h'
                spent_time_hours = parse_time(spent_time_value) 
                spent_time = f"{spent_time_hours} hour"

                estimation_value = '0h'
                estimation_time_hours = parse_time(estimation_value)
                estimation = f"{estimation_time_hours} hour"

                due_date = None
                is_archived = False

            #fetch the resource data and create tickets
            query = text (f"""SELECT id, {db_table_column}, {ticket_name} from {db_table_name} where 1=1;""")
            resource_data = db_conn.execute(query).fetchall()
            if resource_data:
                for resource in resource_data:
                    resource_id = resource[0]
                    status_value = resource[1]
                    ticket_title = resource[2]
                    
                    title = ticket_title or ''
                    description = ''

                    card_index_query = db_conn.execute(
                        text(f"SELECT card_index FROM agile_project_cardindex WHERE project_id = :project_id"),
                        {'project_id': project_id}
                    )
                    card_index = card_index_query.fetchone()[0]
                    
                    new_card_index = card_index + 1
                    card_identifier = f"{project_initials}-{new_card_index}"

                    # Fetch the highest sort_id for the given project
                    sort_id_query = db_conn.execute(
                        text(f"SELECT MAX(sort_id) FROM agile_project_cards WHERE project_id = :project_id AND module_id = :module_id AND current_column_id = :current_column_id"),
                        {'project_id': project_id, 'module_id': current_module_id, 'current_column_id': current_column_id}
                    )
                    highest_sort_id = sort_id_query.scalar()
                    new_sort_id = highest_sort_id + 1 if highest_sort_id is not None else 1

                    query = text(
                    f"""INSERT INTO agile_project_cards (project_id, module_id, current_column_id, status, priority, title, card_identifier, description, assigned_to, spent_time, estimation, due_date, is_archived, created_by, updated_by, created_at, updated_at, sort_id, pipeline_record_id, table_name)
                        VALUES (:project_id, :module_id, :current_column_id, :status, :priority, :title, :card_identifier, :description, :assigned_to, :spent_time, :estimation, :due_date, :is_archived, :created_by, :updated_by, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, :new_sort_id, :resource_id, :table_name);
                    """
                    )
                    query = query.params(project_id=project_id, module_id=current_module_id, current_column_id=current_column_id, status=status, priority=priority, title=title, card_identifier=card_identifier, description=description, assigned_to=assigned_to, spent_time=spent_time, estimation=estimation, due_date=due_date, is_archived=is_archived, created_by=owner_username, updated_by=owner_username, new_sort_id=new_sort_id, resource_id=resource_id, table_name=db_table_name)
                    result = db_conn.execute(query)

                    if result.rowcount > 0:
                        # Update the card_index in the agile_project_cardindex table
                        db_conn.execute(
                            text(f"UPDATE agile_project_cardindex SET card_index = :new_card_index WHERE project_id = :project_id"),
                            {'new_card_index': new_card_index, 'project_id': project_id}
                        )
    except Exception as ex:
        logger.error(str(traceback.format_exc()))
        raise ex
    finally:        
        if db_conn:
            db_conn.commit()
            db_conn.close() 
            

def _fetch_project_detail(conn, id):    
    query = text (f"""SELECT ap.id, ap.name, ap.description, ap.owner_username, ap.due_date, ap.is_archived, ap.updated_by, ap.created_at, ap.updated_at, ap.project_initials, ap.project_type, apc.id AS default_column_id, apm.id AS default_module_id 
                  from agile_projects AS ap 
                  LEFT JOIN agile_project_columns AS apc ON ap.id = apc.project_id AND apc.is_default = true 
                  LEFT JOIN agile_project_modules AS apm ON ap.id = apm.project_id AND apm.is_default = true
                  where ap.id = :id;""")
    query = query.params(id=id)
    result = conn.execute(query)
    result = result.fetchone()
    data = []

    p_query = (f"""SELECT id from agile_card_priorities where is_default = true;""")
    p_result = conn.execute(text(p_query)).fetchone()
    priority_id = p_result[0] if p_result else None
    s_query = (f"""SELECT id from agile_card_status where is_default = true;""")
    s_result = conn.execute(text(s_query)).fetchone()
    status_id = s_result[0] if s_result else None
    
    if result:         
        row_data = {
            'id': result[0],
            'name': result[1],
            'description': result[2],
            'owner_username': result[3],
            'due_date': result[4],
            'is_archived': result[5],
            'updated_by': result[6],
            'created_date': result[7].strftime("%Y-%m-%d %H:%M:%S") if isinstance(result[7], datetime) else str(result[7]),
            'modified_at': result[8].strftime("%Y-%m-%d %H:%M:%S") if isinstance(result[8], datetime) else str(result[8]),
            'project_initials': result[9],
            'project_type': result[10],
            'default_column_id': result[11],
            'default_module_id': result[12],
            'priority': priority_id,
            'status': status_id
        }
        data.append(row_data)
    return data

def _fetch_resource_detail(conn, id, db_table_name, column_name):    
    # Check if the "id" column exists in the table
    column_check_query = text("""
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = :table_name AND column_name = 'id';
    """)
    column_check_result = conn.execute(column_check_query, {'table_name': db_table_name}).fetchone()
    if column_check_result:
        query = text (f"""SELECT {column_name} FROM {db_table_name} where id = :resource_id;""")
        query = query.params(resource_id=id)
    else:
        m_result = conn.execute(text(f"""SELECT mapping_column FROM pipeline_db_tables WHERE table_name = '{db_table_name}';""")).fetchone()
        main_column = m_result[0] if m_result else column_name
        query = text(f"""SELECT {column_name} FROM {db_table_name} WHERE {main_column} = :resource_id;""")
        query = query.params(resource_id=id)
    result = conn.execute(query).fetchone()
    data = result[0] if result else ''
    return data

def parse_time(time_str):
    total_hours = 0
    
    parts = time_str.split()
    for part in parts:
        if part.endswith('d'):
            days = int(part[:-1])
            total_hours += days * 8 
        elif part.endswith('w'):
            weeks = int(part[:-1])
            total_hours += weeks * 5 * 8
        elif part.endswith('h'):
            hours = int(part[:-1])
            total_hours += hours
        elif part.endswith('m'):
            minutes = int(part[:-1])
            total_hours += minutes / 60
        else:
            raise ValueError("Invalid time format")

    return total_hours