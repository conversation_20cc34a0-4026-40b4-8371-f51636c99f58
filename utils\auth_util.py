from utils import jwt_util, redis_util
import datetime
import uuid
import logging
import traceback

logger = logging.getLogger()

token_expiry_duration = 7*24*60*60
jwt_token_algorithm = 'HS256'

def get_jwt_secret(store_id):
    payload = redis_util.get_store_secret(store_id)
    if not payload:
        payload = {
            "client_id": jwt_util.secret_generator(24),
            "secret": jwt_util.secret_generator(),
        }
        redis_util.update_store_secret(store_id, payload)
    return payload

def generate_token(store_id, customer_email, expiry_time):
    secret_payload = get_jwt_secret(store_id)
    client_id = secret_payload["client_id"]
    secret = secret_payload["secret"]
    iat = int(datetime.datetime.now().timestamp())
    exp = iat + expiry_time
    jti = uuid.uuid4().hex
    payload = dict(iss=client_id, iat=iat, exp=exp, jti=jti,
                   store_id=store_id, email=customer_email)
    return jwt_util.encode(payload, secret, algorithm=jwt_token_algorithm), exp