from mongo_db import catalog_db, store_info_db
from utils import bc_util, redis_util, product_util, store_util
import plugin
from pymongo import TEXT
import mongo_db
from urllib.parse import urlparse
import logging
import traceback

logger = logging.getLogger()

def fetch_webhooks(store):
    # Fetch the variant details from BigCommerce
    url = f'v3/hooks'
    bc_api = store_util.get_bc_api_creds(store)
    res = bc_util.call_api(bc_api, "GET", url, query_params={})

    # success ...   
    if res.status_code == 200:
        return res.json()
    else:
        # unprocess able entity...
        return []
