import datetime
import pg_db_utils
import pg_db as db
from pg_db import orders_db, tablemeta_db
from sqlalchemy import text
import pandas as pd
from utils import store_util
import logging
import traceback
import requests

logger = logging.getLogger()

def fetch_order_line_items(store, order_id, last_updated_datetime):
    line_items = []
    discounts = []
    page = 1
    order_specific_sv_cost = get_order_line_item_sv_cost(store['id'], int(order_id)) or {}

    while True:
        query_params = {
            "limit": 250,
            "page": page
        }

        req_body = {
            "query_params": query_params,
            "method": "GET",
            "url": "v2/orders/" + str(order_id) + "/products"
        }

        res = pg_db_utils.process_api(req_body, store)

        if res and res['status_code'] == 200 and res['data']:
            if len(res['data']) > 0:
                for row in res['data']:
                    row['last_updated_datetime'] = last_updated_datetime
                    # Defensive handling of sv_cost assignment
                    try:
                        line_item_id = int(row.get('id', 0))
                        row['sv_cost'] = order_specific_sv_cost.get(line_item_id, 0)
                    except Exception as e:
                        logger.warning(f"Unable to resolve sv_cost for row: {row}. Error: {str(e)}")
                        row['sv_cost'] = 0
                    line_items.append(row)

                    if "applied_discounts" in row and len(row["applied_discounts"]) > 0:
                        for discount in row["applied_discounts"]:
                            discount['line_item_id'] = row["id"]
                            discount['order_id'] = row["order_id"]
                            discount['last_updated_datetime'] = last_updated_datetime
                            discounts.append(discount)
                
                if len(res['data']) < 250:
                    break
            else:
                break
        else:
            break        

        page = page + 1
    return line_items, discounts

def fetch_order_shipping_address(order_id, last_updated_datetime, store):
    addresses = []
    query_params = {
        "limit": 250
    }

    req_body = {
        "query_params": query_params,
        "method": "GET",
        "url": "v2/orders/" + str(order_id) + "/shipping_addresses"
    }

    res = pg_db_utils.process_api(req_body, store)

    if res and res['status_code'] == 200 and res['data']:
        if len(res['data']) > 0:
            for row in res[ "data"]:
                row['last_updated_datetime'] = last_updated_datetime
                addresses.append(row)
    return addresses

def fetch_and_update_orders(store, query_params={}):
    orders = []
    billing_addresses = []
    shipping_addresses = []
    line_items = []
    line_item_discounts = []
    page = 1
    express_orders = []
    express_channel_id = store_util.get_bc_api_creds(store)["channel_id"]

    while True:
        logger.info("Fetching Orders page: " + str(page))
        query_params["limit"] = 250
        query_params["page"] = page

        req_body = {
            "query_params": query_params,
            "method": "GET",
            "url": "v2/orders"
        }

        res = pg_db_utils.process_api(req_body, store)

        if res and res['status_code'] == 200 and res['data']:
            if len(res['data']) > 0:
                order_archived = False
                # coupon_id = ""
                # coupon_code = ""
                for row in res['data']:
                    logger.info("Processing order " + str(row["id"]) + ", modified at: " + row["date_modified"])
                    coupon_details = _fetch_coupon_details(store, row["id"])
                    channel_id = row.get("channel_id", 1)
                    row['coupon_code'] = coupon_details.get("code", "")
                    row['coupon_id'] = coupon_details.get("coupon_id", "")
                    if channel_id == express_channel_id:
                        express_orders.append(row)

                    orders.append(row)
                    
                    if "billing_address" in row:
                        ba = row["billing_address"]
                        ba['order_id'] = row["id"]
                        ba['last_updated_datetime'] = row['date_modified']
                        billing_addresses.append(ba)

                    _shipping_address = fetch_order_shipping_address(row["id"], row['date_modified'], store)
                    if _shipping_address and len(_shipping_address) > 0:
                        shipping_addresses.extend(_shipping_address)

                    _line_items, _discounts = fetch_order_line_items(store, row["id"], row['date_modified'])
                    if _line_items and len(_line_items) > 0:
                        line_items.extend(_line_items)
                    if _discounts and len(_discounts) > 0:
                        line_item_discounts.extend(_discounts)
                    
                process_data(store['id'], orders, billing_addresses, shipping_addresses, line_items, line_item_discounts)
                if len(res['data']) < 250:
                    break
                orders = []
                billing_addresses = []
                shipping_addresses = []
                line_items = []
                line_item_discounts = []
            else:
                break
        else:
            break
        page = page + 1
    if len(express_orders) > 0:
        from task import submit_task
        submit_task("update_express_order_table", (store['id'], express_orders))

def _fetch_coupon_details(store, order_id):
    req_body = {
        "method": "GET",
        "url": "v2/orders/" + str(order_id) + "/coupons"
    }
    res = pg_db_utils.process_api(req_body, store)
    if res and res['status_code'] == 200 and res['data']:
        coupon = res['data'][0]
        return {
            "code": coupon.get("code"),
            "coupon_id": coupon.get("coupon_id")
        }
    return {"code": "", "coupon_id": ""}

def build_orders_model(order):
    order_archived = False
    # coupon_id = ""
    # coupon_code = ""
    date_format = '%a, %d %b %Y %H:%M:%S %z'
    created_date = datetime.datetime.strptime(order['date_created'], date_format)
    modified_date = datetime.datetime.strptime(order['date_modified'], date_format)
    shipped_date = None
    if order["date_shipped"] and order["date_shipped"].strip() != "":
        shipped_date = datetime.datetime.strptime(order['date_shipped'], date_format)

    created_date_day = created_date.day
    created_date_month = created_date.month
    created_date_month_str = created_date.strftime("%b")
    created_date_year = created_date.year
    created_date_qtr = 'q' + str(int((created_date_month - 1) / 3) + 1)

    return orders_db.Orders(
        order_id=int(order['id']),
        order_status_id = int(order['status_id']),
        order_archived = order_archived,
        order_source = order['order_source'],
        external_source = order['external_source'],
        external_id = order['external_id'],
        external_merchant_id = order['external_merchant_id'],
        channel_id = int(order['channel_id']),
        customer_id = int(order['customer_id']),
        cart_id = order['cart_id'],
        payment_status = order['payment_status'],
        payment_provider_id = order['payment_provider_id'],
        payment_method_type = order['payment_method'],
        credit_card_type = order['credit_card_type'],
        total_items = int(order['items_total']),
        total_items_shipped = int(order['items_shipped']),
        coupon_id = str(order['coupon_id']),
        coupon_code = str(order['coupon_code']),
        coupon_discount = float(order['coupon_discount']),
        gift_certificate_amount_redeemed = float(order['gift_certificate_amount']),
        store_credit_redeemed = float(order['store_credit_amount']),
        sub_total_excluding_tax = float(order['subtotal_ex_tax']),
        sub_total_including_tax = float(order['subtotal_inc_tax']),
        sub_total_tax = float(order['subtotal_tax']),
        base_shipping_cost = float(order['base_shipping_cost']),
        shipping_excluding_tax = float(order['shipping_cost_ex_tax']),
        shipping_including_tax = float(order['shipping_cost_inc_tax']),
        shipping_tax = float(order['shipping_cost_tax']),
        base_handling_cost = float(order['base_handling_cost']),
        handling_excluding_tax = float(order['handling_cost_ex_tax']),
        handling_including_tax = float(order['handling_cost_inc_tax']),
        handling_tax = float(order['handling_cost_tax']),
        base_wrapping_cost = float(order['base_wrapping_cost']),
        wrapping_excluding_tax = float(order['wrapping_cost_ex_tax']),
        wrapping_including_tax = float(order['wrapping_cost_inc_tax']),
        wrapping_tax = float(order['wrapping_cost_tax']),
        total_tax = float(order['total_tax']),
        total_excluding_tax = float(order['total_ex_tax']),
        total_including_tax = float(order['total_inc_tax']),
        default_currency_code = order['default_currency_code'],
        currency_exchange_rate = float(order['currency_exchange_rate']),
        refund_amount = float(order['refunded_amount']),
        date_shipped = shipped_date,
        order_created_date_time = created_date,
        last_updated_datetime = modified_date,
        order_day = created_date_day,
        order_month = created_date_month_str,
        order_year = created_date_year,
        order_qtr = created_date_qtr
    )

def build_billing_address_model(address):
    return orders_db.OrderBillingAddress(
        order_id=int(address['order_id']),
        first_name = address['first_name'],
        last_name = address['last_name'],
        company = address['company'],
        street_1 = address['street_1'],
        street_2 = address['street_2'],
        city = address['city'],
        state = address['state'],
        zip = address['zip'],
        country = address['country'],
        country_code = address['country_iso2'],
        phone = address['phone'],
        email = address['email'],
        last_updated_datetime = address['last_updated_datetime']
    )

def build_shipping_address_model(address):
    return orders_db.OrderShippingAddress(
        order_id=int(address['order_id']),
        shipping_address_id=int(address['id']),
        first_name = address['first_name'],
        last_name = address['last_name'],
        company = address['company'],
        street_1 = address['street_1'],
        street_2 = address['street_2'],
        city = address['city'],
        state = address['state'],
        postal_code = address['zip'],
        country = address['country'],
        country_code = address['country_iso2'],
        phone = address['phone'],
        email = address['email'],
        last_updated_datetime = address['last_updated_datetime']
    )



def build_line_item_model(line_item):

    parent_order_product_id = None
    option_set_id = None
    return_id = None

    if line_item['parent_order_product_id']:
        parent_order_product_id = int(line_item['parent_order_product_id'])

    if line_item['option_set_id']:
        option_set_id = int(line_item['option_set_id'])

    if line_item['return_id']:
        return_id = int(line_item['return_id'])
    

    return orders_db.OrderLineItems(
        order_id=int(line_item['order_id']),
        order_line_item_id = int(line_item['id']),
        parent_order_product_id = parent_order_product_id,
        order_address_id = int(line_item['order_address_id']),
        product_id = int(line_item['product_id']),
        variant_id = int(line_item['variant_id']),
        variant_sku = line_item['sku'],
        option_set_id = option_set_id,
        external_id = line_item['external_id'],
        quantity = int(line_item['quantity']),
        quantity_shipped = int(line_item['quantity_shipped']),                        
        product_price = float(line_item['base_price']),
        base_cost_price = float(line_item['base_cost_price']),
        product_price_after_discount = float(line_item['base_price']),
        price_ex_tax = float(line_item['price_ex_tax']),
        price_inc_tax = float(line_item['price_inc_tax']),
        price_tax = float(line_item['price_tax']),
        base_total = float(line_item['base_total']),
        product_total_cost = float(line_item['base_total']),
        total_ex_tax = float(line_item['total_ex_tax']),
        total_inc_tax = float(line_item['total_inc_tax']),
        total_tax = float(line_item['total_tax']),
        is_refunded = line_item['is_refunded'],
        refund_amount = float(line_item['refund_amount']),
        return_id = return_id,
        fixed_shipping_cost = float(line_item['fixed_shipping_cost']),
        base_wrapping_cost = float(line_item['base_wrapping_cost']),
        wrapping_cost_ex_tax = float(line_item['wrapping_cost_ex_tax']),
        wrapping_cost_inc_tax = float(line_item['wrapping_cost_inc_tax']),
        wrapping_cost_tax = float(line_item['wrapping_cost_tax']),
        is_bundled_product = line_item['is_bundled_product'],
        last_updated_datetime = line_item['last_updated_datetime'],
        sv_cost = line_item['sv_cost']
    )

def build_line_item_discount_model(line_item_discount):
    return orders_db.OrderLineItemDiscount(
        order_id=int(line_item_discount['order_id']),
        order_line_item_id = int(line_item_discount['line_item_id']),
        discount_id = line_item_discount['id'],
        discount_amount = line_item_discount['amount'],
        discount_name = line_item_discount['name'],
        discount_code = line_item_discount['code'],
        discount_level = line_item_discount['target'],
        last_updated_datetime = line_item_discount['last_updated_datetime']
    )

def delete_missing_line_items(store_id, order_id, received_line_item_ids):
    """
    Deletes line items from the database that are not in the received list of line items.
    
    :param store_id: The store ID for database session.
    :param order_id: The ID of the order for which line items are being processed.
    :param received_line_item_ids: A list of line item IDs received from BigCommerce.
    """
    session = db.get_session(store_id)
    try:
        # Find and delete line items that are not in the received list
        session.query(orders_db.OrderLineItems).filter(
            orders_db.OrderLineItems.order_id == order_id,
            ~orders_db.OrderLineItems.order_line_item_id.in_(received_line_item_ids)
        ).delete(synchronize_session=False)
    except Exception as ex:
        logger.error(f"Error deleting missing line items for store_id {store_id}, order_id {order_id}")
        logger.error(traceback.format_exc())
    finally:
        session.commit()
        session.close()

def get_order_line_item_sv_cost(store_id, order_id):
    session = db.get_session(store_id)
    try:
        data = {}
        result = session.query(orders_db.OrderLineItems.order_line_item_id,
                               orders_db.OrderLineItems.sv_cost
                               ).filter(orders_db.OrderLineItems.order_id == order_id).all()
        if result:
            for row in result:
                data[int(row[0])] = row[1] if row[1] is not None else 0
        return data
    except Exception as ex:
        logger.error(f"Error fetching sv_cost for store_id {store_id}, order_id {order_id}")
        logger.error(traceback.format_exc())
        return {}
    finally:
        session.close()

def save_data(store_id, dto_list, dto_to_model_convertor):
    session = db.get_session(store_id)
    try:
        for dto in dto_list:
            model = dto_to_model_convertor(dto)
            session.merge(model)
    except Exception as ex:
        logger.error("Caught error in pg_order_util.save_data: store_id: " + str(store_id))
        logger.error(traceback.format_exc())
    finally:
        session.commit()
        session.close()

def process_data(store_id, orders, billing_addresses, shipping_addresses, line_items, line_item_discounts):
    if len(orders) > 0:
        save_data(store_id, orders, build_orders_model)
    if len(billing_addresses) > 0:
        save_data(store_id, billing_addresses, build_billing_address_model)
    if len(shipping_addresses) > 0:
        save_data(store_id, shipping_addresses, build_shipping_address_model)
    if len(line_item_discounts) > 0:
        save_data(store_id, line_item_discounts, build_line_item_discount_model)
    if len(line_items) > 0:
        save_data(store_id, line_items, build_line_item_model)

        # Extract unique order IDs and line item IDs from the received line items
        order_line_item_map = {}
        for item in line_items:
            order_id = item['order_id']
            if order_id not in order_line_item_map:
                order_line_item_map[order_id] = []
            order_line_item_map[order_id].append(item['id'])

        # Delete missing line items for each order
        for order_id, received_line_item_ids in order_line_item_map.items():
            delete_missing_line_items(store_id, order_id, received_line_item_ids)

def increment_update(store_id):
    store = store_util.get_store_by_id(store_id)
    min_date_created_iso = (datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(days=365)).isoformat()
    min_date_modified_iso = None
    last_modified_date = None
    session = db.get_session(store_id)
    try:
        last_modified_date = orders_db.Orders.get_last_modified_at(store_id, session)
        if not last_modified_date:
            last_modified_date = tablemeta_db.TableMetadata.get_last_updated_at(store_id, db.orders_table, session)
    except Exception as ex:
        logger.error("Caught error in pg_order_util.increment_update: store_id: " + str(store_id))
        logger.error(traceback.format_exc())
    finally:
        session.close()

    if last_modified_date:
        min_date_modified_iso = last_modified_date.isoformat()
    else:
        min_date_modified_iso = (datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(hours=3)).isoformat()
    
    query_params = {
        "sort": "date_created:asc",
        "min_date_modified": min_date_modified_iso,
        "min_date_created": min_date_created_iso
    }
    
    fetch_and_update_orders(store, query_params)
    
def update_missing_line_items(store):
    line_items = []
    line_item_discounts = []
    session = db.get_session(store['id'])
    try:
        q = session.query().with_entities(orders_db.Orders.order_id, orders_db.Orders.order_created_date_time).order_by(orders_db.Orders.order_id)
        count = 0
        for row in q:
            order_id = row.order_id
            ols = session.query(orders_db.OrderLineItems).where(orders_db.OrderLineItems.order_id == order_id)
            ol_count = ols.count()
            if ol_count == 0:
                _line_items, _discounts = fetch_order_line_items(store, order_id, row['order_created_date_time'])
                if _line_items and len(_line_items) > 0:
                    line_items.extend(_line_items)
                if _discounts and len(_discounts) > 0:
                    line_item_discounts.extend(_discounts)
                count = count + 1
            if count >= 50:
                count = 0
                if len(line_items) > 0:
                    save_data(store['id'], line_items, build_line_item_model)
                    line_items = []
                if len(line_item_discounts) > 0:
                    save_data(store['id'], line_item_discounts, build_line_item_discount_model)
                    line_item_discounts = []

        if len(line_items) > 0:
            save_data(store['id'], line_items, build_line_item_model)
            line_items = []
        if len(line_item_discounts) > 0:
            save_data(store['id'], line_item_discounts, build_line_item_discount_model)
            line_item_discounts = []
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        session.close()

def update_missing_shipping_address(store):
    shipping_addresses = []
    session = db.get_session(store['id'])
    try:
        q = session.query(orders_db.Orders).join(orders_db.OrderShippingAddress,orders_db.OrderShippingAddress.order_id == orders_db.Orders.order_id, isouter=True).where(orders_db.OrderShippingAddress.order_id == None)
        for row in q:
            _shipping_address = fetch_order_shipping_address(row.order_id, row.order_created_date_time, store)
            if _shipping_address and len(_shipping_address) > 0:
                shipping_addresses.extend(_shipping_address)
            if len(shipping_addresses) >= 50:
                save_data(store['id'], shipping_addresses, build_shipping_address_model)
                shipping_addresses = []

        if len(shipping_addresses) > 0:
            save_data(store['id'], shipping_addresses, build_shipping_address_model)
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        session.close()

def fetch_historical_orders(store, query_params={}):
    orders = []
    billing_addresses = []
    shipping_addresses = []
    line_items = []
    line_item_discounts = []
    page = 1
    while True:
        logger.info("Fetching Orders page: " + str(page))
        query_params["limit"] = 250
        query_params["page"] = page

        req_body = {
            "query_params": query_params,
            "method": "GET",
            "url": "v2/orders"
        }

        res = pg_db_utils.process_api(req_body, store)

        if res and res['status_code'] == 200 and res['data']:
            if len(res['data']) > 0:
                order_archived = False
                coupon_id = ""
                coupon_code = ""
                for row in res['data']:
                    logger.info("Processing order " + str(row["id"]) + ", created at: " + row["date_created"])
                    
                    orders.append(row)
                   
                    if "billing_address" in row:
                        ba = row["billing_address"]
                        ba['order_id'] = row["id"]
                        ba['last_updated_datetime'] = row['date_modified']
                        billing_addresses.append(ba)

                    _shipping_address = fetch_order_shipping_address(row["id"], row['date_modified'], store)
                    if _shipping_address and len(_shipping_address) > 0:
                        shipping_addresses.extend(_shipping_address)

                    _line_items, _discounts = fetch_order_line_items(store, row["id"], row['date_modified'])
                    if _line_items and len(_line_items) > 0:
                        line_items.extend(_line_items)
                    if _discounts and len(_discounts) > 0:
                        line_item_discounts.extend(_discounts)

                if len(orders) > 0:
                    save_data(store['id'], orders, build_orders_model)
                    orders = []
                if len(billing_addresses) > 0:
                    save_data(store['id'], billing_addresses, build_billing_address_model)
                    billing_addresses = []
                if len(shipping_addresses) > 0:
                    save_data(store['id'], shipping_addresses, build_shipping_address_model)
                    shipping_addresses = []
                if len(line_item_discounts) > 0:
                    save_data(store['id'], line_item_discounts, build_line_item_discount_model)
                    line_item_discounts = []
                if len(line_items) > 0:
                    save_data(store['id'], line_items, build_line_item_model)
                    line_items = []
                if len(res['data']) < 250:
                    break
            else:
                break
        else:
            break
        page = page + 1

    return orders, billing_addresses, shipping_addresses, line_items, line_item_discounts

def historical_order_update(store):
    min_date_created_iso = "2020-01-01T00:00:00"
    max_date_created_iso = "2020-01-31T23:59:59"
    
    query_params = {
            "sort": "date_created:asc",
            "max_date_created": max_date_created_iso,
            "min_date_created": min_date_created_iso
    }
    fetch_historical_orders(store, query_params)

def get_db_order(df_row):
    date_shipped = None
    if df_row['date_shipped'] and df_row['date_shipped'] != 0:
        date_shipped = df_row['date_shipped']
    return orders_db.Orders(
        order_id=df_row['order_id'],
        order_status_id = df_row['order_status_id'],
        order_archived = df_row['order_archived'],
        order_source = df_row['order_source'],
        external_source = df_row['external_source'],
        external_id = df_row['external_id'],
        external_merchant_id = df_row['external_merchant_id'],
        channel_id = df_row['channel_id'],
        customer_id = df_row['customer_id'],
        cart_id = df_row['cart_id'],
        payment_status = df_row['payment_status'],
        payment_provider_id = df_row['payment_provider_id'],
        payment_method_type = df_row['payment_method_type'],
        credit_card_type = df_row['credit_card_type'],
        total_items = df_row['total_items'],
        total_items_shipped = df_row['total_items_shipped'],
        coupon_id = df_row['coupon_id'],
        coupon_code = df_row['coupon_code'],
        coupon_discount = df_row['coupon_discount'],
        gift_certificate_amount_redeemed = df_row['gift_certificate_amount_redeemed'],
        store_credit_redeemed = df_row['store_credit_redeemed'],
        sub_total_excluding_tax = df_row['sub_total_excluding_tax'],
        sub_total_including_tax = df_row['sub_total_including_tax'],
        sub_total_tax = df_row['sub_total_tax'],
        base_shipping_cost = df_row['base_shipping_cost'],
        shipping_excluding_tax = df_row['shipping_excluding_tax'],
        shipping_including_tax = df_row['shipping_including_tax'],
        shipping_tax = df_row['shipping_tax'],
        base_handling_cost = df_row['base_handling_cost'],
        handling_excluding_tax = df_row['handling_excluding_tax'],
        handling_including_tax = df_row['handling_including_tax'],
        handling_tax = df_row['handling_tax'],
        base_wrapping_cost = df_row['base_wrapping_cost'],
        wrapping_excluding_tax = df_row['wrapping_excluding_tax'],
        wrapping_including_tax = df_row['wrapping_including_tax'],
        wrapping_tax = df_row['wrapping_tax'],
        total_tax = df_row['total_tax'],
        total_excluding_tax = df_row['total_excluding_tax'],
        total_including_tax = df_row['total_including_tax'],
        default_currency_code = df_row['default_currency_code'],
        currency_exchange_rate = df_row['currency_exchange_rate'],
        refund_amount = df_row['refund_amount'],
        date_shipped = date_shipped,
        order_created_date_time = df_row['order_created_date_time'],
        last_updated_datetime = df_row['last_updated_datetime']
    ) 

def cast_order_df(df):
    df = df.fillna(0)
    df['order_id'] = df['order_id'].astype('int64')
    df['order_status_id'] = df['order_status_id'].astype('int64')
    df['customer_id'] = df['customer_id'].astype('int64')
    df['total_items'] = df['total_items'].astype('int64')
    df['total_items_shipped'] = df['total_items_shipped'].astype('int64')
    df['coupon_discount'] = df['coupon_discount'].astype('float')
    df['gift_certificate_amount_redeemed'] = df['gift_certificate_amount_redeemed'].astype('float')
    df['store_credit_redeemed'] = df['store_credit_redeemed'].astype('float')
    df['sub_total_excluding_tax'] = df['sub_total_excluding_tax'].astype('float')
    df['sub_total_including_tax'] = df['sub_total_including_tax'].astype('float')
    df['sub_total_tax'] = df['sub_total_tax'].astype('float')
    df['base_shipping_cost'] = df['base_shipping_cost'].astype('float')
    df['shipping_excluding_tax'] = df['shipping_excluding_tax'].astype('float')
    df['shipping_including_tax'] = df['shipping_including_tax'].astype('float')
    df['shipping_tax'] = df['shipping_tax'].astype('float')
    df['base_handling_cost'] = df['base_handling_cost'].astype('float')
    df['handling_excluding_tax'] = df['handling_excluding_tax'].astype('float')
    df['handling_including_tax'] = df['handling_including_tax'].astype('float')
    df['handling_tax'] = df['handling_tax'].astype('float')
    df['base_wrapping_cost'] = df['base_wrapping_cost'].astype('float')
    df['wrapping_excluding_tax'] = df['wrapping_excluding_tax'].astype('float')
    df['wrapping_including_tax'] = df['wrapping_including_tax'].astype('float')
    df['wrapping_tax'] = df['wrapping_tax'].astype('float')
    df['total_tax'] = df['total_tax'].astype('float')
    df['total_excluding_tax'] = df['total_excluding_tax'].astype('float')
    df['total_including_tax'] = df['total_including_tax'].astype('float')
    df['currency_exchange_rate'] = df['currency_exchange_rate'].astype('float')
    df['refund_amount'] = df['refund_amount'].astype('float')
    df['order_created_date_time'] = pd.to_datetime(df['order_created_date_time'])
    df['last_updated_datetime'] = pd.to_datetime(df['last_updated_datetime'])
    df['date_shipped'] = pd.to_datetime(df['date_shipped'])
    return df

def update_orders_table(store_id, csv_file_path):
    session = db.get_session(store_id)
    try:
        with pd.read_csv(csv_file_path, chunksize=10000, header=0) as reader:
            with session.begin():
                page = 1
                for df in reader:
                    page = page + 1
                    df = cast_order_df(df)
                    for index, row in df.iterrows():
                        order = get_db_order(row)
                        session.add(order)
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        session.close()

def _build_billing_address_model(address):
    return orders_db.OrderBillingAddress(
        order_id=int(address['order_id']),
        first_name = address['first_name'],
        last_name = address['last_name'],
        company = address['company'],
        street_1 = address['street_1'],
        street_2 = address['street_2'],
        city = address['city'],
        state = address['state'],
        zip = address['zip'],
        country = address['country'],
        country_code = address['country_code'],
        phone = address['phone'],
        email = address['email'],
        last_updated_datetime = address['last_updated_datetime']
    )

def _build_shipping_address_model(address):
    return orders_db.OrderShippingAddress(
        order_id=int(address['order_id']),
        shipping_address_id=int(address['shipping_address_id']),
        first_name = address['first_name'],
        last_name = address['last_name'],
        company = address['company'],
        street_1 = address['street_1'],
        street_2 = address['street_2'],
        city = address['city'],
        state = address['state'],
        postal_code = address['postal_code'],
        country = address['country'],
        country_code = address['country_code'],
        phone = address['phone'],
        email = address['email'],
        last_updated_datetime = address['last_updated_datetime']
    )

def _build_line_item_discount_model(line_item_discount):
    return orders_db.OrderLineItemDiscount(
        order_id=int(line_item_discount['order_id']),
        order_line_item_id = int(line_item_discount['order_line_item_id']),
        discount_id = line_item_discount['discount_id'],
        discount_amount = line_item_discount['discount_amount'],
        discount_name = line_item_discount['discount_name'],
        discount_code = line_item_discount['discount_code'],
        discount_level = line_item_discount['discount_level'],
        last_updated_datetime = line_item_discount['last_updated_datetime']
    )

def _build_line_item_model(line_item):
    return orders_db.OrderLineItems(
        order_id=int(line_item['order_id']),
        order_line_item_id = int(line_item['order_line_item_id']),
        parent_order_product_id = int(line_item['parent_order_product_id']),
        order_address_id = int(line_item['order_address_id']),
        product_id = int(line_item['product_id']),
        variant_id = int(line_item['variant_id']),
        option_set_id = int(line_item['option_set_id']),
        external_id = line_item['external_id'],
        quantity = int(line_item['quantity']),
        quantity_shipped = int(line_item['quantity_shipped']),                        
        product_price = float(line_item['product_price']),
        base_cost_price = float(line_item['base_cost_price']),
        product_price_after_discount = float(line_item['product_price_after_discount']),
        price_ex_tax = float(line_item['price_ex_tax']),
        price_inc_tax = float(line_item['price_inc_tax']),
        price_tax = float(line_item['price_tax']),
        base_total = float(line_item['base_total']),
        product_total_cost = float(line_item['product_total_cost']),
        total_ex_tax = float(line_item['total_ex_tax']),
        total_inc_tax = float(line_item['total_inc_tax']),
        total_tax = float(line_item['total_tax']),
        is_refunded = line_item['is_refunded'],
        refund_amount = float(line_item['refund_amount']),
        return_id = int(line_item['return_id']),
        fixed_shipping_cost = float(line_item['fixed_shipping_cost']),
        base_wrapping_cost = float(line_item['base_wrapping_cost']),
        wrapping_cost_ex_tax = float(line_item['wrapping_cost_ex_tax']),
        wrapping_cost_inc_tax = float(line_item['wrapping_cost_inc_tax']),
        wrapping_cost_tax = float(line_item['wrapping_cost_tax']),
        is_bundled_product = line_item['is_bundled_product'],
        last_updated_datetime = line_item['last_updated_datetime']
    )

def add_express_order(store_id, order_id, customer_id, cart_id):
    session = db.get_session(store_id)
    try:
        model = orders_db.ExpressOrders(
                order_id=int(order_id),
                customer_id = int(customer_id),
                cart_id = cart_id
            )
        session.merge(model)
        session.commit()
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        if session:
            session.close()

def _NY_restricted_zip_codes_persist_to_db(store_id, dto_list, dto_to_model_convertor):
    session = db.get_session(store_id)
    try:
        # clean up the table before inserting new data
        orders_db.NYRestrictedZipcodes.clear_table(store_id, session)
        for dto in dto_list:
            model = dto_to_model_convertor(dto)
            session.add(model)
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        session.commit()
        session.close()


def build_NY_restricted_zip_codes_model(line_item):
    return orders_db.NYRestrictedZipcodes(
        zip_code = int(line_item['Zip_Code']),
        zip_id = int(line_item['ID']),
        city_name = line_item['City_Name1']
    )

def fetch_NY_city_restricted_zip_codes(store_id):
    url = "https://accounts.zoho.com/oauth/v2/token"
    access_token = None
    zip_code_data = []
    # Define the payload (data for the POST request)
    payload = {
        "client_id": "1000.RFCUV67I1FCVVET4XUKVR5D7T7HRUX",
        "client_secret": "d4e263d084d5a2d74d35290be2df0543959db7cee0",
        "grant_type": "refresh_token",
        "refresh_token": "**********************************************************************"
    }

    # Send the POST request
    response = requests.post(url, data=payload)

    # Check if the request was successful (status code 200)
    if response.status_code == 200:
        access_token = response.json().get("access_token", None)
    
    if access_token:
        get_url = "https://creatorapp.zoho.com/api/v2/umairyasin/courier-hub/report/Flavor_Ban_Zipcode_Report"
        header = {
            "Authorization": "Zoho-oauthtoken " + access_token,
            "type": "text"
        }

        response = requests.get(get_url, headers=header)

        if response.status_code == 200:
            zip_code_data = response.json()['data']
    
    if len(zip_code_data) > 0:
        process_NY_restricted_zipcodes_data(store_id, zip_code_data)


def process_NY_restricted_zipcodes_data(store_id, zip_codes):
    if len(zip_codes) > 0:
        _NY_restricted_zip_codes_persist_to_db(store_id, zip_codes, build_NY_restricted_zip_codes_model) 


def get_missing_order_line_items(store_id):
    missing_orders = []
    session = db.get_session(store_id)
    try:
        with session.begin():
            sql = """
                SELECT o.order_id, o.last_updated_datetime
                FROM public.orders o
                LEFT JOIN public.order_line_items li ON o.order_id = li.order_id
                WHERE li.order_id IS NULL
                and o.order_created_date_time > current_date - interval '40' month
                and o.order_status_id in (10)
                order by o.order_created_date_time desc
            """
            _missing_orders = session.execute(text(sql)).fetchall()
            for order in _missing_orders:
                missing_orders.append({
                    "order_id": order[0],
                    "last_updated_datetime": order[1]})
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        session.close()
    return missing_orders

def fetch_missing_order_line_items_from_bc(store_id):
    store = store_util.get_store_by_id(store_id)
    missing_orders = get_missing_order_line_items(store_id)
    if len(missing_orders) > 0:
        line_items = []
        line_item_discounts = []
        for order in missing_orders:
            _line_items, _discounts = fetch_order_line_items(store, order["order_id"], order["last_updated_datetime"])
            line_items.extend(_line_items)
            line_item_discounts.extend(_discounts)
            if len(line_items) > 0:
                save_data(store['id'], line_items, build_line_item_model)
                line_items = []
            if len(line_item_discounts) > 0:
                save_data(store['id'], line_item_discounts, build_line_item_discount_model)
                line_item_discounts = []
        if len(line_items) > 0:
            save_data(store['id'], line_items, build_line_item_model)
            line_items = []
        if len(line_item_discounts) > 0:
            save_data(store['id'], line_item_discounts, build_line_item_discount_model)
            line_item_discounts = []
