import datetime
from sqlalchemy import Column, DateTime, String, Integer, Boolean
import pg_db

class Notification(pg_db.Base):
    __tablename__ = pg_db.notifications_table

    notification_id = Column(Integer, primary_key=True)
    notification_type = Column(String)
    notification_type_id = Column(Integer)
    customer_id = Column(Integer)
    notification_date_created = Column(DateTime)
    is_read = Column(Boolean)

    