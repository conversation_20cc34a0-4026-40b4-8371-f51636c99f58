from mongo_db import product_db
from utils import store_util, bc_util
from bson import ObjectId
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

int_field_list = ['width', 'depth', 'height', 'cost_price', 'retail_price', 'sale_price', 'sort_order', 'fixed_cost_shipping_price', 'order_quantity_minimum', 'order_quantity_maximum','availability_description','availability']


def process_images(images):
    result = []

    if len(images['edges']) == 0:
      return result
    else:
      for i in images['edges']:
        obj = {}
        obj['url'] = i['node']['url']
        obj['url_original'] = i['node']['urlOriginal']
        obj['is_default'] = i['node']['isDefault']
        obj['alt_text'] = i['node']['altText']
        result.append(obj)

    return result

def process_custom_fields(fields):
    result = []

    if len(fields['edges']) == 0:
      return result
    else:
      for i in fields['edges']:
        obj = {}
        obj['name'] = i['node']['name']
        obj['value'] = i['node']['value']

        result.append(obj)

    return result

def process_reviews(reviews):
    result = []

    if len(reviews['edges']) == 0:
      return result
    else:
      for i in reviews['edges']:
        obj = {}
        obj['id'] = i['node']['entityId']
        obj['rating'] = i['node']['rating']
        obj['text'] = i['node']['text']
        obj['title'] = i['node']['title']
        obj['date_created'] = i['node']['createdAt']['utc']
        obj['author'] = i['node']['author']['name']

        result.append(obj)

    return result

def process_related_products(related_products):
    result = []

    if len(related_products['edges']) == 0:
      return result
    else:
      for i in related_products['edges']:
        obj = {}
        obj['id'] = i['node']['entityId']
        obj['name'] = i['node']['name']
        obj['custom_url'] = {
        "url": i['node']['path']
      } 
        obj['brand_name'] = i['node']['brand']['name'] if i['node']['brand'] else ''
        obj['image'] = i['node']['defaultImage']['url'] if i['node']['defaultImage'] else ''
        obj['price'] = i['node']['prices']['price']['value'] or 0
        obj['retail_price'] = i['node']['prices']['retailPrice']['value'] if i['node']['prices']['retailPrice'] else 0
        obj['sale_price'] = i['node']['prices']['salePrice']['value'] if i['node']['prices']['salePrice'] else 0
        result.append(obj)

    return result

def process_single_product(product):
    result = {
      "data": {}
    }
    item = product['data']['site']['product']

    if item == None:
      return result
    else:
      result['data']['id'] = item['entityId']
      result['data']['sku'] = item['sku'] or ''
      result['data']['name'] = item['name']
      result['data']['price'] = item['prices']['price']['value'] or 0
      result['data']['retail_price'] = item['prices']['retailPrice']['value'] if item['prices']['retailPrice'] else 0
      result['data']['sale_price'] = item['prices']['salePrice']['value'] if item['prices']['salePrice'] else 0
      result['data']['images'] = process_images(item['images'])
      result['data']['custom_fields'] = process_custom_fields(item['customFields'])
      result['data']['reviews'] = process_reviews(item['reviews'])
      result['data']['related_products'] = process_related_products(item['relatedProducts'])
      result['data']['upc'] = item['upc'] or ''
      result['data']['condition'] = item['condition'] 
      result['data']['brand_name'] = item['brand']['name'] if item['brand'] else ''
      result['data']['inventory_is_in_stcok'] = item['inventory']['isInStock']
      result['data']['order_quantity_minimum'] = item['minPurchaseQuantity']
      result['data']['order_quantity_maximum'] = item['maxPurchaseQuantity'] 
      result['data']['reviews_count'] = item['reviewSummary']['numberOfReviews']
      result['data']['reviews_rating_sum'] = item['reviewSummary']['summationOfRatings']
      result['data']['weight'] = {
        "unit": item['weight']['unit'],
        "value": item['weight']['value']
      }
      result['data']['availability'] = item['availabilityV2']
      result['data']['description'] = item['description'] or ''
      result['data']['warranty'] = item['warranty'] or ''
      result['data']['breadcrumb_url'] = item['categories']['edges'][0]['node']['path'] + item['path' ].replace("/","")

    return result

def process_individual_variant(variant, headings, row_titles):
  res = {}
  
  variant = variant['node']
  res['id'] = variant['entityId']
  res['sku'] = variant['sku']
  
  res['inventory'] = {
    "available_to_sell": variant['inventory']['aggregated']['availableToSell'],
    "warning_level": variant['inventory']['aggregated']['warningLevel'],
  }
  
  prices = variant['prices']
  res['price'] = {
    "value":  prices['price']['value'],
    "currency_code": prices['price']['currencyCode']
  }
  
  res['sale_price'] = {
    "value":  prices['salePrice']['value'],
    "currency_code": prices['salePrice']['currencyCode']
  } if prices['salePrice'] else {"value": 0, "currency_code": ""}

  res['retail_price'] = {
    "value":  prices['retailPrice']['value'],
    "currency_code": prices['retailPrice']['currencyCode']
  } if prices['retailPrice'] else {"value": 0, "currency_code": ""}
  
  res['options'] = []
  if len(variant['productOptions']['edges']) > 0:
    for i in variant['productOptions']['edges']:
      obj = {}
      opt = i['node']
      
      obj['id'] = opt['entityId']
      # obj['display_name'] = opt['displayName']
      # obj['is_required'] = opt['isRequired']
      
      # generating table headers...
      if 'Nicotine' in opt['displayName']:
        if len(opt['values']['edges']) > 0:
          for i in opt['values']['edges']:
            op = i['node']
            dic = {
              "id": op['entityId'],
              "label": op['label']
            }
            if dic not in headings:
              headings.append(dic)

      # generating row titles...
      if len(opt['values']['edges']) > 0:
        for i in opt['values']['edges']:
          op = i['node']
          dic = {
            "id": op['entityId'],
            "label": op['label']
          }
          if dic not in row_titles and dic not in headings:
            row_titles.append(dic)
      
      # varaints ...
      if len(opt['values']['edges']) > 0:
        values = []
        for i in opt['values']['edges']:
          option = {}
          op = i['node']

          option['id'] = op['entityId']
          option['label'] = op['label']
          # option['is_default'] = op['isDefault']

          values.append(option)
        obj['values'] = values
      res['options'].append(obj)

  return res

def process_product_variants(item):
  resulted_variants = []
  headings = []
  row_titles = []
  variants = item['variants']['edges']

  if len(variants) > 0:
    for i in variants:
      res = process_individual_variant(i, headings, row_titles)
      resulted_variants.append(res) 
  else:
    return resulted_variants
    
  return resulted_variants, headings, row_titles


def create_product_in_background(store_id, id):
    try:
        store = store_util.get_store_by_id(store_id)
        # Fetch product details
        product_details = product_db.fetch_product_details_by_id(store, id)
        logger.info(f"Fetched product details: product id: {id}")

        # Remove _id from product_details if it exists
        if '_id' in product_details:
            del product_details['_id']

        # Convert UPC and GTIN to strings if they exist, else set as empty
        product_details['upc'] = str(product_details['upc']) if product_details.get('upc') else ''
        product_details['gtin'] = str(product_details['gtin']) if product_details.get('gtin') else ''
        logger.info("Processed UPC and GTIN values")

        # Handle categories and images (if they exist) by processing them
        product_details = _process_categories_and_images(store, product_details)
        logger.info(f"Processed categories and images")

        # Build product DTO for BigCommerce
        product_details = _build_product_dto_for_bc(product_details)
        logger.info("Built product DTO for BigCommerce")

        # Make the API call to create the product on BigCommerce
        res, status = create_bc_product(store, product_details)

        # Update MongoDB based on the API response
        payload = {}
        if status != 200:
            product_creation = get_product(store, product_details['sku'])
            if product_creation:
              payload['error'] = ''
              payload['status'] = 'published'
              update_data = {"$set": payload}
              product_db.update_product_document(store, {"_id": ObjectId(id)}, update_data)
              logger.info("Product successfully created and updated in MongoDB")
            else:
              if status == 207:
                payload['error'] = res.get('errors', {}).get('title', 'Unknown error')
              else:
                payload['error'] = res.get('title', 'Unknown error')
              payload['status'] = 'error'
              update_data = {"$set": payload}
              product_db.update_product_document(store, {"_id": ObjectId(id)}, update_data)
              logger.error(f"Failed to create product in BigCommerce: {res.get('details')}")
              return {'data': res}, status
        else:
            payload['error'] = ''
            payload['status'] = 'published'
            update_data = {"$set": payload}
            product_db.update_product_document(store, {"_id": ObjectId(id)}, update_data)
            logger.info("Product successfully created and updated in MongoDB")

    except Exception as e:
        logger.exception(f"An error occurred while creating product {id} for store {store}: {str(e)}")
        return {"status": "error", "message": "An error occurred while creating product.", "error": str(e)}


def get_product(store, sku):
    bc_api = store_util.get_bc_api_creds(store)
    api = f"v3/catalog/products"
    query_params = {                
        "sku": sku
    }
    res = bc_util.call_api(api_data=bc_api, method="GET", url=api, query_params=query_params)
    if res.status_code == 200 and res.json()['data'] != []:
       return True
    else:
       return False


def create_bc_product(store, req_body):
    url = f'v3/catalog/products'
    bc_api = store_util.get_bc_api_creds(store)
    res = bc_util.call_api(bc_api, "POST", url, query_params={}, req_body=req_body)
    
    # Check if the response has content
    if res.status_code == 200:
        try:
            return res.json(), 200
        except ValueError:
            # Log the raw content for debugging purposes
            logger.error(f"Failed to parse JSON response: {res.text}")
            return {"error": "Invalid JSON response from server"}, 200
    else:
        try:
            return res.json(), res.status_code
        except ValueError:
            logger.error(f"Failed to parse JSON response for error: {res.text}")
            return {"error": "Invalid JSON response from server"}, res.status_code

    
def _process_categories_and_images(store, product_data):
    cdn_baseurl = store_util.get_cdn_base_url(store)
    
    # Process images
    if 'images' in product_data:
        images = product_data['images']
        if len(images) > 0:
            for image in images:
                if 'image_url' in image:
                    image['image_url'] = cdn_baseurl.replace('/storefront/api/static/images', '/admin/api/my-products/image') + image['image_url']

    # Process categories
    if 'categories' in product_data:
        categories = product_data['categories']
        category_ids = []
        if len(categories) > 0:
            for category in categories:
                # Handle case where category is a string or a dictionary
                if isinstance(category, dict) and 'id' in category:
                    category_ids.append(category['id'])
                elif isinstance(category, str):
                    category_ids.append(category)
        product_data['categories'] = category_ids

    return product_data

def _build_product_dto_for_bc(data):
    if isinstance(data, dict):
        keys_to_remove = []
        for key, value in data.items():
            # Check if the key is in the int_field_list and the value is None
            if key in int_field_list and value is None:
                keys_to_remove.append(key)
            elif isinstance(value, (dict, list)):
                _build_product_dto_for_bc(value)
        
        # Remove the identified keys after iteration
        for key in keys_to_remove:
            del data[key]

    elif isinstance(data, list):
        items_to_remove = []
        for i in range(len(data)):
            if isinstance(data[i], dict):
                # Check for keys in int_field_list with None value inside the dict
                keys_to_remove = []
                for key, value in data[i].items():
                    if key in int_field_list and value is None:
                        keys_to_remove.append(key)
                # Remove identified keys in the current dict
                for key in keys_to_remove:
                    del data[i][key]

                # Recursively handle nested structures
                _build_product_dto_for_bc(data[i])
            elif data[i] is None or data[i] == 'null':
                items_to_remove.append(i)

        # Remove items from the list in reverse order to avoid index issues
        for i in reversed(items_to_remove):
            data.pop(i)
    
    return data

def update_bc_product_variants(store, req_body):
  url = f'v3/catalog/variants'
  bc_api = store_util.get_bc_api_creds(store)
  res = bc_util.call_api(bc_api, "PUT", url, req_body=req_body) 
  # success ...   
  if res.status_code == 200:
      return res.json(), 200
  else:
      # unprocess able entity...
      return res.json(), res.status_code