import logging
import datetime
import mongo_db

logger = logging.getLogger()

TASK_LOG_COLLECTION = "task_log"
MASTER_TASK_COLLECTION = "task_master"

def create_new_task(store, task, is_active=True):
    task_status = {
                "status": "pending",  ## pending, scheduled, running
                "current_run_start_time": None, 
                "last_run_start_time": None,
                "last_run_end_time": None,
                "last_run_outcome": None,
                "last_run_log": None
    }
    task_id = task.get("name")
    task_label = task.get("label", task_id)
    description = task.get("description", "")
    is_store_task = task.get("is_store_task", True)
    run_after_registration = task.get("run_after_registration", True)
    interval_seconds = task.get("interval_seconds", -1)
    run_at = task.get("run_at", None)
    is_periodic_task = False
    if interval_seconds > 0 or run_at:
        is_periodic_task = True

    task = {
        "_id": task_id,
        "name": task_label,
        "description": description,
        "is_periodic_task": is_periodic_task,
        "interval_seconds": interval_seconds,
        "run_at": run_at,
        "is_active": is_active,
        "is_store_task": is_store_task,
        "run_after_registration": run_after_registration,
        "store_run_status": task_status
    }
    
    db = mongo_db.get_admin_db_client(store)
    exiting_task = db[MASTER_TASK_COLLECTION].find_one({"_id": task_id})
    if exiting_task:
        return exiting_task
    db[MASTER_TASK_COLLECTION].replace_one({"_id": task_id},task,upsert=True)
    return task

def create_task_log(store_id, task_id, task_name, parent_id, root_id):
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    current_time = datetime.datetime.now(datetime.timezone.utc)
    task = {
        "_id": task_id,
        "taskName": task_name,
        "parentId": parent_id,
        "rootId": root_id,
        "storeId": store_id,
        "status": "running",
        "outcome": -1,
        "log": {},
        "endAt": -1,
        "createdAtStr": str(current_time),
        "createdAt": int(current_time.timestamp()),
        "modifiedAt": int(current_time.timestamp()),
    }
    db[TASK_LOG_COLLECTION].replace_one({"_id": task_id},task,upsert=True)

    exiting_task = db[MASTER_TASK_COLLECTION].find_one({"_id": task_name})
    if exiting_task:
        if "store_run_status" in exiting_task:
            update_key = "store_run_status"
            if store_id:
                run_status = exiting_task["store_run_status"].get(store_id, {})
                update_key = "store_run_status." + store_id
            else:
                run_status = exiting_task["store_run_status"]
            run_status["status"] = "running"
            run_status["current_task_id"] = task_id
            run_status["current_run_start_time"] = int(current_time.timestamp())
            run_status["current_run_start_time_str"] = str(current_time)

            update = {
                "$set" : {
                    update_key: run_status
                }
            }
            db[MASTER_TASK_COLLECTION].update_one({"_id": task_name}, update)

def end_task(store_id, task_id, task_name, executionTime, outcome, log):
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    current_time = datetime.datetime.now(datetime.timezone.utc)
    task = {
        "$set" : {
            "status": "completed",
            "outcome": int(outcome),
            "log": log,
            "executionTime": executionTime,
            "endAt": int(current_time.timestamp()),
            "endAtStr": str(current_time),
            "modifiedAt": str(current_time)
        }   
    }
    db[TASK_LOG_COLLECTION].update_one({"_id": task_id}, task)

    exiting_task = db[MASTER_TASK_COLLECTION].find_one({"_id": task_name})
    if exiting_task:
        if "store_run_status" in exiting_task:
            update_key = "store_run_status"
            if store_id:
                run_status = exiting_task["store_run_status"].get(store_id, {})
                update_key = "store_run_status." + store_id
            else:
                run_status = exiting_task["store_run_status"]

            if exiting_task["is_periodic_task"]:
                run_status["status"] = "scheduled"
            else:
                run_status["status"] = "pending"
            run_status["last_run_start_time"] = run_status["current_run_start_time"]
            run_status["last_run_start_time_str"] = run_status["current_run_start_time_str"]
            run_status["last_run_end_time"] = int(current_time.timestamp())
            run_status["last_run_end_time_str"] = str(current_time)
            run_status["last_run_execution_time"] = executionTime
            run_status["last_run_outcome"] = int(outcome)
            run_status["last_run_log"] = log
            run_status["current_task_id"] = None
            run_status["current_run_start_time"] = None

            update = {
                "$set" : {
                    update_key: run_status
                }
            }
            db[MASTER_TASK_COLLECTION].update_one({"_id": task_name}, update)
    return exiting_task

def delete_older_task(store_id, time_minutes):
    timedelta = int((datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(minutes=time_minutes)).timestamp())
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    db[TASK_LOG_COLLECTION].delete_many({"createdAt":{"$lte":timedelta}})