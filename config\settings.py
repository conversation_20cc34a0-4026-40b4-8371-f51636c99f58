import os

class BaseConfig():
   HOST = '0.0.0.0'
   PORT = 8080
   API_PREFIX = ''
   TESTING = False
   DEBUG = False
   SECRET_KEY = '9d398b6ed032b813d45bf24f48ee3fb7'
   APP_ENV = 'development'
   PROFILE = "prod"
   TENANT_DB_NAME = 'tenant_db'
   #MONGODB_CONN_STRING = 'mongodb://localhost:27017/'
   MONGODB_CONN_STRING = 'mongodb://**************:27017/'
   CELERY_BROKER_REDIS_HOST = 'localhost'
   CELERY_BROKER_REDIS_PORT = '6379'
   CELERY_BROKER_REDIS_DB = '5'
   CELERY_APP_NAME = "ad_app"
   
class DevConfig(BaseConfig):
    FLASK_ENV = 'development'
    DEBUG = True

class ProductionConfig(BaseConfig):
    APP_ENV = os.environ.get("APP_ENV", "Production")
    FLASK_ENV = 'production'
    PROFILE = os.environ.get("PROFILE", "prod")
    CELERY_APP_NAME = os.environ.get("CELERY_APP_NAME", "ad_app")
    MONGODB_CONN_STRING = os.getenv('MONGO_CONN_STRING', 'mongodb://ad-mongodb:27017/')
    CELERY_BROKER_REDIS_HOST = os.getenv('CELERY_BROKER_REDIS_HOST', 'ad-redis')
    CELERY_BROKER_REDIS_PORT = os.getenv('CELERY_BROKER_REDIS_PORT', '6379')
    CELERY_BROKER_REDIS_DB = os.getenv('CELERY_BROKER_REDIS_DB', '5')
    SECRET_KEY = os.getenv('SECRET_KEY', '9d398b6ed032b813d45bf24f48ee3fb7')
    TENANT_DB_NAME = os.getenv('TENANT_DB_NAME', 'tenant_db')

class TestConfig(BaseConfig):
    FLASK_ENV = 'development'
    TESTING = True
    DEBUG = True
