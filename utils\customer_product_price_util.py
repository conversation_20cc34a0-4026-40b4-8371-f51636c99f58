import datetime
from pg_db import products_db
from utils import store_util
import gspread
from oauth2client.service_account import ServiceAccountCredentials
import logging
import traceback
import pg_db as db
from sqlalchemy.exc import IntegrityError
import pytz
from sqlalchemy import text

logger = logging.getLogger()


# production account for mapping sheet operation
gs_token_old = **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
#test account for price-list sheet
gs_token = {
                "type": "service_account",
                "project_id": "midwest-price-list",
                "private_key_id": "f67fe74a43d13f3a899e34ab04a67e92668b55e1",
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
                "client_email": "<EMAIL>",
                "client_id": "116076913945457396283",
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://oauth2.googleapis.com/token",
                "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/pricelist-test%40midwest-price-list.iam.gserviceaccount.com"
            }


# sheetId = '1xNeP2pir_ypJmX7eieb--aH-KUzwMOHi6apvUB0f6RU'
sheetId = '1elPUFqxa-EyEk3L89TYn2ZFnNT9S_nCf_LtPQF_ZHN8'

def get_gsclient(store_id):
    store = store_util.get_store_by_id(store_id)
    if not store:
        return None
    SERVICE_ACCOUNT_INFO = store['apps']['google']['service_account']
    ALL_SHEETS = store['apps']['google']['google_sheet']
    
    SHEET =  ALL_SHEETS['customer-price-mapping']
    SCOPES = SHEET['scopes']
    SPREADSHEET_ID = SHEET['sheet_id']
    RANGE_NAME = SHEET['default_range']
    SHEET_NAME = SHEET['sheet_name']

    creds = ServiceAccountCredentials.from_json_keyfile_dict(SERVICE_ACCOUNT_INFO, SCOPES)
    client =  gspread.authorize(creds)
    sheet = client.open_by_key(SPREADSHEET_ID)
    return sheet, SHEET_NAME


def fetch_customer_product_price_mapping(store_id):
    store = store_util.get_store_by_id(store_id)
    if not store:
        return

    sheet, sheet_name = get_gsclient(store_id)
    if not sheet:
        return
    mapping_sheet = sheet.worksheet(sheet_name)
    data = mapping_sheet.get_all_records()
    mapping_data = []
    created_date = datetime.datetime.now(datetime.timezone.utc)
    
    for row in data:
        if 'Customer ID' in row and row['Customer ID'] and 'Product ID' in row and row['Product ID']:
            try:
                customer_id = str(row['Customer ID'])
                product_id = str(row['Product ID'])
                if 'Price' in row:
                    price = float(row.get('Price', 0) if row['Price'] else 0)
                    if price > 0:
                        mapping_data.append({
                            "customer_id": customer_id,
                            "product_id": product_id,
                            "price": price,
                            "created_by": "Price Sheet",
                            "created_at": created_date
                        })
            except Exception as ex:
                logger.error(str(traceback.format_exc()))
    
    if len(mapping_data) > 0:
        save_data(store_id, mapping_data, build_customer_price_mapping_model)

def update_customer_product_price(store_id, payload):
    try:
        product_id = payload.get("product_id", 0)
        customer_id = payload.get("customer_id", 0)
        new_price = payload.get("price", 0)
        updated_by = payload.get("updated_by", "")
        cst = pytz.timezone('America/Chicago') 
        updated_at = datetime.datetime.now(cst).strftime("%Y-%m-%d %H:%M:%S")

        # Authenticate and open Google Sheet
        sheet, sheet_name = get_gsclient(store_id)  # This should return a sheet
        if not sheet:
            return
        mapping_sheet = sheet.worksheet(sheet_name)  # Select the 'Mapping' sheet

        # Fetch all rows to get headers and identify column positions
        data = mapping_sheet.get_all_values()
        headers = data[0]  # First row contains headers

        # Identify column indexes (1-based indexing for Google Sheets)
        customer_col = headers.index("Customer ID") + 1
        product_col = headers.index("Product ID") + 1
        price_col = headers.index("Price") + 1
        updated_by_col = headers.index("Created By") + 1
        updated_at_col = headers.index("Created At") + 1

        # Find matching rows
        matching_rows = []
        for row_idx, row in enumerate(data[1:], start=2):  # Start from row 2 (skip headers)
            if len(row) >= max(customer_col, product_col) and \
               str(row[customer_col - 1]).strip() == str(customer_id) and \
               str(row[product_col - 1]).strip() == str(product_id):
                matching_rows.append(row_idx)

        if not matching_rows:
            return {"status": 404, "message": f"No matching entries found for Customer ID {customer_id}, Product ID {product_id}."}

        # Prepare batch update data
        updates = []
        for row in matching_rows:
            updates.append({'range': gspread.utils.rowcol_to_a1(row, price_col), 'values': [[new_price]]})
            updates.append({'range': gspread.utils.rowcol_to_a1(row, updated_by_col), 'values': [[updated_by]]})
            updates.append({'range': gspread.utils.rowcol_to_a1(row, updated_at_col), 'values': [[updated_at]]})

        # Perform batch update
        mapping_sheet.batch_update(updates)

        return {"status": 200, "message": f"Updated price for Customer ID {customer_id}, Product ID {product_id} in {len(matching_rows)} row(s)."}

    except gspread.exceptions.APIError as api_err:
        return {"status": 500, "message": f"Google Sheets API Error: {api_err}"}

    except Exception as e:
        return {"status": 500, "message": f"Error: {str(e)}"}

def insert_customer_product_price(store_id, payload):
    conn = db.get_connection(store_id)
    try:
        store = store_util.get_store_by_id(store_id)
        if not store:
            return
        product_id = payload.get("product_id", 0)
        customer_id = payload.get("customer_id", 0)
        price = payload.get("price", 0)
        created_by = payload.get("created_by", "")

        query_check_product = text("""SELECT product_name FROM products WHERE product_id = :product_id""")
        product = conn.execute(query_check_product, {"product_id": product_id}).fetchone()
        if not product:
            return {"status": 404, "message": f"Product with ID {product_id} not found."}
        product_name = product[0]
        
        query_check_customer = text("""SELECT customer_id, first_name, last_name, company, email FROM customers WHERE customer_id = :customer_id  """)
        customer = conn.execute(query_check_customer, {"customer_id": customer_id}).fetchone()
        if not customer:
            return {"status": 404, "message": f"Customer with ID {customer_id} not found."}
        
        first_name = customer[1]
        last_name = customer[2]
        company = customer[3]
        email = customer[4]
        cst = pytz.timezone('America/Chicago') 
        current_date = datetime.datetime.now(cst).strftime("%Y-%m-%d %H:%M:%S")

        
        # Authenticate and open Google Sheet
        sheet, sheet_name = get_gsclient(store_id) # This should return a sheet
        if not sheet:
            return
        mapping_sheet = sheet.worksheet(sheet_name)  # Select the 'Mapping' sheet

        # Fetch headers to ensure correct column order
        headers = mapping_sheet.row_values(1)
        expected_headers = ["Product ID", "Customer ID", "Price", "Product Name", "First Name", "Last Name", "Email", "Company", "Created By", "Created At"]

        # Validate if sheet has expected headers
        if headers[:len(expected_headers)] != expected_headers:
            return {"status": 400, "message": "Sheet format mismatch. Expected headers do not match."}

        # Ensure required fields are not empty
        if not all([product_id, customer_id, price, product_name, first_name, last_name, email, company, created_by]):
            return {"status": 400, "message": "Missing required fields. All parameters must have valid values."}

        # Convert values to proper format
        product_id = int(product_id)
        customer_id = int(customer_id)
        price = float(price)  # Ensure price is a valid float
        product_name = product_name.strip()
        first_name = first_name.strip()
        last_name = last_name.strip()
        email = email.strip()
        company = company.strip()

        # Prepare new row data
        new_row = [product_id, customer_id, price, product_name, first_name, last_name, email, company, created_by, current_date]

        # Find the last empty row
        last_row = len(mapping_sheet.get_all_values()) + 1  # Get number of filled rows and add 1

        # Insert the row at the first column of the last empty row
        mapping_sheet.update(f"A{last_row}", [new_row])

        return {"status": 200, "message": "New row inserted successfully."}

    except gspread.exceptions.APIError as api_err:
        return {"status": 500, "message": f"Google Sheets API Error: {api_err}"}

    except ValueError as val_err:
        return {"status": 400, "message": f"Invalid data type: {val_err}"}

    except Exception as e:
        return {"status": 500, "message": f"Error: {str(e)}"}
    finally:
        if conn:
            conn.close()


def delete_customer_product_price_rows(store_id, delete_list):
    try:
        # Authenticate and open Google Sheet
        sheet, sheet_name = get_gsclient(store_id)  # This should return a sheet and sheet name
        if not sheet:
            return {"status": 500, "message": "Failed to connect to Google Sheets."}

        mapping_sheet = sheet.worksheet(sheet_name)  # Select the 'Mapping' sheet

        # Fetch all rows to get headers and identify column positions
        data = mapping_sheet.get_all_values()
        headers = data[0]  # First row contains headers

        # Identify column indexes (1-based for Google Sheets)
        try:
            product_col = headers.index("Product ID") + 1
            customer_col = headers.index("Customer ID") + 1
        except ValueError:
            return {"status": 400, "message": "Sheet format mismatch. Expected headers do not match."}

        # Find matching rows to delete
        rows_to_delete = []
        for row_idx, row in enumerate(data[1:], start=2):  # Start from row 2 (skip headers)
            for entry in delete_list:
                if (
                    str(row[product_col - 1]).strip() == str(entry["product_id"]) and
                    str(row[customer_col - 1]).strip() == str(entry["customer_id"])
                ):
                    rows_to_delete.append(row_idx)

        if not rows_to_delete:
            return {"status": 404, "message": "No matching rows found for deletion."}

        # Sort rows in descending order before deletion (to prevent shifting issues)
        rows_to_delete.sort(reverse=True)

        # Delete rows
        for row in rows_to_delete:
            mapping_sheet.delete_rows(row)

        return {"status": 200, "message": f"Deleted {len(rows_to_delete)} row(s) successfully."}

    except gspread.exceptions.APIError as api_err:
        return {"status": 500, "message": f"Google Sheets API Error: {api_err}"}

    except KeyError:
        return {"status": 400, "message": "Invalid input format. Expected list of {'product_id': ..., 'customer_id': ...}."}

    except Exception as e:
        return {"status": 500, "message": f"Error: {str(e)}"}

def build_customer_price_mapping_model(mapping):
    return products_db.ProductCustomerPriceMapping(
        product_id = int(mapping['product_id']),
        customer_id = int(mapping['customer_id']),
        price = mapping['price'],
        created_at = mapping['created_at'],
        created_by = mapping['created_by'],
        updated_at = mapping['created_at'],
        updated_by = mapping['created_by']
    )

def save_data(store_id, dto_list, dto_to_model_convertor):
    session = db.get_session(store_id)
    try:
        products_db.ProductCustomerPriceMapping.clear_table(store_id, session)
        for dto in dto_list:
            model = dto_to_model_convertor(dto)
            try:
                session.merge(model)  # Merge handles existing records
            except IntegrityError:
                session.rollback()  # Rollback only the failing transaction
                logger.warning(f"Skipping duplicate entry for: {dto}")
    except Exception as ex:
        logger.error("Caught error in customer_product_price_util.save_data: store_id: " + str(store_id))
        logger.error(traceback.format_exc())
    finally:
        session.commit()
        session.close()