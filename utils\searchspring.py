import requests

# Python code to merge dict using update() method
def Merge(dict1, dict2):
   return {**dict1, **dict2}

def parseRequestFitlers(default_params, fitlers):
    params_str = ''
    counter = 0
    
    # all query params...
    params = Merge(default_params, fitlers)

    for key, value in params.items():
        symbol = "?" if counter == 0 else "&"

        # while values are in list...
        if(type(value) == list):
            for i in value:
                if type(i) == str:
                    i = i.strip()
                params_str = params_str + "{}{}={}".format(symbol, key, i)
                counter += 1

        # while values are in int or str ...
        if(type(value) == str or type(value) == int and type(value) != list):
            if type(value) == str:
                value = value.strip()
            params_str = params_str + "{}{}={}".format(symbol, key, value)
            counter += 1
    
    return params_str

def call_searchspring_api(method, url, store, query_params):    
    res = {}

    # common params for all searchspring requests...
    default_params = {
        "resultsFormat": "json",
        "siteId": 'm8l85c'
    }

    # put dynamic siteId from store...
    api_url = "https://" + 'm8l85c' + ".a.searchspring.io/api/" + url  + parseRequestFitlers(default_params, query_params)

    method = method.upper()
    
    if "GET" == method:
        res = requests.get(url=api_url)
    
    return res.json()
