import datetime
from mongo_db import store_db
from sqlalchemy import text
import logging
import csv
import io
import pytz
from utils import email_util
import pg_db
from zoneinfo import ZoneInfo

logger = logging.getLogger()


def _calculate_profit_and_contribution(data, total_profit_all_groups=None, total_revenue_all_groups=None, calculate_profit_percentage=True):
    """Calculate profit percentage and contributed profit."""

    # Calculate total profit if not passed
    if total_profit_all_groups is None:
        total_profit_all_groups = sum(group["PROFIT"] for group in data)
    
    if total_revenue_all_groups is None:
        total_revenue_all_groups = sum((group.get("REVENUE") or group.get("TOTAL SALES") or 0) for group in data)

    for group in data:
        total_revenue = float(group.get("REVENUE") or group.get("TOTAL SALES") or 0)
        total_profit = float(group["PROFIT"] or 0)

        # Calculate profit percentage (profit relative to revenue)
        if calculate_profit_percentage:
            profit_percent = round((total_profit / total_revenue) * 100, 2) if total_revenue else 0
            group["PROFIT(%)"] = f"{profit_percent}%"

        # Calculate contributed profit percentage (relative to total)
        contributed_profit_percent = round((float(total_profit) / float(total_profit_all_groups)) * 100, 2) if total_profit_all_groups else 0
        group["CONTRIBUTED PROFIT(%)"] = f"{contributed_profit_percent}%"

        contribution_to_sales_percent = round((float(total_revenue) / float(total_revenue_all_groups)) * 100, 2) if total_revenue_all_groups else 0
        group["CONTRIBUTION TO SALES(%)"] = f"{contribution_to_sales_percent}%"
        
        group["COST"] = formate_numbers(group["COST"])
        if group.get("TOTAL SALES"):
            group["TOTAL SALES"] = formate_numbers(group["TOTAL SALES"])
        if group.get("REVENUE"):
            group["REVENUE"] = formate_numbers(group["REVENUE"])
        group["PROFIT"] = formate_numbers(group["PROFIT"])

    return data

def formate_numbers(number):
    if number is None or number == 0:
        return "$0.00"

    abs_number = abs(number)
    formatted = f"${abs_number:,.2f}"

    return f"-{formatted}" if number < 0 else formatted
    
def get_customer_profitability_csv(store_id, query_params):
    conn = pg_db.get_connection(store_id)
    try:
        user_details = store_db.fetch_user_by_username(store_id, query_params['username'])
        user_name = user_details['name'] if 'name' in user_details and user_details else ''

        start_date = query_params.get('start_date', None)
        end_date = query_params.get('end_date', None)
        sort_by = query_params.get('sort_by', '').strip()
        sort_array = sort_by.split("/") if sort_by != '' else []
        search = query_params.get('search', None)
        customer_group_id = query_params.get('customer_group_id', '')
        sales_rep_emails = query_params.get('sales_rep_email', '')

        # Build WHERE clause
        where_clauses = []

        if start_date and end_date:
            where_clauses.append("ct.order_date BETWEEN :start_date AND :end_date")

        if search:
            search = search.strip().replace(" ", "")
            search_condition = (
                f"(c.first_name ILIKE '%{search}%' "
                f"OR c.last_name ILIKE '%{search}%' "
                f"OR REPLACE(CONCAT(c.first_name,' ',c.last_name), ' ', '') ILIKE '%{search}%' "
                f"OR c.email ILIKE '%{search}%')"
            )
            where_clauses.append(search_condition)
        
        customer_group_ids = []    
        if customer_group_id:
            customer_group_ids = list(map(int, customer_group_id.split(","))) if isinstance(customer_group_id, str) else []
            where_clauses.append("c.customer_group_id = ANY(:customer_group_ids)")
            
        sales_rep_emails_array = []
        if sales_rep_emails:
            sales_rep_emails_array = sales_rep_emails.split(",") if isinstance(sales_rep_emails, str) else []
            where_clauses.append("scr.rep_email = ANY(:sales_rep_emails)")

        where_sql = f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""

        base_query =f"""SELECT
                        ct.customer_id,
                        (SUM(ct.revenue) + COALESCE(sum(sc.shipping_cost), 0)) AS total_revenue,
                        (SUM(ct.profit) + COALESCE(sum(sc.shipping_cost), 0)) AS total_profit,
                        SUM(ct.total_cost) AS total_cost,
                        SUM(ct.orders) AS total_orders,
                        CONCAT(c.first_name, ' ', c.last_name) AS name,
                        c.email,
                        c.customer_group_id,
                        c.customer_group_name,
                        scr.rep_name,
                        scr.rep_email,
                        scr.payment_term
                    FROM
                        analytics.profitability_customers_trend ct
                    JOIN 
                        customers c ON c.customer_id = ct.customer_id
                    JOIN 
                        salesforce_customer_rep scr ON scr.customer_id = c.customer_id
                    LEFT JOIN  
                        analytics.profitability_customers_shipping_cost sc ON ct.customer_id = sc.customer_id and ct.order_date = sc.order_date
                    {where_sql}
                    GROUP BY
                        ct.customer_id,
                        c.first_name,
                        c.last_name,
                        c.email,
                        c.customer_group_id,
                        c.customer_group_name,
                        scr.rep_name,
                        scr.rep_email,
                        scr.payment_term
                   """
    
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            nulls_order = "NULLS FIRST" if sort_direction.lower() == "asc" else "NULLS LAST"        
            if sort_array[0] in ["total_revenue", "total_profit", "total_cost", "total_orders", "name", "email", "customer_group_name", "rep_name", "rep_email"]:                
                base_query += f" ORDER BY {sort_array[0]} {sort_direction} {nulls_order}"
    

        # Query Parameters
        params = {}

        if start_date and end_date:
            params.update({"start_date": start_date, "end_date": end_date})
        if customer_group_ids:
            params["customer_group_ids"] = customer_group_ids
        if sales_rep_emails_array:
            params["sales_rep_emails"] = sales_rep_emails_array

        result = conn.execute(text(base_query), params)
        data = []
    
        for row in result.fetchall():
            total_revenue = float(round(row[1] or 0, 2))
            total_profit = float(round(row[2] or 0, 2))
            total_cost = float(round(row[3] or 0, 2))
            total_orders = int(row[4] or 0)
            data.append({
                "CUSTOMER NAME": row[5],
                "CUSTOMER EMAIL": row[6],
                "CUSTOMER GROUP": row[8],
                "SALES REP NAME": row[9],
                "SALES REP EMAIL": row[10],
                "ORDER": total_orders,
                "COST": total_cost,
                "REVENUE": total_revenue,
                "PROFIT": total_profit
            })

        # Total Profit Query
        exclude_keywords = ["c.customer_group_id", "c.first_name"]
        total_where_clauses = [clause for clause in where_clauses if not any(keyword in clause for keyword in exclude_keywords)]
        total_where_sql = f"WHERE {' AND '.join(total_where_clauses)}" if total_where_clauses else ""
        total_profit_query = f"""
            SELECT (SUM(ct.profit) + COALESCE(sum(sc.shipping_cost), 0)) AS total_profit,
            (SUM(ct.revenue) + COALESCE(sum(sc.shipping_cost), 0)) AS total_revenue
            FROM
                analytics.profitability_customers_trend ct
            JOIN 
                customers c ON c.customer_id = ct.customer_id
            JOIN 
                salesforce_customer_rep scr ON scr.customer_id = c.customer_id
            LEFT JOIN  
                analytics.profitability_customers_shipping_cost sc ON ct.customer_id = sc.customer_id and ct.order_date = sc.order_date
            {total_where_sql}
        """
        result = conn.execute(text(total_profit_query), {k: v for k, v in params.items() if k not in ['customer_group_ids', 'search']})
        total_profit, total_revenue = result.fetchone()
        
        customers_profitability = _calculate_profit_and_contribution(data, total_profit, total_revenue)
    
        data = []
        cst = ZoneInfo("America/Chicago")         
        # Create CSV content in memory
        if customers_profitability:
            utc_now = datetime.datetime.now(pytz.utc)
            cst_now = utc_now.astimezone(pytz.timezone('America/Chicago'))
            created_date = cst_now.strftime("%m-%d-%Y_%H:%M")
            file_name = f'Customer_Profitability_Report_{created_date}.csv'
            csv_content = io.StringIO()
            keys = list(customers_profitability[0].keys())
            writer = csv.DictWriter(csv_content, fieldnames=keys)
            writer.writeheader()
            for row in customers_profitability:
                writer.writerow(row)

            email_util.send_customer_profitability_report_email(store_id, csv_content, query_params['username'], file_name, user_name, created_date)
    
    finally:
        conn.close()

def get_product_wise_profitability_csv(store_id, query_params):
    conn = pg_db.get_connection(store_id)
    try:
        user_details = store_db.fetch_user_by_username(store_id, query_params['username'])
        user_name = user_details['name'] if 'name' in user_details and user_details else ''

        start_date = query_params.get('start_date', None)
        end_date = query_params.get('end_date', None)
        search = query_params.get('search', None).strip()
        sort_by = query_params.get('sort_by', '').strip()
        sort_array = sort_by.split("/") if sort_by != '' else []
        classification_filter = query_params.get('classification_filter', None)
        products_filter = query_params.get('products_filter', None)
        supplier_filter = query_params.get('supplier_filter', None)

        # Build WHERE clause
        where_clauses = []
        CTE_where_clauses = []
        if start_date and end_date:
            where_clauses.append("apr.order_date BETWEEN :start_date AND :end_date")

        if search:
            search = search.strip()
            search_condition = (f" p.product_name ILIKE '%{search}%' OR apr.parent_sku ILIKE '%{search}%'")
            where_clauses.append(search_condition)
        
        classification_array = []
        if classification_filter:
            classification_array = classification_filter.split(',')
            # where_clauses.append("sv.classification = ANY(:classification)")
            CTE_where_clauses.append("classification IN :classification_filter_list")

        supplier_array = []
        if supplier_filter:
            supplier_array = supplier_filter.split(';')
            # where_clauses.append("sv.primary_supplier = ANY(:primary_supplier)")
            CTE_where_clauses.append("primary_supplier IN :primary_supplier_list")
        
        products_ids = []
        if products_filter:
            products_ids = list(map(int, products_filter.split(','))) if isinstance(products_filter, str) else []
            where_clauses.append("apr.product_id = ANY(:products_ids)")

        where_sql = f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""
        cte_where_sql = f"WHERE {' AND '.join(CTE_where_clauses)}" if CTE_where_clauses else ""

        query = f"""WITH sv_aggregated AS (
                        SELECT
                            parent_sku,
                            ARRAY_AGG(DISTINCT classification) AS classification,
                            ARRAY_AGG(DISTINCT primary_supplier) AS primary_supplier
                        FROM
                            {pg_db.skuvault_catalog}
                        {cte_where_sql}
                        GROUP BY
                            parent_sku
                    )
                SELECT
                    apr.parent_sku AS product_sku, apr.product_id, p.product_name,
                    SUM(apr.revenue) AS total_sales,
                    SUM(apr.total_cost) AS total_cost,
                    SUM(apr.profit) AS total_profit,
                    sv.classification,
                    sv.primary_supplier,
                    SUM(apr.orders) AS total_orders
                FROM
                    analytics.profitability_products_revenue apr
                LEFT JOIN
                    {pg_db.products_table} p ON apr.product_id = p.product_id
                INNER JOIN sv_aggregated sv ON sv.parent_sku = p.sku
                {where_sql}
                GROUP BY apr.parent_sku, apr.product_id, p.product_name, sv.classification, sv.primary_supplier"""

        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            nulls_order = "NULLS FIRST" if sort_direction.lower() == "asc" else "NULLS LAST"        
            if sort_array[0] in ["total_sales", "total_profit", "total_cost", "product_sku", "product_name", "classification", "primary_supplier", "total_orders"]:                
                query += f" ORDER BY {sort_array[0]} {sort_direction} {nulls_order}"
        else:
            query += f" ORDER BY p.total_profit {sort_direction} {nulls_order}"

        # Query Parameters
        params = {}
        
        if start_date and end_date:
            params.update({"start_date": start_date, "end_date": end_date})
        if classification_array:
            params["classification_filter_list"] = tuple(classification_array)
        if supplier_array:
            params["primary_supplier_list"] = tuple(supplier_array)
        if products_ids:
            params["products_ids"] = products_ids

        result = conn.execute(text(query), params).fetchall()
    
        data = []
        for row in result:
            data.append({
                "PRODUCT ID": row[1],
                "PRODUCT NAME": row[2],
                "SKU": row[0],
                "CLASSIFICATION": row[6],
                "SUPPLIER": row[7],
                "ORDERS": int(row[8]) if row[8] else 0,
                "COST": float(round(row[4] or 0, 2)),
                "TOTAL SALES": float(round(row[3] or 0, 2)),
                "PROFIT": float(round(row[5] or 0, 2))
            })

        # Total Profit Query
        exclude_keywords = ["classification", "primary_supplier", "p.product_name", "apr.product_id"]
        total_cte_where_clauses = [clause for clause in CTE_where_clauses if not any(keyword in clause for keyword in exclude_keywords)]
        total_cte_where_sql = f"WHERE {' AND '.join(total_cte_where_clauses)}" if total_cte_where_clauses else ""
        total_where_clauses = [clause for clause in where_clauses if not any(keyword in clause for keyword in exclude_keywords)]
        total_where_sql = f"WHERE {' AND '.join(total_where_clauses)}" if total_where_clauses else ""

        total_profit_query = f"""WITH sv_aggregated AS (
                                    SELECT
                                        parent_sku,
                                        ARRAY_AGG(DISTINCT classification) AS classifications,
                                        ARRAY_AGG(DISTINCT primary_supplier) AS primary_suppliers
                                    FROM
                                        {pg_db.skuvault_catalog}
                                    {total_cte_where_sql}
                                    GROUP BY
                                        parent_sku
                                )
                                SELECT
                                    SUM(apr.profit) AS total_profit,
                                    SUM(apr.revenue) AS total_revenue
                                FROM
                                    analytics.profitability_products_revenue apr
                                LEFT JOIN
                                    {pg_db.products_table} p ON p.product_id = apr.product_id
                                INNER JOIN sv_aggregated sv ON sv.parent_sku = p.sku
                                {total_where_sql}
                                """
        result = conn.execute(text(total_profit_query), {k: v for k, v in params.items() if k not in ['classification_filter_list', 'primary_supplier_list', 'products_ids', 'search']})
        total_profit, total_revenue = result.fetchone() 
        
        products = _calculate_profit_and_contribution(data, total_profit, total_revenue)
    
        data = []
        cst = ZoneInfo("America/Chicago")         
        # Create CSV content in memory
        if products:
            utc_now = datetime.datetime.now(pytz.utc)
            cst_now = utc_now.astimezone(pytz.timezone('America/Chicago'))
            created_date = cst_now.strftime("%m-%d-%Y_%H:%M")
            file_name = f'Product_Wise_Profitability_Report_{created_date}.csv'
            csv_content = io.StringIO()
            keys = list(products[0].keys())
            writer = csv.DictWriter(csv_content, fieldnames=keys)
            writer.writeheader()
            for row in products:
                writer.writerow(row)

            email_util.send_product_wise_profitability_report_email(store_id, csv_content, query_params['username'], file_name, user_name, created_date)
    
    finally:
        conn.close()

def get_suppliers_profitability_csv(store_id, query_params):
    conn = pg_db.get_connection(store_id)
    try:
        user_details = store_db.fetch_user_by_username(store_id, query_params['username'])
        user_name = user_details['name'] if 'name' in user_details and user_details else ''

        start_date = query_params.get('start_date', None)
        end_date = query_params.get('end_date', None)
        search = query_params.get('search', None).strip()
        sort_by = query_params.get('sort_by', '').strip()
        sort_array = sort_by.split("/") if sort_by != '' else []
        supplier_filter = query_params.get('supplier_filter', None)

        # Build WHERE clause
        where_clauses = []
        CTE_where_clauses = []
        CTE_where_clauses.append("primary_supplier IS NOT NULL AND primary_supplier != ''")
        
        if search:
            search = search.strip()
            search_condition = (f" sc.primary_supplier ILIKE '%{search}%'")
            where_clauses.append(search_condition)
        
        supplier_array = []
        if supplier_filter:
            supplier_array = supplier_filter.split(';')
            CTE_where_clauses.append("primary_supplier IN :primary_supplier_list")

        where_sql = f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""
        cte_where_sql = f"WHERE {' AND '.join(CTE_where_clauses)}" if CTE_where_clauses else ""

        query = f""" 
                SELECT 
                    sc.primary_supplier AS supplier_name,
                    SUM(ar.revenue) AS total_sales,
                    SUM(ar.total_cost) AS total_cost,
                    SUM(ar.profit) AS total_profit,
                    SUM(ar.orders) AS total_orders
                FROM 
                    (
                        SELECT DISTINCT parent_sku, primary_supplier
                        FROM {pg_db.skuvault_catalog}
                        {cte_where_sql}
                    ) sc
                LEFT JOIN 
                    analytics.profitability_products_revenue ar 
                    ON sc.parent_sku = ar.parent_sku
                    AND ar.order_date BETWEEN :start_date AND :end_date
                {where_sql}
                GROUP BY sc.primary_supplier
                """
        
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            nulls_order = "NULLS FIRST" if sort_direction.lower() == "asc" else "NULLS LAST"
            if sort_array[0] in ["total_profit", "total_sales", "total_cost", "supplier_name", "total_orders"]:
                query += f" ORDER BY {sort_array[0]} {sort_direction} {nulls_order}"
            else:
                query += f" ORDER BY sc.primary_supplier {sort_direction} {nulls_order}"

        # Query Parameters
        params = {}
        
        if start_date and end_date:
            params.update({"start_date": start_date, "end_date": end_date})
        if supplier_array:
            params["primary_supplier_list"] = tuple(supplier_array)

        result = conn.execute(text(query), params).fetchall()
        data = []
        for row in result:
            data.append({
                "SUPPLIER NAME": row[0],
                "ORDERS": int(row[4]) if row[4] else 0,
                "COST": float(round(row[2] or 0, 2)),
                "TOTAL SALES": float(round(row[1] or 0, 2)),
                "PROFIT": float(round(row[3] or 0, 2))
            })

        # Total Profit Query
        exclude_keywords = ["sc.primary_supplier", "primary_supplier"]
        total_cte_where_clauses = [clause for clause in CTE_where_clauses if not any(keyword in clause for keyword in exclude_keywords)]
        total_cte_where_sql = f"WHERE {' AND '.join(total_cte_where_clauses)}" if total_cte_where_clauses else ""
        total_where_clauses = [clause for clause in where_clauses if not any(keyword in clause for keyword in exclude_keywords)]
        total_where_sql = f"WHERE {' AND '.join(total_where_clauses)}" if total_where_clauses else ""
        total_profit_query = f"""
                                SELECT SUM(ar.profit), SUM(ar.revenue) FROM (
                                    SELECT DISTINCT parent_sku, primary_supplier FROM skuvault_catalog
                                    {total_cte_where_sql}
                                ) sc
                                JOIN analytics.profitability_products_revenue ar ON sc.parent_sku = ar.parent_sku
                                AND ar.order_date BETWEEN :start_date AND :end_date
                                {total_where_sql}
                            """
        result = conn.execute(text(total_profit_query), {k: v for k, v in params.items() if k not in ['primary_supplier_list', 'search']})
        total_profit, total_revenue = result.fetchone() 
        
        suppliers = _calculate_profit_and_contribution(data, total_profit, total_revenue)
    
        data = []
        cst = ZoneInfo("America/Chicago")         
        # Create CSV content in memory
        if suppliers:
            utc_now = datetime.datetime.now(pytz.utc)
            cst_now = utc_now.astimezone(pytz.timezone('America/Chicago'))
            created_date = cst_now.strftime("%m-%d-%Y_%H:%M")
            file_name = f'Suppliers_Profitability_Report_{created_date}.csv'
            csv_content = io.StringIO()
            keys = list(suppliers[0].keys())
            writer = csv.DictWriter(csv_content, fieldnames=keys)
            writer.writeheader()
            for row in suppliers:
                writer.writerow(row)

            email_util.send_suppliers_profitability_report_email(store_id, csv_content, query_params['username'], file_name, user_name, created_date)
    
    finally:
        conn.close()

def get_classification_profitability_csv(store_id, query_params):
    conn = pg_db.get_connection(store_id)
    try:
        user_details = store_db.fetch_user_by_username(store_id, query_params['username'])
        user_name = user_details['name'] if 'name' in user_details and user_details else ''

        start_date = query_params.get('start_date', None)
        end_date = query_params.get('end_date', None)
        search = query_params.get('search', None).strip()
        sort_by = query_params.get('sort_by', '').strip()
        sort_array = sort_by.split("/") if sort_by != '' else []
        classification_filter = query_params.get('classification_filter', None)

        classification_array = []
        classification_condition = ""
        if classification_filter:
            classification_array = classification_filter.split(',')
            classification_condition = " AND classification = ANY(:classification_filter_list)"
        
        query = f"""
                WITH unique_classified_skus AS (
                    SELECT DISTINCT parent_sku, classification
                    FROM {pg_db.skuvault_catalog}
                    WHERE classification IS NOT NULL AND classification != '' {classification_condition}
                ),
                aggregated_revenue AS (
                    SELECT parent_sku, 
                        SUM(revenue) AS total_sales,
                        SUM(total_cost) AS total_cost,
                        SUM(profit) AS total_profit,
                        SUM(orders) AS total_orders
                    FROM analytics.profitability_products_revenue
                    WHERE order_date BETWEEN :start_date AND :end_date
                    GROUP BY parent_sku
                )
                SELECT
                    ucs.classification,
                    SUM(ar.total_sales) AS total_sales,
                    SUM(ar.total_cost) AS total_cost,
                    SUM(ar.total_profit) AS total_profit,
                    SUM(ar.total_orders) AS total_orders
                FROM unique_classified_skus ucs
                LEFT JOIN aggregated_revenue ar
                    ON ucs.parent_sku = ar.parent_sku
                """

        if search:
            query += f" WHERE ucs.classification ILIKE '%{search}%'"

        query += " GROUP BY ucs.classification" 

        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            nulls_order = "NULLS FIRST" if sort_direction.lower() == "asc" else "NULLS LAST"
            if sort_array[0] in ["total_sales", "total_profit", "total_cost", "total_orders"]:
                query += f" ORDER BY {sort_array[0]} {sort_direction} {nulls_order}"
            else:
                query += f" ORDER BY ucs.classification {sort_direction} {nulls_order}"

        if classification_array:
            result = conn.execute(text(query), {"start_date": start_date, "end_date": end_date, "classification_filter_list": classification_array}).fetchall()
        else:
            result = conn.execute(text(query), {"start_date": start_date, "end_date": end_date}).fetchall()
        data = []
        for row in result:
            data.append({
                "CLASSIFICATION NAME": row[0],
                "ORDERS": int(row[4]) if row[4] else 0,
                "COST": float(round(row[2] or 0, 2)),
                "TOTAL SALES": float(round(row[1] or 0, 2)),
                "PROFIT": float(round(row[3] or 0, 2)) if row[3] else 0
            })

        # Total Profit Query
        total_profit_query = f"""
                                WITH unique_classified_skus AS (
                                    SELECT DISTINCT parent_sku, classification
                                    FROM {pg_db.skuvault_catalog}
                                    WHERE classification IS NOT NULL AND classification != ''
                                ),
                                aggregated_revenue AS (
                                    SELECT parent_sku, SUM(profit) AS total_profit
                                    FROM analytics.profitability_products_revenue
                                    WHERE order_date BETWEEN :start_date AND :end_date
                                    GROUP BY parent_sku
                                )
                                SELECT SUM(ar.total_profit) AS total_profit
                                FROM unique_classified_skus ucs
                                LEFT JOIN aggregated_revenue ar ON ucs.parent_sku = ar.parent_sku
                            """

        total_profit_all_classifications = conn.execute(text(total_profit_query), {"start_date": start_date, "end_date": end_date}).scalar() or 0
        
        classifications = _calculate_profit_and_contribution(data, total_profit_all_classifications)
    
        data = []
        cst = ZoneInfo("America/Chicago")         
        # Create CSV content in memory
        if classifications:
            utc_now = datetime.datetime.now(pytz.utc)
            cst_now = utc_now.astimezone(pytz.timezone('America/Chicago'))
            created_date = cst_now.strftime("%m-%d-%Y_%H:%M")
            file_name = f'Classifications_Profitability_Report_{created_date}.csv'
            csv_content = io.StringIO()
            keys = list(classifications[0].keys())
            writer = csv.DictWriter(csv_content, fieldnames=keys)
            writer.writeheader()
            for row in classifications:
                writer.writerow(row)

            email_util.send_classifications_profitability_report_email(store_id, csv_content, query_params['username'], file_name, user_name, created_date)
    
    finally:
        conn.close()
    
def get_orders_profitability_csv(store_id, query_params):
    conn = pg_db.get_connection(store_id)
    try:
        cst = ZoneInfo("America/Chicago")  
        user_details = store_db.fetch_user_by_username(store_id, query_params['username'])
        user_name = user_details['name'] if 'name' in user_details and user_details else ''

        start_date = query_params.get('start_date', None)
        end_date = query_params.get('end_date', None)
        search = query_params.get('search', None).strip()
        sort_by = query_params.get('sort_by', '').strip()
        sort_array = sort_by.split("/") if sort_by != '' else []
        customer_group_ids = query_params.get('customer_group_id', '')
        sales_rep_emails = query_params.get('sales_rep_email', '')
        customer_id = query_params.get('customer_id', None)
        customer_type = query_params.get('customer_type', '')
        supplier_name = query_params.get('supplier_name', '')
        classification = query_params.get('classification', '')
        purchaser_name = query_params.get('purchaser_name', '')
        product_id = query_params.get('product_id', None)
        rep_type = query_params.get('rep_type', '')

        params = {}
        where_clauses = []
        item_where_clauses = []
        # where_clauses.append("o.order_status_id NOT IN (0,5,6,7)")
        if start_date and end_date:
            where_clauses.append("o.order_date BETWEEN :start_date AND :end_date")
            params.update({"start_date": start_date, "end_date": end_date})

        # Add search filter if provided
        if search:
            search = search.strip().replace(" ", "")
            where_clauses.append(f"""(
                c.first_name ILIKE :search_pattern OR
                c.last_name ILIKE :search_pattern OR
                REPLACE(CONCAT(c.first_name, ' ', c.last_name), ' ', '') ILIKE :search_pattern OR
                c.email ILIKE :search_pattern OR
                o.order_id::text ILIKE :search_pattern
            )""")
            params["search_pattern"] = f"%{search}%"

        # Add customer group filter if provided
        if customer_group_ids:
            group_id_array = list(map(int, customer_group_ids.split(","))) if isinstance(customer_group_ids, str) else []
            where_clauses.append("c.customer_group_id = ANY(:customer_group_ids)")
            params["customer_group_ids"] = group_id_array

        # Add sales rep filter if provided
        if sales_rep_emails:
            emails = sales_rep_emails.split(",") if isinstance(sales_rep_emails, str) else []
            where_clauses.append("scr.rep_email = ANY(:sales_rep_emails)")
            params["sales_rep_emails"] = emails
        
        # Add customer ID filter if provided
        if customer_id:
            where_clauses.append("o.customer_id = :customer_id")
            params["customer_id"] = int(customer_id)
        
        # Add customer type filter if provided
        if customer_type:
            where_clauses.append("scr.type = :customer_type")
            params["customer_type"] = str(customer_type)
        
        # Add supplier filter if provided
        if supplier_name:
            item_where_clauses.append("ol.primary_supplier = :supplier_name")
            params["supplier_name"] = str(supplier_name)

        # Add classification filter if provided
        if classification:
            item_where_clauses.append("ol.classification = :classification")
            params["classification"] = str(classification)
        
        # Add purchaser filter if provided
        if purchaser_name:
            item_where_clauses.append("ol.purchaser_name = :purchaser_name")
            params["purchaser_name"] = str(purchaser_name)
        
        # Add product filter if provided
        if product_id:
            item_where_clauses.append("ol.product_id = :product_id")
            params["product_id"] = int(product_id)
        
        # Add rep type filter if provided
        if rep_type:
            where_clauses.append("scr.rep_type = :rep_type")
            params["rep_type"] = str(rep_type)

        # Build the WHERE clauses
        where_sql = f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""
        item_where_sql = f"WHERE {' AND '.join(item_where_clauses)}" if item_where_clauses else ""

        # Main Query - Use CTE for better performance and readability
        query = f"""
        WITH filtered_orders AS (
            SELECT DISTINCT o.order_id
            FROM analytics.bc_orders o
            LEFT JOIN customers c ON o.customer_id = c.customer_id
            LEFT JOIN {pg_db.salesforce_customer_rep_table} scr ON scr.customer_id = c.customer_id
            {where_sql}
        ),
        filtered_items AS (
            SELECT DISTINCT ol.order_id
            FROM analytics.bc_order_line_items ol
            {item_where_sql}
        ),
        final_filtered_orders AS (
            SELECT fo.order_id
            FROM filtered_orders fo
            {f"JOIN filtered_items fi ON fo.order_id = fi.order_id" if item_where_clauses else ""}
        )
        SELECT DISTINCT
            o.order_date,
            o.order_id,
            CONCAT(c.first_name, ' ', c.last_name) AS name,
            c.email,
            c.customer_group_name,
            scr.rep_name AS sales_rep_name,
            o.subtotal_ex_tax AS subtotal,
            o.coupon_discounts AS coupon_discounts,
            o.total_tax AS tax,
            o.shipping_inc_tax AS shipping,
            o.total_inc_tax AS total,
            o.total_items AS total_sell,
            o.total_revenue AS total_revenue,
            o.total_cost AS total_cost,
            o.total_profit AS total_profit,
            c.customer_group_id,
            c.customer_id,
            o.profit_percentage
        FROM analytics.bc_orders o
        JOIN final_filtered_orders ffo ON o.order_id = ffo.order_id
        LEFT JOIN customers c ON o.customer_id = c.customer_id
        LEFT JOIN {pg_db.salesforce_customer_rep_table} scr ON scr.customer_id = c.customer_id
        """

        # Add sorting if provided
        if sort_array and sort_array[0]:
            col, direction = sort_array
            sort_col = col if col in [
                "order_date", "order_id", "name", "email", "customer_group_name", "sales_rep_name", "subtotal",
                "coupon_discounts", "tax", "shipping", "total", "total_cost", "total_sell", 'total_revenue', 'total_profit', 'profit_percentage'
            ] else "order_date"
            sort_direction = "ASC" if direction == "1" else "DESC"
            nulls_order = "NULLS FIRST" if sort_direction == "ASC" else "NULLS LAST"
            query += f" ORDER BY {sort_col} {sort_direction} {nulls_order}"

        # Execute the query
        result = conn.execute(text(query), params).fetchall()
        order_data = []
        for row in result:
            order_data.append({
                "ORDER DATE": row[0].astimezone(cst).strftime("%b %d, %Y, %I:%M %p") if isinstance(row[0], datetime.datetime) else str(row[0]),
                "ORDER ID": row[1],
                "CUSTOMER NAME": row[2],
                "CUSTOMER EMAIL": row[3],
                "CUSTOMER GROUP": row[4],
                "SALES REP NAME": row[5],
                "SUBTOTAL": float(row[6] or 0),
                "COUPON DISCOUNTS": float(row[7] or 0),
                "TAX": float(row[8] or 0),  
                "SHIPPING": float(row[9] or 0),
                "TOTAL": float(row[10] or 0),
                "COST": float(row[13] or 0),
                "TOTAL SELL": int(row[11] or 0),
                "PROFIT": float(row[14] or 0),
                "REVENUE": float(row[12] or 0),
                "PROFIT(%)": f"{float(row[17] or 0)}%"
            })

        # Total Profit Query
        total_profit_query = f"""
            SELECT COALESCE(SUM(o.total_profit), 0) as profit 
            FROM analytics.bc_orders o
            WHERE o.order_date BETWEEN :start_date AND :end_date"""
        total_profit_params = {"start_date": start_date, "end_date": end_date}
        total_profit = conn.execute(text(total_profit_query), total_profit_params).scalar()
        
        order_data = _calculate_profit_and_contribution(order_data, total_profit, calculate_profit_percentage=False)
    
        data = []       
        # Create CSV content in memory
        if order_data:
            utc_now = datetime.datetime.now(pytz.utc)
            cst_now = utc_now.astimezone(pytz.timezone('America/Chicago'))
            created_date = cst_now.strftime("%m-%d-%Y_%H:%M")
            file_name = f'Orders_Profitability_Report_{created_date}.csv'
            csv_content = io.StringIO()
            keys = list(order_data[0].keys())
            writer = csv.DictWriter(csv_content, fieldnames=keys)
            writer.writeheader()
            for row in order_data:
                writer.writerow(row)

            email_util.send_orders_profitability_report_email(store_id, csv_content, query_params['username'], file_name, user_name, created_date)
    
    finally:
        conn.close()
        if conn:
            conn.close()

def get_brands_profitability_csv(store_id, query_params):
    conn = pg_db.get_connection(store_id)
    try:
        cst = ZoneInfo("America/Chicago")  
        user_details = store_db.fetch_user_by_username(store_id, query_params['username'])
        user_name = user_details['name'] if 'name' in user_details and user_details else ''

        start_date = query_params.get('start_date', None)
        end_date = query_params.get('end_date', None)
        sort_by = query_params.get('sort_by', '').strip()
        sort_array = sort_by.split("/") if sort_by != '' else []
        search = query_params.get('search', '').strip()
        brand_ids = query_params.get('brand_ids', None)
        purchaser_emails = query_params.get('purchaser_emails', None)

        params = {}
        brand_where_clauses = []
        purchaser_where_clauses = []

        # Add date range filter if provided
        if start_date and end_date:
            params.update({"start_date": start_date, "end_date": end_date})

        # Add search filter if provided - applied in first CTE
        if search:
            search = search.strip()
            brand_where_clauses.append("b.brand_name ILIKE :search_pattern")
            params["search_pattern"] = f"%{search}%"

        # Add brand filter if provided - applied in first CTE
        if brand_ids:
            brand_id_array = list(map(int, brand_ids.split(","))) if isinstance(brand_ids, str) else []
            brand_where_clauses.append("b.brand_id = ANY(:brand_ids)")
            params["brand_ids"] = brand_id_array

        # Add purchaser filter if provided - applied in purchaser_info CTE
        if purchaser_emails:
            emails = purchaser_emails.split(",") if isinstance(purchaser_emails, str) else []
            purchaser_where_clauses.append("string_to_array(pi.purchaser_emails, ', ') && ARRAY[:purchaser_emails]")
            params["purchaser_emails"] = emails

        # Build the WHERE clauses
        brand_where_sql = f"WHERE {' AND '.join(brand_where_clauses)}" if brand_where_clauses else ""
        purchaser_where_sql = f"WHERE {' AND '.join(purchaser_where_clauses)}" if purchaser_where_clauses else ""

        # Main Query - Using correct tables with aggregated purchaser information
        query = f"""
        WITH brand_metrics AS (
            SELECT
                b.brand_id,
                b.brand_name,
                SUM(oli.total_ex_tax) AS total_revenue,
                SUM(oli.total_cost) AS total_cost,
                SUM(oli.profit) AS total_profit,
                COUNT(DISTINCT oli.order_id) AS total_orders
            FROM analytics.bc_brands b
            LEFT JOIN analytics.bc_order_line_items oli
                ON oli.brand_id = b.brand_id
                AND oli.order_created_date_time BETWEEN :start_date AND :end_date
            {brand_where_sql}
            GROUP BY b.brand_id, b.brand_name
        ),
        purchaser_info AS (
            SELECT
                bpm.brand_id,
                STRING_AGG(DISTINCT bpm.purchaser_name, ', ') AS purchaser_names,
                STRING_AGG(DISTINCT bpm.purchaser_email, ', ') AS purchaser_emails
            FROM public.brand_purchaser_mapping bpm
            GROUP BY bpm.brand_id
        )
        SELECT
            bm.brand_id,
            bm.brand_name,
            COALESCE(pi.purchaser_names, '-') AS purchaser_name,
            COALESCE(pi.purchaser_emails, '-') AS purchaser_email,
            COALESCE(bm.total_revenue, 0) AS total_revenue,
            COALESCE(bm.total_cost, 0) AS total_cost,
            COALESCE(bm.total_profit, 0) AS total_profit,
            COALESCE(bm.total_orders, 0) AS total_orders
        FROM brand_metrics bm
        LEFT JOIN purchaser_info pi
            ON bm.brand_id = pi.brand_id
        {purchaser_where_sql}
        """

        # Add sorting if provided
        if sort_array and sort_array[0]:
            col, direction = sort_array
            sort_col = col if col in [
                "brand_id", "brand_name", "purchaser_name", "total_revenue", 
                "total_cost", "total_profit", "total_orders"
            ] else "brand_name"
            sort_direction = "ASC" if direction == "1" else "DESC"
            nulls_order = "NULLS FIRST" if sort_direction == "ASC" else "NULLS LAST"
            query += f" ORDER BY {sort_col} {sort_direction} {nulls_order}"

        # Execute the query
        result = conn.execute(text(query), params).fetchall()

        # Process the results
        brand_data = []
        for row in result:
            brand_data.append({
                "BRAND ID": row[0],
                "BRAND NAME": row[1],
                "PURCHASER NAME": row[2],
                "PURCHASER EMAIL": row[3],
                "ORDERS": int(row[7] or 0),
                "COST": float(round(row[5] or 0, 2)),
                "TOTAL SALES": float(round(row[4] or 0, 2)),
                "PROFIT": float(round(row[6] or 0, 2))
            })

        # Total Profit Query - Only apply date range filter and consider only products with brand IDs
        total_profit_query = f"""
        SELECT 
            COALESCE(SUM(oli.profit), 0) as total_profit
        FROM analytics.bc_order_line_items oli
        JOIN analytics.bc_brands b ON oli.brand_id = b.brand_id
        WHERE oli.order_created_date_time BETWEEN :start_date AND :end_date
        """
        
        # Execute total profit query with only date range parameters
        total_profit_params = {"start_date": start_date, "end_date": end_date}
        result = conn.execute(text(total_profit_query), total_profit_params).fetchone()
        total_profit = result[0] if result else 0
        
        brand_data = _calculate_profit_and_contribution(brand_data, total_profit)
    
        # Create CSV content in memory
        if brand_data:
            utc_now = datetime.datetime.now(pytz.utc)
            cst_now = utc_now.astimezone(pytz.timezone('America/Chicago'))
            created_date = cst_now.strftime("%m-%d-%Y_%H:%M")
            file_name = f'Brands_Profitability_Report_{created_date}.csv'
            csv_content = io.StringIO()
            keys = list(brand_data[0].keys())
            writer = csv.DictWriter(csv_content, fieldnames=keys)
            writer.writeheader()
            for row in brand_data:
                writer.writerow(row)

            email_util.send_brands_profitability_report_email(store_id, csv_content, query_params['username'], file_name, user_name, created_date)
    
    finally:
        conn.close()
        if conn:
            conn.close()