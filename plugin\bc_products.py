from asyncio import QueueEmpty
from graphql import products_query
from utils import bc_util, redis_util, product_util, store_util
import datetime
import plugin
from mongo_db import catalog_db, fetchall_documents_from_storefront_collection
import logging
import traceback
import rule_engine
from mongo_db import product_db
import os

logger = logging.getLogger()

PRODUCT_API = "v3/catalog/products/{product_id}"
IMAGE_API="v3/catalog/products/{product_id}/images/{image_id}"
CUSTOM_FEILD_UPDATE_API="v3/catalog/products/{product_id}/custom-fields/{custom_id}"
CUSTOM_FEILD_API="v3/catalog/products/{product_id}/custom-fields"
PROMOTION_API="v3/promotions/{id}"
VARIANT_UPDATE_API = "v3/catalog/products/{product_id}/variants/{variant_id}"

def process_options(node):
    options = {}
    options['id'] = node['entityId']
    options['displayName'] = node['displayName']
    options['isRequired'] = node['isRequired']
    options['values'] = plugin.process_edges(node, 'values')
    return options

def process_variant(node):
    variant = {}
    variant['id'] = node['entityId']
    variant['sku'] = node['sku']
    variant['isPurchasable'] = node['isPurchasable']
    variant['inventory'] = node['inventory']
    variant['options'] = plugin.process_edges(node, 'options', process_options)
    variant['prices'] = node['prices']
    return variant

def process_product(product):
    result = {}
    result['_id'] = product['entityId']
    result['sku'] = product['sku']
    result['name'] = product['name']
    result['description'] = product['description']
    result['minPurchaseQuantity'] = product['minPurchaseQuantity']
    result['type'] = product['type']
    result['weight'] = product['weight']
    result['height'] = product['height']
    result['width'] = product['width']
    result['depth'] = product['depth']
    result['inventory'] = product['inventory']
    result['defaultImage'] = product['defaultImage']
    result['brand'] = product['brand']
    result['prices'] = product['prices']
    result['reviewSummary'] = product['reviewSummary']
    result['date_created'] = product['date_created']
    result['search_field'] = product['name'] + " " + product['sku'] + " " + str(product['id'])
    result['categories'] = plugin.process_edges(product,'categories')
    result['variants'] = plugin.process_edges(product, 'variants', process_variant)
    result['images'] = plugin.process_edges(product, 'images')
    return result

def fetch_products(store, query_params={}):
    api = "v3/catalog/products"
    req_body = {
        "query_params": query_params,
        "method": "GET",
        "url": api
    }
    status, res = bc_util.process_bc_api_request(store, req_body)    
    if str(status) == "200":
        for idx, obj in enumerate(res):
            image = ""
            custom_url = ""
            brand = redis_util.get_brand_by_id(store["id"], obj["brand_id"])
            brand_name = ""
            brand_url = ""
            if brand:
                brand_name = brand["name"]
                brand_url = brand["custom_url"]["url"]

            res[idx]["brand_name"] = brand_name
            res[idx]["brand_url"] = brand_url
            
            if 'images' in obj and len(obj['images']) > 0:
                for img in obj['images']:
                    if img['is_thumbnail']:
                        image = img['url_standard']
                        break  
                del res[idx]["images"]
            
            if 'custom_url' in obj and not obj['custom_url']['is_customized']:
                custom_url = res[idx]["custom_url"]["url"]
                del res[idx]["custom_url"]
            res[idx]["image"] = image
            res[idx]["custom_url"] = custom_url
    return status, res

def get_all_product_images(store, pid):
    url = PRODUCT_API.format(product_id=pid)
    bc_api = store_util.get_bc_api_creds(store)
    return bc_util.call_api(bc_api, "GET", url+"/images")

def delCustomFields(store,pid,cid):
    url = CUSTOM_FEILD_UPDATE_API.format(product_id=pid,custom_id=cid)
    bc_api = store_util.get_bc_api_creds(store)
    return bc_util.call_api(bc_api, "DELETE", url)

def delete_the_image(store, pid,imageid):
    url = IMAGE_API.format(product_id=pid,image_id=imageid)
    bc_api = store_util.get_bc_api_creds(store)
    return bc_util.call_api(bc_api, "DELETE", url)

def update_promotions(store, pid):
    url = PROMOTION_API.format(id=pid)
    req_body={
            "name": "Test product promo",
            "status": "DISABLED",
            "shipping_address": {
                "countries": [
                    {
                        "iso2_country_code": "US"
                    }
                ]
            }
        }
    bc_api = store_util.get_bc_api_creds(store)
    return bc_util.call_api(bc_api, "PUT", url,req_body=req_body)

def disable_promotion(store, pid, status):
    url = PROMOTION_API.format(id=pid)
    req_body={            
            "status": status,            
        }
    bc_api = store_util.get_bc_api_creds(store)
    return bc_util.call_api(bc_api, "PUT", url,req_body=req_body)

def update_product_visibility(store, pid, category_id):    
    url = PRODUCT_API.format(product_id=pid)    
    bc_api = store_util.get_bc_api_creds(store)
    query_params = {       
        "include_fields": "name,sku,categories",        
    }
    product_data = bc_util.call_api(bc_api, "GET", url,query_params=query_params)   
    product = product_data.json() 
    category_array = []
    if product:                
        category_array = product['data']['categories']        
    category_id.extend(category_array)
    req_body={            
            "categories": category_id,            
        }    
    return bc_util.call_api(bc_api, "PUT", url,req_body=req_body)

def delete_product_cutomFields(store, pid):    
    url = CUSTOM_FEILD_API.format(product_id=pid)
    bc_api = store_util.get_bc_api_creds(store)    
    custom_field_data = bc_util.call_api(bc_api, "GET", url)
    target_name = "[Promo]" 
    
    if custom_field_data.status_code == 200: 
        custom_field_data = custom_field_data.json() 
        category_array = []
        if custom_field_data:  
            category_array = custom_field_data['data']             
            if len(category_array):
                for field in category_array:
                    if target_name.lower() in str(field["name"]).lower():
                        promo_id = field["id"]
                        url = CUSTOM_FEILD_UPDATE_API.format(product_id=pid,custom_id=promo_id)                
                        bc_util.call_api(bc_api, "DELETE", url)                                                                           

def unhide_product(store, pid, status):    
    url = PRODUCT_API.format(product_id=pid)
    bc_api = store_util.get_bc_api_creds(store)       
    req_body={            
            "is_visible": status,            
        }    
    return bc_util.call_api(bc_api, "PUT", url,req_body=req_body)

def change_visibility_of_variant(store, vid, pid, status):    
    url = VARIANT_UPDATE_API.format(product_id=pid, variant_id=vid)
    bc_api = store_util.get_bc_api_creds(store)       
    req_body={            
            "purchasing_disabled": status,            
        }    
    return bc_util.call_api(bc_api, "PUT", url,req_body=req_body)

def get_product_card_query_param(page=1, limit=15, include_fields=[], include=[]):
    query_params = {
        "availability": "available",
        "direction": "desc",
        "is_visible": "true",
        "sort": "id",
        "include_fields": "name,sku,custom_url,brand_id,brand_name,order_quantity_minimum,order_quantity_maximum,availability_description,availability,reviews_rating_sum,reviews_count,price,retail_price,sale_price,calculated_price,inventory_level,inventory_warning_level,images,base_variant_id",
        "include": "images",
        "limit": limit,
        "page": page
    }
    if include_fields and len(include_fields) > 0:
        query_params["include_fields"] = ','.join(include_fields)

    if include and len(include) > 0:
        query_params["include"] = ','.join(include)

    return query_params

def fetch_featured_products(store, include_fields=[], include=[], page=1, limit=15):
    query_params = get_product_card_query_param(page, limit, include_fields, include)
    query_params["is_featured"] = 1
    return fetch_products(store, query_params)

def fetch_new_products(store, include_fields=[], include=[], page=1, limit=15):
    query_params = get_product_card_query_param(page, limit, include_fields, include)
    return fetch_products(store, query_params)

def fetch_popular_products(store, include_fields=[], include=[], page=1, limit=15):
    query_params = get_product_card_query_param(page, limit, include_fields, include)
    query_params["sort"] = "total_sold"
    return fetch_products(store, query_params)

def fetch_products_by_id(store, ids=[], include_fields=[], include=[], page=1, limit=50):
    query_params = get_product_card_query_param(page, limit, include_fields, include)
    query_params["id:in"] = ",".join(str(x) for x in ids)
    query_params["is_visible"]
    del query_params["availability"]
    return fetch_products(store, query_params)

def fetch_products_graphql(store):
    return plugin.fetch_all_with_pagination(store=store, query_builder=products_query, page_size=250, 
            resource_name="products", resource_processor=process_product,
            db_collection = catalog_db.PRODUCTS_COLLECTION)

def fetch_all_products(store, modified_date=None):
    return fetch_products_rest_api(store, modified_date)

def fetch_customer_group_pricing(store, customer_group_id, product_list):
    api = "v3/pricing/products"
    req_body = {
        "channel_id": 1,
        "currency_code": "USD",
        "customer_group_id": customer_group_id,
        "items": product_list
    }
    req_data = {
                "query_params": {},
                "method": "POST",
                "url": "v3/pricing/products",
                "body": req_body
            }
    bc_api = store_util.get_bc_api_creds(store)
    res = bc_util.process_api(bc_api, req_data, exclude_meta=False)
    prices = []
    if res["status_code"] == 200:
        data = res['data']['data']
        for pp in data:
            prices.append({
                "product_id": pp["product_id"],
                "variant_id": pp["variant_id"],
                "price": pp["price"]["as_entered"]
            })
    return prices

def _get_product_query_param(page=0, limit=10, include_fields=[], include=[]):
    query_params = {
        "direction": "desc",
        "sort": "id",
        "limit": limit,
        "page": page        
    }
    
    if not include_fields or len(include_fields) < 0:
        include_fields = ["name","sku","weight","width","depth","height","order_quantity_minimum","order_quantity_maximum","availability_description","availability","type","categories","custom_url","brand_id","is_featured","page_title","is_visible","is_price_hidden","price","retail_price","sale_price","calculated_price","inventory_level","inventory_warning_level", "date_created", "date_modified","images","options","variants","bulk_pricing_rules","custom_fields","total_sold","view_count", "option_set_id"]

    if not include or len(include) < 0:
        include = ["images","options","variants","bulk_pricing_rules","custom_fields"]

    query_params["include"] = ','.join(include)
    query_params["include_fields"] = ','.join(include_fields)

    return query_params

def _product_processor(store, products):
    product_util.update_bc_product_invetory_cache(store, products)
    for product in products:        
        date_modified = product.get("date_modified", None)
        if date_modified:
            date_modified = date_modified.split("+")[0]
            product["date_modified"] = datetime.datetime.strptime(date_modified, "%Y-%m-%dT%H:%M:%S")
    rule_engine.process_products(store, products)
    return products

def fetch_products_rest_api(store, modified_date=None):
    query_params = _get_product_query_param()
    if modified_date:
        query_params["date_modified:min"] = modified_date        
    
    api = "v3/catalog/products"
    return plugin.fetch_all_by_rest_api(store, api, limit_per_req=10, query_params=query_params, 
            db_collection=catalog_db.PRODUCTS_COLLECTION, db_process_threshold=100, 
            resource_processor=_product_processor)

def update_products(store, products=[]):    
    n = len(products)
    if n == 0:
        return
    
    query_params = _get_product_query_param()
    api = "v3/catalog/products"

    product_list = []
    batch_size = 50    
    for idx, product_id in enumerate(products):
        product_list.append(str(product_id))   
        if (len(product_list) == batch_size) or (idx == n-1):
            query_params['id:in'] = ",".join(product_list)
            #logger.info("Updating products: ", query_params['id:in'], query_params)
            plugin.fetch_all_by_rest_api(store, api, limit_per_req=batch_size, query_params=query_params, 
                    db_collection=catalog_db.PRODUCTS_COLLECTION, db_process_threshold=10,
                    resource_processor=_product_processor)
            product_list = []
    if len(product_list) > 0:
        query_params['id:in'] = ",".join(product_list)
        plugin.fetch_all_by_rest_api(store, api, limit_per_req=batch_size, query_params=query_params, 
                db_collection=catalog_db.PRODUCTS_COLLECTION, db_process_threshold=10,
                resource_processor=_product_processor)


def get_variant_details(store, pid, vid):    
    data = {}
    url = VARIANT_UPDATE_API.format(product_id=pid, variant_id=vid)
    bc_api = store_util.get_bc_api_creds(store)       
    req_body={}    
    res = bc_util.call_api(bc_api, "GET", url,req_body=req_body)
    if res.status_code == 200:
        data = res.json()["data"]        
    return data

def fetch_product_complex_rules(store):
    api_data = store_util.get_bc_api_creds(store)
    updated_record_count = 0
    products = catalog_db.get_products(store, query={"is_visible": True}, fields={"id": 1, "name": 1})
    if len(products):
        for product in products:
            product_id = product['id']
            query_params = {
                    "method": 'GET',
                    "url": "v3/catalog/products/" + str(product_id) + "/complex-rules",
                    "page": 1,
                    "limit": 250
                } 
            product_complex_rules = bc_util.fetch_all(api_data, query_params)
            if product_complex_rules and len(product_complex_rules):
                catalog_db.update_product_complex_rule(store, product_complex_rules[0])
                updated_record_count = updated_record_count + len(product_complex_rules)
            
    return updated_record_count

def fetch_product_name(store, product_id):
    # Fetch the product name and SKU from BigCommerce
    url = f'v3/catalog/products/{product_id}'
    bc_api = store_util.get_bc_api_creds(store)
    res = bc_util.call_api(bc_api, "GET", url, query_params={})

    if res.status_code == 200:
        product_data = res.json()
        product_name = product_data['data'].get('name', "")
        product_sku = product_data['data'].get('sku', "")  # Fetch SKU
        return product_name, product_sku  # Return both name and SKU
    else:
        print(f"Error fetching product name for product_id {product_id}: {res.status_code}")
        return "", ""
    
# def fetch_products(store):
#     # Fetch the product name and SKU from BigCommerce
#     url = f'v3/catalog/products'
#     query_params = {
#         "include": "variants"
#     }
#     bc_api = store_util.get_bc_api_creds(store)
#     res = bc_util.call_api(bc_api, "GET", url, query_params=query_params)

#     if res.status_code == 200:
#         product_data = res.json()
#         return product_data
#     else:
#         print(f"Error fetching product data: {res.status_code}")
#         return res.json()



def fetch_variant_name(store, product_id, variant_id):
    # Fetch the variant details from BigCommerce
    url = f'v3/catalog/products/{product_id}/variants/{variant_id}'
    bc_api = store_util.get_bc_api_creds(store)
    res = bc_util.call_api(bc_api, "GET", url, query_params={})

    if res.status_code == 200:
        variant_data = res.json()
        option_values = variant_data['data'].get('option_values', [])
        variant_sku = variant_data['data'].get('sku', "")  # Fetch SKU

        if not option_values:  # Check if option_values is empty
            return "Parent product", variant_sku

        # Combine the labels to create a variant name
        variant_name = " ".join([option['label'] for option in option_values])
        return variant_name, variant_sku  # Return both name and SKU
    else:
        print(f"Error fetching variant name for product_id {product_id}, variant_id {variant_id}: {res.status_code}")
        return "", ""



def fetch_variant(store, product_id, variant_id):
    # Fetch the variant details from BigCommerce
    url = f'v3/catalog/products/{product_id}/variants/{variant_id}'
    bc_api = store_util.get_bc_api_creds(store)
    res = bc_util.call_api(bc_api, "GET", url, query_params={})

    # success ...   
    if res.status_code == 200:
        return res.json(), 200
    else:
        # unprocess able entity...
        return res.json(), res.status_code

def fetch_bc_product_variant(store, product_id, variant_id):
    url = f"v3/catalog/products/{product_id}/variants/{variant_id}"
    bc_api = store_util.get_bc_api_creds(store)
    product_data = bc_util.call_api(bc_api, "GET", url) 
    product = {}
    if product_data.status_code == 200:  
        product = product_data.json()     
    return product

def update_bc_product_variants(store, req_body):
  url = f'v3/catalog/variants'
  bc_api = store_util.get_bc_api_creds(store)
  res = bc_util.call_api(bc_api, "PUT", url, req_body=req_body) 
  # success ...   
  if res.status_code == 200:
      return res.json(), 200
  else:
      # unprocess able entity...
      return res.json(), res.status_code


def fetch_bc_product(store, pid):
    url = f"v3/catalog/products/{pid}"
    bc_api = store_util.get_bc_api_creds(store)
    query_params = {                
        "include_fields": "name,type,sku,description,weight,width,depth,height,price,cost_price,retail_price,map_price,categories,brand_id,brand_name,inventory_warning_level,is_visible,custom_url,brand_id,brand_name,reviews_rating_sum,reviews_count,price,retail_price,sale_price,calculated_price,inventory_level,custom_fields,images,videos,variants,options,mpn,gtin,upc,bin_picking_number,price,tax_class_id,inventory_tracking,search_keywords,sort_order,warranty,availability,availability_description,condition,is_condition_shown,custom_fields,is_free_shipping,fixed_cost_shipping_price,order_quantity_maximum,order_quantity_minimum,page_title,meta_description,fixed_cost_shipping_price",
        "include": "custom_fields,images,variants,videos,variants,options",
    }
    product_data = bc_util.call_api(bc_api, "GET", url,query_params=query_params) 
    product = {}
    if product_data.status_code == 200:  
        product = product_data.json()     
    return product


def update_bc_product(store, req_body, product_id):
    url = f'v3/catalog/products/{product_id}'
    bc_api = store_util.get_bc_api_creds(store)
    res = bc_util.call_api(bc_api, "PUT", url, query_params={} ,req_body=req_body) 

    # success ...   
    if res.status_code == 200:
        return res.json(), 200
    else:

        # unprocess able entity...
        return res.json(), res.status_code
    
def fetch_bc_product_variants(store, product_id):
    url = f"v3/catalog/products/{product_id}/variants"
    bc_api = store_util.get_bc_api_creds(store)
    variant_data = bc_util.call_api(bc_api, "GET", url, query_params={}) 
    variants = []
    if variant_data.status_code == 200:  
        variants = variant_data.json()['data']     
    return variants

def remove_msda_pmts_unlinked_products_documents(store_id):
    response = {
        "status": 200,
        "message": "Cleanup completed",
        "summary": {
            "total_files_checked": 0,
            "files_removed": 0,
            "files_kept": 0,
            "errors": []
        }
    }
    
    try:
        # Base path for documents
        base_path = "/app/images/products"
        attachment_types = ['msda', 'pmta']
        
        # Get all attachments from MongoDB
        attachments = fetchall_documents_from_storefront_collection(
            store_id,
            product_db.MSDA_PMTA_ATTACHMENTS_COLLECTION,
            {},
            {"attachments": 1}
        )
        
        # Create a set of valid file paths from MongoDB
        valid_file_paths = set()
        for doc in attachments:
            if doc.get('attachments'):
                # Extract paths from msda attachments
                if 'msda' in doc['attachments']:
                    for attachment in doc['attachments']['msda']:
                        if attachment.get('file_path'):
                            file_path = attachment['file_path']
                            valid_file_paths.add(file_path)
                
                # Extract paths from pmta attachments
                if 'pmta' in doc['attachments']:
                    for attachment in doc['attachments']['pmta']:
                        if attachment.get('file_path'):
                            file_path = attachment['file_path']
                            valid_file_paths.add(file_path)

        # Process each attachment type directory
        for attachment_type in attachment_types:
            type_path = os.path.join(base_path, attachment_type)
            # Skip if directory doesn't exist
            if not os.path.exists(type_path):
                continue
            # Get all month_year folders
            month_folders = [f for f in os.listdir(type_path) if os.path.isdir(os.path.join(type_path, f))]
            for month_folder in month_folders:
                folder_path = os.path.join(type_path, month_folder)
                # Get all files in the month folder
                try:
                    files = [f for f in os.listdir(folder_path) if os.path.isfile(os.path.join(folder_path, f))]
                    response["summary"]["total_files_checked"] += len(files)
                    
                    for file_name in files:
                        file_path = os.path.join(folder_path, file_name)
                        try:
                            # Check if file path exists in MongoDB
                            if file_path not in valid_file_paths:
                                # File is not linked in MongoDB, remove it
                                os.remove(file_path)
                                response["summary"]["files_removed"] += 1
                            else:
                                response["summary"]["files_kept"] += 1
                                
                        except Exception as e:
                            response["summary"]["errors"].append({
                                "file": file_path,
                                "error": str(e)
                            })
                            
                    # Remove empty month folder
                    if not os.listdir(folder_path):
                        os.rmdir(folder_path)
                        
                except Exception as e:
                    response["summary"]["errors"].append({
                        "folder": folder_path,
                        "error": str(e)
                    })
        
        # Update response message based on results
        if response["summary"]["files_removed"] > 0:
            logger.info(f"Cleanup completed. Removed {response['summary']['files_removed']} unlinked files, kept {response['summary']['files_kept']} files.")
        else:
            logger.info(f"No unlinked files found. All {response['summary']['files_kept']} files are properly linked.")
            
        if response["summary"]["errors"]:
            logger.info(f"Encountered {len(response['summary']['errors'])} errors during cleanup.")
            
    except Exception as e:
        logger.info(f"Error during cleanup: {str(e)}")