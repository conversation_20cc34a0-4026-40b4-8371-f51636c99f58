from collections import defaultdict
from mongo_db import catalog_db, store_db
import mongo_db
from utils import store_util, redis_util, email_util, common_util
from plugin import bc_products, bc_price_list, bc_product
from bson import ObjectId
import logging
import traceback
logger = logging.getLogger()
from concurrent.futures import ThreadPoolExecutor, as_completed
from pymongo import UpdateOne, DeleteOne, InsertOne
from datetime import datetime
from pymongo import UpdateOne, InsertOne
import logging
from pymongo import IndexModel
from pymongo import DESCENDING
from datetime import timezone
import pg_db
from sqlalchemy import text
from googleapiclient.discovery import build
from google.oauth2.service_account import Credentials
from itertools import islice
import io
import csv
import zipfile
import pytz
import task
from pg_db import analytics_db
import math
from pg_db_utils import pg_skuvault_sales_util
from pg_db_utils import pg_analytics_util
from utils import agile_project_utils
import time
from utils import redis_util
from templates import html_template
import threading

logger = logging.getLogger()
import time

PRICE_LIST_COLLECTION = "product_price_lists"

product_fields = {"id": 1, 
                  "name": 1, 
                  "sku": 1, 
                  "is_visible":1, 
                  "variants": {"id": 1, "product_id": 1, "sku": 1}
                  }

all_product_fields = {"id": 1, 
                      "name": 1, 
                      "sku": 1, 
                      "sale_price": 1,
                      "price": 1,
                      "date_created": 1,
                      "inventory_level": 1,
                      "categories": 1,
                      "variants": {"id": 1, "sku": 1, "sale_price": 1, "price": 1, "inventory_level": 1, "option_values": 1}
                      }

def sync_price_lists_in_mongodb(store_id):
    try:
        # Fetch store and database clients
        store = mongo_db.get_store_by_id(store_id)
        db = mongo_db.get_admin_db_client_for_store_id(store_id)
        store_db = mongo_db.get_store_db_client_for_store_id(store_id)

        # Ensure parent_product_id and variant_id are indexed for fast lookup
        db[PRICE_LIST_COLLECTION].create_indexes([
            IndexModel([("parent_product_id", DESCENDING)]),
            IndexModel([("variants.variant_id", DESCENDING)])
        ])

        # Reset syncing status for existing records in bulk
        db[PRICE_LIST_COLLECTION].update_many({}, {"$set": {"syncing": False}})

        # Fetch all products and price lists
        start_time = time.time()
        all_products = list(store_db["products"].find(projection=all_product_fields))
        end_time = time.time()
        logging.info(f"Time taken to fetch products: {end_time - start_time} seconds")
        start_time = time.time()
        res, status_code = bc_price_list.fetch_price_lists(store)
        end_time = time.time()
        logging.info(f"Time taken to fetch price lists: {end_time - start_time} seconds")
        if status_code != 200:
            logging.error(f"Error fetching price lists: {status_code}")
            return
            
        # price_lists = res['data']
        price_lists = [pl for pl in res['data'] if pl.get('active')]
        
        bulk_operations = []
        variant_skus = [v['sku'] for p in all_products for v in p['variants']]
        product_ids = [p['id'] for p in all_products]

        start_time = time.time()
        variant_costs = _fetch_cost_and_po_average_cost(store_id, variant_skus)
        end_time = time.time()
        logging.info(f"Time taken to fetch variant costs: {end_time - start_time} seconds")

        start_time = time.time()
        months_data = _fetch_products_months_data(store_id, product_ids)
        end_time = time.time()
        logging.info(f"Time taken to fetch product months data: {end_time - start_time} seconds")

        start_time = time.time()
        variant_months_data = _fetch_variants_months_data(store_id, variant_skus)
        end_time = time.time()
        logging.info(f"Time taken to fetch variant months data: {end_time - start_time} seconds")


        start_time = time.time()
        # Process products and prepare bulk insert operations
        for product in all_products:
            default_price = product.get('sale_price') or product.get('price')
            
            new_product_entry = {
                "parent_product_id": product['id'],
                "parent_product_name": product['name'],
                "parent_product_sku": product['sku'],
                "default_price": default_price,
                "created_at": product['date_created'],
                "inventory_level": product.get('inventory_level', 0),
                "categories": product.get('categories', []),
                "variants": [],
                "syncing": True,
                "month_1": months_data.get(product['id'], {}).get('month_1', 0),
                "month_2": months_data.get(product['id'], {}).get('month_2', 0),
                "month_3": months_data.get(product['id'], {}).get('month_3', 0),
                "month_4": months_data.get(product['id'], {}).get('month_4', 0),
                "month_5": months_data.get(product['id'], {}).get('month_5', 0),
                "month_6": months_data.get(product['id'], {}).get('month_6', 0),
                "month_7": months_data.get(product['id'], {}).get('month_7', 0),
                "price_update_status": "success",
            }

            total_sum = sum([
                int(months_data.get(product['id'], {}).get(f'month_{i}', 0) or 0) for i in range(2, 8)
            ])
            turn_rate = (((int(total_sum) / int(product['inventory_level']) if int(product['inventory_level']) != 0 else 1) * 365) / 181)
            weeks_on_hand = 52 / turn_rate if turn_rate != 0 else 1

            new_product_entry['turn_rate'] = turn_rate
            new_product_entry['weeks_on_hand'] = weeks_on_hand


            # Initialize min values with None
            min_variant_cost = None
            min_latest_po = None

            # Initialize for average po cost calculation
            po_cost_sum = 0
            po_cost_count = 0

            # Process each variant in the product
            for variant in product.get('variants', []):
                variant_price = variant.get('sale_price') or variant.get('price') or default_price
                variant_name = " - ".join(option['label'] for option in variant.get('option_values', [])) or "Parent Product"
                cost_data = variant_costs.get(variant['sku'], {})

                variant_cost = cost_data.get("cost")
                po_avg_cost = cost_data.get("po_average_cost") 
                latest_po = cost_data.get("latest_po")
                # variant_cost = variant_cost if variant_cost is not None else 0
                # po_avg_cost = po_avg_cost if po_avg_cost is not None else 0
                # latest_po = latest_po if latest_po is not None else 0

                # Track minimums
                # if min_variant_cost is None or variant_cost < min_variant_cost:
                #     min_variant_cost = variant_cost

                # if min_po_avg_cost is None or po_avg_cost < min_po_avg_cost:
                #     min_po_avg_cost = po_avg_cost

                # if min_latest_po is None or latest_po < min_latest_po:
                #     min_latest_po = latest_po

                # Track minimums
                if variant_cost is not None and (min_variant_cost is None or variant_cost < min_variant_cost):
                    min_variant_cost = variant_cost

                # if po_avg_cost is not None and (min_po_avg_cost is None or po_avg_cost < min_po_avg_cost):
                #     min_po_avg_cost = po_avg_cost

                if latest_po is not None and (min_latest_po is None or latest_po < min_latest_po):
                    min_latest_po = latest_po

                # Track po_avg_cost sum and count (only non-null values)
                if po_avg_cost is not None:
                    po_cost_sum += po_avg_cost
                    po_cost_count += 1

                total_sum = sum([
                    int(variant_months_data.get(variant['sku'], {}).get(f'month_{i}', 0) or 0) for i in range(2, 8)
                ])
                variant_turn_rate = (((int(total_sum) / int(variant['inventory_level']) if int(variant['inventory_level']) != 0 else 1) * 365) / 181)
                variant_weeks_on_hand = 52 / variant_turn_rate if variant_turn_rate != 0 else 1
                
                new_product_entry['variants'].append({
                    "variant_id": variant['id'],
                    "variant_name": variant_name,
                    "variant_sku": variant['sku'],
                    "variant_price": variant_price,
                    # "variant_cost": variant_cost,
                    # "variant_po_average_cost": po_avg_cost, # last 3 po average cost
                    # "variant_latest_po_cost": latest_po,
                    "variant_cost": variant_cost if variant_cost is not None else None,
                    "variant_po_average_cost": po_avg_cost if po_avg_cost is not None else None,
                    "variant_latest_po_cost": latest_po if latest_po is not None else None,
                    "inventory_level": variant.get('inventory_level', 0),
                    "month_1": variant_months_data.get(variant['sku'], {}).get('month_1', 0),
                    "month_2": variant_months_data.get(variant['sku'], {}).get('month_2', 0),
                    "month_3": variant_months_data.get(variant['sku'], {}).get('month_3', 0),
                    "month_4": variant_months_data.get(variant['sku'], {}).get('month_4', 0),
                    "month_5": variant_months_data.get(variant['sku'], {}).get('month_5', 0),
                    "month_6": variant_months_data.get(variant['sku'], {}).get('month_6', 0),
                    "month_7": variant_months_data.get(variant['sku'], {}).get('month_7', 0),
                    "turn_rate": variant_turn_rate,
                    "weeks_on_hand": variant_weeks_on_hand,
                    "price_list": []
                })


            # After looping all variants, calculate average po cost
            avg_po_cost_of_all_variants = (po_cost_sum / po_cost_count) if po_cost_count > 0 else None
            avg_po_cost_of_all_variants = round(avg_po_cost_of_all_variants, 2) if avg_po_cost_of_all_variants is not None else None

            # Calculate cost_margin only if valid costs exist to avoid division by zero
            if min_variant_cost is not None and avg_po_cost_of_all_variants not in (None, 0):
                cost_margin = ((min_variant_cost - avg_po_cost_of_all_variants) / avg_po_cost_of_all_variants) * 100
            else:
                cost_margin = None

            product_cost = min_variant_cost if min_variant_cost is not None and min_variant_cost > 0 else 0
            product_default_price = default_price if default_price is not None and default_price > 0 else 0

            if product_cost > 0 and product_default_price > 0:
                new_product_entry['default_price_margin'] = round(((product_default_price - product_cost) / product_cost) * 100, 2)
            else:
                new_product_entry['default_price_margin'] = 0

            # if min_po_avg_cost is None or min_po_avg_cost == 0:
            #     cost_margin = None

            new_product_entry["cost_margin"] = cost_margin
            new_product_entry["avg_po_cost_of_all_variants"] = avg_po_cost_of_all_variants
            new_product_entry["min_variant_cost"] = min_variant_cost
            new_product_entry["min_latest_po"] = min_latest_po
            
            bulk_operations.append(InsertOne(new_product_entry))

        # Insert new products in bulk
        if bulk_operations:
            db[PRICE_LIST_COLLECTION].bulk_write(bulk_operations)

        end_time = time.time()
        logging.info(f"Time taken to insert new products: {end_time - start_time} seconds")

        for price_list in price_lists:
            start_time = time.time()
            
            price_list_id = price_list['id']
            price_list_name = price_list['name']
            
            # This should return a dict like {variant_id: price, ...}
            price_dict = bc_price_list.fetch_records_by_price_list_id(store, price_list_id)
            
            end_time = time.time()
            logging.info(f"Time taken to fetch records: {end_time - start_time} seconds for {price_list_id} {price_list_name} with {len(price_dict)} entries")

            start_time = time.time()
            bulk_ops = []

            for variant_id, data in price_dict.items():
                price = data.get("price")
                date_modified = data.get("date_modified")

                update_query = {
                    "variants.variant_id": variant_id,
                    "syncing": True
                }

                update_doc = {
                    "$push": {
                        "variants.$.price_list": {
                            "price_list_id": price_list_id,
                            "name": price_list_name,
                            "price": price
                        }
                    },
                    "$set": {
                        "updated_by": "BigCommerce"
                    }
                }

                # Add date_modified if available
                if date_modified:
                    update_doc["$set"]["variants.$.date_modified"] = date_modified

                bulk_ops.append(UpdateOne(update_query, update_doc))


            if bulk_ops:
                db[PRICE_LIST_COLLECTION].bulk_write(bulk_ops, ordered=False)

            end_time = time.time()
            logging.info(f"Time taken to process price list {price_list_id} ({price_list_name}): {end_time - start_time} seconds for {len(bulk_ops)} operations")

        start_time = time.time()
        # Step 1: Build vip_price_map using filtered query
        vip_price_map = {}

        cursor = db["static_price_lists"].find(
            {"variants.price_list.price_list_id": 52},
            {"variants.variant_id": 1, "variants.price_list": 1}
        )

        for doc in cursor:
            for variant in doc.get("variants", []):
                for pl in variant.get("price_list", []):
                    if pl.get("price_list_id") == 52:
                        vip_price_map[variant["variant_id"]] = pl.get("price")

        # Step 2: Build bulk operations for product_price_lists
        bulk_ops = []

        for variant_id, vip_price in vip_price_map.items():
            bulk_ops.append(UpdateOne(
                {
                    "variants.variant_id": variant_id,
                    "syncing": True
                },
                {
                    "$push": {
                        "variants.$.price_list": {
                            "price_list_id": 52,
                            "name": "VIP",
                            "price": vip_price
                        }
                    }
                }
            ))

        # Step 3: Execute bulk update
        if bulk_ops:
            db[PRICE_LIST_COLLECTION].bulk_write(bulk_ops, ordered=False)

        end_time = time.time()
        logging.info(f"Time taken to process static price lists: {end_time - start_time} seconds")

        bulk_updates = []

        price_list_products = db[PRICE_LIST_COLLECTION].find({"syncing": True}, {"parent_product_id": 1, "variants.date_modified": 1, "variants.price_list": 1, "variants.variant_cost": 1})

        for product in price_list_products:
            variant_dates = []
            lowest_prices = {}  # price_list_id => {"price": x, "cost": y}

            for variant in product.get("variants", []):
                dt = variant.get("date_modified")
                if dt:
                    # Parse string to datetime if needed (if stored as string)
                    if isinstance(dt, str):
                        dt = datetime.fromisoformat(dt.replace("Z", "+00:00"))
                    variant_dates.append(dt)
                
                cost_price = variant.get("variant_cost")
                price_lists = variant.get("price_list", [])

                if cost_price is not None and cost_price > 0:
                    for price_entry in price_lists:
                        price_list_id = price_entry.get("price_list_id")
                        price = price_entry.get("price")

                        if price_list_id is not None and price is not None:
                            current = lowest_prices.get(price_list_id)
                            if current is None or price < current["price"]:
                                lowest_prices[price_list_id] = {"price": price, "cost": cost_price}

            update_fields = {}

            # Set updated_at from latest date
            if variant_dates:
                latest_date = max(variant_dates)
                update_fields["updated_at"] = latest_date.astimezone(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")

            # Calculate and set margin_percentage_{price_list_id}
            for pl_id, data in lowest_prices.items():
                price = data["price"]
                cost = data["cost"]
                if cost > 0:
                    margin = round(((price - cost) / cost) * 100, 2)
                    update_fields[f"margin_percentage_{pl_id}"] = margin

            if update_fields:
                bulk_updates.append(
                    UpdateOne(
                        {"parent_product_id": product["parent_product_id"], "syncing": True},
                        {"$set": update_fields}
                    )
                )

        if bulk_updates:
            db[PRICE_LIST_COLLECTION].bulk_write(bulk_updates)


    except Exception as e:
        logger.error(traceback.format_exc())
        logging.error(f"Error syncing price lists: {e}")

    finally:
        logging.info("Finalizing sync process: updating and cleaning up records")
        
        # Clean up records marked for deletion
        delete_result = db[PRICE_LIST_COLLECTION].delete_many({"syncing": False})
        logging.info(f"Deleted {delete_result.deleted_count} records marked for deletion")
        
        # Reset syncing status for remaining records
        update_result = db[PRICE_LIST_COLLECTION].update_many({"syncing": True}, {"$set": {"syncing": False}})
        logging.info(f"Updated {update_result.modified_count} records with syncing status set to False")

    logging.info("Sync completed successfully.")


def _fetch_variants_months_data(store_id, variant_skus, batch_size=10000):
    conn = pg_db.get_connection(store_id)
    try:
        variant_monthly_data_map = {}

        total_batches = math.ceil(len(variant_skus) / batch_size)
        for i in range(total_batches):
            batch_skus = variant_skus[i * batch_size : (i + 1) * batch_size]

            query = text(f"""
                SELECT sku, month_1, month_2, month_3, month_4, month_5, month_6, month_7
                FROM {analytics_db.AnalyticsDB.get_replenishment_variants_table()}
                WHERE sku = ANY(:variant_skus)
            """)
            result = conn.execute(query, {"variant_skus": batch_skus}).fetchall()

            for row in result:
                variant_monthly_data_map[row[0]] = {
                    f"month_{i}": int(row[i]) if row[i] is not None else None
                    for i in range(1, 8)
                }

        return variant_monthly_data_map

    except Exception as e:
        logging.error(traceback.format_exc())
        logging.error(f"Error fetching variants months data: {e}")
        return {}
    finally:
        conn.close()


def _fetch_products_months_data(store_id, product_ids, batch_size=10000):
    conn = pg_db.get_connection(store_id)
    try:
        product_monthly_data_map = {}

        total_batches = math.ceil(len(product_ids) / batch_size)
        for i in range(total_batches):
            batch_product_ids = product_ids[i * batch_size : (i + 1) * batch_size]

            query = text(f"""
                SELECT product_id, month_1, month_2, month_3, month_4, month_5, month_6, month_7
                FROM {analytics_db.AnalyticsDB.get_replenishment_products_table()}
                WHERE product_id = ANY(:product_ids)
            """)
            result = conn.execute(query, {"product_ids": batch_product_ids}).fetchall()

            for row in result:
                product_monthly_data_map[row[0]] = {
                    f"month_{i}": int(row[i]) if row[i] is not None else None
                    for i in range(1, 8)
                }

        return product_monthly_data_map

    except Exception as e:
        logging.error(traceback.format_exc())
        logging.error(f"Error fetching months data: {e}")
        return {}
    finally:
        conn.close()

def _fetch_cost_and_po_average_cost(store_id, variant_skus, batch_size=10000):
    store_db = mongo_db.get_store_db_client_for_store_id(store_id)
    conn = pg_db.get_connection(store_id)
    try:
        variant_costs_dict = {}

        skuvault_pos_collection = store_db["skuvault_pos"]

        total_batches = math.ceil(len(variant_skus) / batch_size)
        for i in range(total_batches):
            batch_skus = variant_skus[i*batch_size : (i+1)*batch_size]

            # SQL query for this batch
            query = """ SELECT sku, cost FROM skuvault_catalog WHERE sku = ANY(:variant_skus) """
            cursor = conn.execute(text(query), {"variant_skus": batch_skus})
            variant_costs = cursor.fetchall()

            # Build initial cost dict
            for sku, cost in variant_costs:
                variant_costs_dict[sku] = {
                    "cost": cost,
                    "po_average_cost": None,
                    "latest_po": None,
                }

            # MongoDB query for this batch
            mongo_docs = skuvault_pos_collection.find({"_id": {"$in": batch_skus}})

            for doc in mongo_docs:
                pos_entries = doc.get("pos", {}).values()

                latest_3_costs = sorted(
                    pos_entries,
                    key=lambda x: x.get("created_at", ""),
                    reverse=True
                )[:3]

                if latest_3_costs:
                    avg_cost = sum(entry.get("cost", 0) for entry in latest_3_costs) / len(latest_3_costs)
                    if doc["_id"] in variant_costs_dict:
                        variant_costs_dict[doc["_id"]]["po_average_cost"] = round(avg_cost, 2) if avg_cost is not None else None
                        variant_costs_dict[doc["_id"]]["latest_po"] = latest_3_costs[0].get("cost", 0)

        return variant_costs_dict

    except Exception as e:
        logging.error(traceback.format_exc())
        logging.error(f"Error fetching cost and po average cost: {e}")
        return {}
    finally:
        conn.close()



def process_product_price_list_webhook(store_id, price_list_id, variant_id, operation):
    if operation == 'CREATE' or operation == 'UPDATE':
        update_or_create_price_list(store_id, price_list_id, variant_id)

    elif operation == 'DELETE':
        delete_price_list(store_id, price_list_id, variant_id)



def update_or_create_price_list(store_id, price_list_id, variant_id):
    store = mongo_db.get_store_by_id(store_id)
    db = mongo_db.get_admin_db_client_for_store_id(store_id)

    price_logs = []

    # Fetch price list details
    res, status_code = bc_price_list.fetch_price_list_by_id(store, price_list_id)
    if status_code != 200:
        logging.info(f"Failed to fetch price list. Status Code: {status_code}")
        return

    price_list_name = res.get("data", {}).get("name")
    if not price_list_name:
        logging.info("Price list name not found in the response.")
        return

    records = bc_price_list.fetch_records_by_variant_id(store, price_list_id, variant_id)
    record = next((r for r in records if r["price_list_id"] == price_list_id), None)
    if not record:
        logging.info(f"No matching record found for price_list_id {price_list_id}.")
        return

    price = record.get("price", 0)
    date_modified = record.get("date_modified")

    product = db["product_price_lists"].find_one({"variants.variant_id": variant_id})
    if not product:
        logging.info(f"No product found with variant_id: {variant_id}")
        return

    parent_product_id = product["parent_product_id"]
    parent_product_name = product.get("parent_product_name")
    parent_product_sku = product.get("parent_product_sku")

    variant = next((v for v in product["variants"] if v["variant_id"] == variant_id), None)
    if not variant:
        logging.info(f"Variant with ID {variant_id} not found in product.")
        return

    variant_name = variant.get("variant_name")
    variant_sku = variant.get("variant_sku")

    existing_price_list = next((pl for pl in variant.get("price_list", []) if pl.get("price_list_id") == price_list_id), None)

    if existing_price_list:
        old_price = existing_price_list.get("price", 0)
        updated_price = price

        # Compare old and updated price
        if old_price == updated_price:
            logging.info(f"Old price and updated price are the same for price_list_id {price_list_id}. No update required.")
            return
        
        # log_price_change(store_id, price_list_id, parent_product_id, variant_id, parent_product_name, parent_product_sku,
        #                  variant_name, variant_sku, old_price, updated_price, "BigCommerce")
        
        price_logs.append({
            "price_list_id": price_list_id,
            "variant_id": variant_id,
            "parent_product_name": parent_product_name,
            "parent_product_sku": parent_product_sku,
            "variant_name": variant_name,
            "variant_sku": variant_sku,
            "old_price": old_price,
            "new_price": updated_price,
            "updated_by": "BigCommerce"
        })

        db["product_price_lists"].update_one(
            {
                "parent_product_id": parent_product_id,
                "variants.variant_id": variant_id,
                "variants.price_list.price_list_id": price_list_id
            },
            {
                "$set": {
                    "variants.$.price_list.$[pl].name": price_list_name,
                    "variants.$.price_list.$[pl].price": updated_price
                }
            },
            array_filters=[{"pl.price_list_id": price_list_id}]
        )
    else:
        old_price = None  # No existing price
        updated_price = price
        # log_price_change(store_id, price_list_id, parent_product_id, variant_id, parent_product_name, parent_product_sku,
        #                  variant_name, variant_sku, old_price, updated_price, "BigCommerce")

        price_logs.append({
            "price_list_id": price_list_id,
            "variant_id": variant_id,
            "parent_product_name": parent_product_name,
            "parent_product_sku": parent_product_sku,
            "variant_name": variant_name,
            "variant_sku": variant_sku,
            "old_price": old_price,
            "new_price": updated_price,
            "updated_by": "BigCommerce"
        })

        new_price_list_entry = {
            "price_list_id": price_list_id,
            "name": price_list_name,
            "price": updated_price
        }
        db["product_price_lists"].update_one(
            {"parent_product_id": parent_product_id, "variants.variant_id": variant_id},
            {"$push": {"variants.$.price_list": new_price_list_entry}}
        )

    db["product_price_lists"].update_one(
        {"parent_product_id": parent_product_id},
        {
            "$set": {
                "updated_at": date_modified,
                "updated_by": "BigCommerce"
            }
        }
    )
    logger.info(f"Call from update_or_create_price_list")
    old_data_map = _fetch_old_data_of_products(store_id, [parent_product_id])
    if old_data_map != {}:
        task.submit_task('check_promo_product_and_create_ticket', (store['id'], [parent_product_id], old_data_map))
    # log_price_change(store_id, parent_product_id, price_logs)


def delete_price_list(store_id, price_list_id, variant_id):
    store = mongo_db.get_store_by_id(store_id)
    db = mongo_db.get_admin_db_client_for_store_id(store_id)

    price_logs = []
    records = bc_price_list.fetch_records_by_variant_id(store, price_list_id, variant_id)
    if not records:
        product = db["product_price_lists"].find_one({"variants.variant_id": variant_id})
        if not product:
            logging.info(f"No product found with variant_id: {variant_id}")

        parent_product_id = product["parent_product_id"]
        parent_product_name = product.get("parent_product_name")
        parent_product_sku = product.get("parent_product_sku")

        variant = next((v for v in product["variants"] if v["variant_id"] == variant_id), None)
        if not variant:
            logging.info(f"Variant with ID {variant_id} not found in product.")

        variant_name = variant.get("variant_name")
        variant_sku = variant.get("variant_sku")

        existing_price_list = next((pl for pl in variant.get("price_list", []) if pl.get("price_list_id") == price_list_id), None)
        if existing_price_list:
            old_price = existing_price_list.get("price", 0)
            # log_price_change(store_id, price_list_id, parent_product_id, variant_id, parent_product_name, parent_product_sku,
            #                  variant_name, variant_sku, old_price, None, "BigCommerce")

            price_logs.append({
                "price_list_id": price_list_id,
                "variant_id": variant_id,
                "parent_product_name": parent_product_name,
                "parent_product_sku": parent_product_sku,
                "variant_name": variant_name,
                "variant_sku": variant_sku,
                "old_price": old_price,
                "new_price": None,
                "updated_by": "BigCommerce"
            })

            db["product_price_lists"].update_one(
                {"parent_product_id": parent_product_id, "variants.variant_id": variant_id},
                {"$pull": {"variants.$.price_list": {"price_list_id": price_list_id}}}
            )

            db["product_price_lists"].update_one(
                {"parent_product_id": parent_product_id},
                {
                    "$set": {
                        "updated_at": datetime.now(timezone.utc).isoformat(timespec='seconds').replace('+00:00', 'Z'),
                        "updated_by": "BigCommerce"
                    }
                }
            )
            # log_price_change(store_id, parent_product_id, price_logs)
            logger.info(f"Call from delete_price_list")
            old_data_map = _fetch_old_data_of_products(store_id, [parent_product_id])
            if old_data_map != {}:
                task.submit_task('check_promo_product_and_create_ticket', (store['id'], [parent_product_id], old_data_map))
    else:
        logging.info(f"Price list {price_list_id} has entries for variant {variant_id}.")


def process_multiple_product_price_list_webhook(store_id, payload, operation):
    if operation == 'CREATE' or operation == 'UPDATE':
        update_or_create_multiple_price_lists(store_id, payload)

    elif operation == 'DELETE':
        delete_multiple_price_lists(store_id, payload)


def update_or_create_multiple_price_lists(store_id, payload):
    time.sleep(2)
    store = mongo_db.get_store_by_id(store_id)
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    price_logs = defaultdict(list)
    product_ids = []
    price_list_id = payload[0].get('price_list_id')

    # Group records by variant_id for efficient lookup
    variant_records = {record['variant_id']: record for record in payload}
    
    # Fetch all relevant products in a single query
    variant_ids = list(variant_records.keys())
    products = list(db["product_price_lists"].find({"variants.variant_id": {"$in": variant_ids}}))

    variant_ids_str = ','.join(map(str, variant_ids))
    records = bc_price_list.fetch_records_by_variant_id(store, price_list_id, variant_ids_str)
    if not records:
        logging.info(f"No matching record found for price_list_id {price_list_id}.")
        return
    
    for record in records:
        variant_id = record.get("variant_id")
        variant_records[variant_id] = record

    # Create a mapping from variant_id to its parent product
    variant_to_product = {}
    for product in products:
        for variant in product.get("variants", []):
            if variant["variant_id"] in variant_ids:
                variant_to_product[variant["variant_id"]] = product

    # Prepare bulk operations
    bulk_operations = []

    for variant_id, record in variant_records.items():
        price_list_id = record.get('price_list_id')
        if not price_list_id:
            logging.info(f"Price list ID missing for variant_id: {variant_id}")
            continue

        # Fetch price list details
        res, status_code = bc_price_list.fetch_price_list_by_id(store, price_list_id)
        if status_code != 200:
            logging.info(f"Failed to fetch price list {price_list_id}. Status Code: {status_code}")
            continue

        price_list_name = res.get("data", {}).get("name")
        if not price_list_name:
            logging.info(f"Price list name not found for price_list_id {price_list_id}.")
            continue
        
        # Fetch record details
        price = record.get("price", 0)
        date_modified = record.get("date_modified")

        product = variant_to_product.get(variant_id)
        if not product:
            logging.info(f"No product found with variant_id: {variant_id}")
            continue

        parent_product_id = product["parent_product_id"]
        parent_product_name = product.get("parent_product_name")
        parent_product_sku = product.get("parent_product_sku")
        product_ids.append(parent_product_id)

        variant = next((v for v in product["variants"] if v["variant_id"] == variant_id), None)
        if not variant:
            logging.info(f"Variant with ID {variant_id} not found in product {parent_product_id}.")
            continue

        variant_name = variant.get("variant_name")
        variant_sku = variant.get("variant_sku")

        existing_price_list = next((pl for pl in variant.get("price_list", []) if pl.get("price_list_id") == price_list_id), None)

        if existing_price_list:
            old_price = existing_price_list.get("price", 0)
            updated_price = price

            if old_price == updated_price:
                product_ids.append(parent_product_id)
                continue

            # Prepare update operation
            bulk_operations.append(UpdateOne(
                {
                    "parent_product_id": parent_product_id,
                    "variants.variant_id": variant_id,
                    "variants.price_list.price_list_id": price_list_id
                },
                {
                    "$set": {
                        "variants.$[variant].price_list.$[pl].name": price_list_name,
                        "variants.$[variant].price_list.$[pl].price": updated_price,
                        "updated_at": date_modified,
                        "updated_by": "BigCommerce"
                    }
                },
                array_filters=[
                    {"variant.variant_id": variant_id},
                    {"pl.price_list_id": price_list_id}
                ]
            ))

            price_logs[parent_product_id].append({
                "price_list_id": price_list_id,
                "variant_id": variant_id,
                "parent_product_name": parent_product_name,
                "parent_product_sku": parent_product_sku,
                "variant_name": variant_name,
                "variant_sku": variant_sku,
                "old_price": old_price,
                "new_price": updated_price,
                "updated_by": "BigCommerce"
            })
            product_ids.append(parent_product_id)
        else:
            old_price = None  # No existing price
            updated_price = price

            # Prepare update operation to add new price list entry
            bulk_operations.append(UpdateOne(
                {
                    "parent_product_id": parent_product_id,
                    "variants.variant_id": variant_id
                },
                {
                    "$push": {
                        "variants.$[variant].price_list": {
                            "price_list_id": price_list_id,
                            "name": price_list_name,
                            "price": updated_price
                        }
                    },
                    "$set": {
                        "updated_at": date_modified,
                        "updated_by": "BigCommerce"
                    }
                },
                array_filters=[
                    {"variant.variant_id": variant_id}
                ]
            ))

            price_logs[parent_product_id].append({
                "price_list_id": price_list_id,
                "variant_id": variant_id,
                "parent_product_name": parent_product_name,
                "parent_product_sku": parent_product_sku,
                "variant_name": variant_name,
                "variant_sku": variant_sku,
                "old_price": old_price,
                "new_price": updated_price,
                "updated_by": "BigCommerce"
            })
            product_ids.append(parent_product_id)
    
    old_data_map = {}
    if product_ids:
        product_ids = list(set(product_ids))
        old_data_map = _fetch_old_data_of_products(store_id, product_ids)

    # Execute bulk operations
    if bulk_operations:
        result = db["product_price_lists"].bulk_write(bulk_operations, ordered=False)
        logging.info(f"Bulk write result: {result.bulk_api_result}")

    if old_data_map != {}:
        logger.info(f"old_data_map_webhook: {old_data_map}")
        task.submit_task('check_promo_product_and_create_ticket', (store['id'], product_ids, old_data_map))

    # Log price changes
    # for parent_product_id, logs in price_logs.items():
    #     log_price_change(store_id, parent_product_id, logs)


def delete_multiple_price_lists(store_id, payload):
    store = mongo_db.get_store_by_id(store_id)
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    product_ids = []
    price_logs = defaultdict(list)

    # Group payload by price_list_id
    price_list_variants = defaultdict(list)
    for record in payload:
        price_list_id = record.get('price_list_id')
        variant_id = record.get('variant_id')
        if price_list_id and variant_id:
            price_list_variants[price_list_id].append(variant_id)

    bulk_operations = []

    for price_list_id, variant_ids in price_list_variants.items():
        # Fetch records for all variant_ids in the current price_list_id
        variant_ids_str = ','.join(map(str, variant_ids))
        records = bc_price_list.fetch_records_by_variant_id(store, price_list_id, variant_ids_str)
        existing_variant_ids = {record['variant_id'] for record in records}

        # Identify variant_ids that need deletion
        variant_ids_to_delete = set(variant_ids) - existing_variant_ids

        for variant_id in variant_ids_to_delete:
            product = db["product_price_lists"].find_one({"variants.variant_id": variant_id})
            if not product:
                logging.info(f"No product found with variant_id: {variant_id}")
                continue

            parent_product_id = product["parent_product_id"]
            parent_product_name = product.get("parent_product_name")
            parent_product_sku = product.get("parent_product_sku")

            variant = next((v for v in product["variants"] if v["variant_id"] == variant_id), None)
            if not variant:
                logging.info(f"Variant with ID {variant_id} not found in product.")
                continue

            variant_name = variant.get("variant_name")
            variant_sku = variant.get("variant_sku")

            existing_price_list = next((pl for pl in variant.get("price_list", []) if pl.get("price_list_id") == price_list_id), None)
            if existing_price_list:
                old_price = existing_price_list.get("price", 0)

                price_logs[parent_product_id].append({
                    "price_list_id": price_list_id,
                    "variant_id": variant_id,
                    "parent_product_name": parent_product_name,
                    "parent_product_sku": parent_product_sku,
                    "variant_name": variant_name,
                    "variant_sku": variant_sku,
                    "old_price": old_price,
                    "new_price": None,
                    "updated_by": "BigCommerce"
                })
                product_ids.append(parent_product_id)

                bulk_operations.append(UpdateOne(
                    {"parent_product_id": parent_product_id, "variants.variant_id": variant_id},
                    {"$pull": {"variants.$.price_list": {"price_list_id": price_list_id}}}
                ))

                bulk_operations.append(UpdateOne(
                    {"parent_product_id": parent_product_id},
                    {
                        "$set": {
                            "updated_at": datetime.now(timezone.utc).isoformat(timespec='seconds').replace('+00:00', 'Z'),
                            "updated_by": "BigCommerce"
                        }
                    }
                ))
    
    old_data_map = {}
    if product_ids:
        product_ids = list(set(product_ids))
        old_data_map = _fetch_old_data_of_products(store_id, product_ids)

    if bulk_operations:
        result = db["product_price_lists"].bulk_write(bulk_operations, ordered=False)
        logging.info(f"Bulk write result: {result.bulk_api_result}")

    # if product_ids and _should_call_task(store['id'], 'check_promo_product_and_create_ticket'):
    #     logger.info(f"Task triggered from delete_multiple_price_lists for store {store['id']}")
    #     task.submit_task('check_promo_product_and_create_ticket', (store['id'], product_ids))
    
    if old_data_map != {}:
        task.submit_task('check_promo_product_and_create_ticket', (store['id'], product_ids, old_data_map))

    # for parent_product_id, logs in price_logs.items():
    #     log_price_change(store_id, parent_product_id, logs)


def sync_google_sheet_price_list(store_id):
    try:
        # Fetch store and database details
        store = mongo_db.get_store_by_id(store_id)
        db = mongo_db.get_admin_db_client_for_store_id(store_id)
        conn = pg_db.get_connection(store['id'])

        price_list_products = list(db["product_price_lists"].find())
        static_price_list = list(db["static_price_lists"].find())

        # Fetch price lists from BigCommerce
        res, status_code = bc_price_list.fetch_price_lists(store)
        if status_code != 200:
            logging.error(f"Error fetching price lists: {status_code}")
            return

        # Extract active price lists
        active_price_lists = {
            price_list["id"]: price_list["name"]
            for price_list in res["data"]
            if price_list.get("active", False)
        }

        # Initialize dynamic_columns with active price lists only
        dynamic_columns = set(active_price_lists.values())

        # Add static price list names ensuring uniqueness
        for product in static_price_list:
            variants = product.get("variants", [])
            for variant in variants:
                price_list_entries = variant.get("price_list", [])
                for price_list_entry in price_list_entries:
                    price_list_name = price_list_entry.get("name")
                    if price_list_name:  # Add the name if it exists
                        dynamic_columns.add(price_list_name)

        # Ensure no extra price lists are added
        dynamic_columns = sorted(dynamic_columns)

        # Fetch all SKUs and their associated costs from skuvault_catalog

        sku_cost_map = {}
        
        query = text("SELECT sku, cost FROM skuvault_catalog")
        result = conn.execute(query)
        
        for row in result:     
            sku_cost_map[row[0]] = row[1]

        # Prepare the header row
        header = ["Product Name", "Variant Name", "SKU", "Cost", "Wholesale"] + dynamic_columns

        # Prepare data rows
        data = [header]

        # Traverse price_list_products to create rows
        for product in price_list_products:
            product_name = product.get("parent_product_name", "")
            product_sku = product.get("parent_product_sku", "")
            default_price = product.get("default_price", 0)

            for variant in product.get("variants", []):
                variant_sku = variant.get("variant_sku", "")
                variant_name = variant.get("variant_name", "")

                # Fetch cost for the variant SKU
                cost = sku_cost_map.get(variant_sku, None)

                row = [product_name, variant_name, variant_sku or product_sku, cost, default_price]

                # Map prices for dynamic columns
                prices = {col: "" for col in dynamic_columns}  # Initialize all as empty
                for price_entry in variant.get("price_list", []):
                    price_list_name = price_entry.get("name")
                    if price_list_name in dynamic_columns:  # Include only valid columns
                        prices[price_list_name] = price_entry.get("price", "")

                # Append prices in column order
                row += [prices[col] for col in dynamic_columns]
                data.append(row)

        # Update the sheet
        body = {
            'values': data
        }

        SERVICE_ACCOUNT_INFO = store['apps']['google']['service_account']
        SPREADSHEET_ID = store['apps']['google']['google_sheet']['sheet_id']
        RANGE_NAME = store['apps']['google']['google_sheet']['default_range']
        SCOPES = store['apps']['google']['google_sheet']['scopes']

        # Authenticate Google Sheets API using static credentials
        creds = Credentials.from_service_account_info(SERVICE_ACCOUNT_INFO, scopes=SCOPES)
        service = build('sheets', 'v4', credentials=creds)

        result = service.spreadsheets().values().update(
            spreadsheetId=SPREADSHEET_ID,
            range=RANGE_NAME,
            valueInputOption='RAW',
            body=body
        ).execute()

        logging.info(f"{result.get('updatedCells')} cells updated in Google Sheet.")

    except Exception as e:
        logging.error(f"Error syncing price lists with Google Sheet: {e}")
    finally:
        if conn:
            conn.close()

def update_price_list_using_rules(store_id, rule_id, is_normal_sync=False):
    try:
        conn = pg_db.get_connection(store_id)
        # Fetch store and database details
        # store = mongo_db.get_store_by_id(store_id)
        db = mongo_db.get_admin_db_client_for_store_id(store_id)
        store_db = mongo_db.get_store_db_client_for_store_id(store_id)

        if rule_id:
            rule_details = db['price_list_rules'].find_one({"_id": ObjectId(str(rule_id))})
            if rule_details:
                rule_details = mongo_db.process_data(rule_details)
                rule_name = rule_details['rule_name']
                distributor_id = rule_details['distributor_id']
                distributor_name = rule_details['distributor_name']
                category_ids = rule_details['categories']
                price_calculation  = rule_details['price_calculation']
                base_price_group = rule_details['price_group']
                markup_price = rule_details['markup_price']
                markup_price = float(rule_details['markup_price'])

                variant_ids = fetch_updated_variants_by_admin_app(store_id)

                if is_normal_sync:
                    pipeline = [
                        {
                            "$match": {
                                "categories": {"$in": category_ids}
                            }
                        },
                        {
                            "$addFields": {
                                "variants": {
                                    "$filter": {
                                        "input": "$variants",
                                        "as": "variant",
                                        "cond": {"$not": {"$in": ["$$variant.id", variant_ids]}}
                                    }
                                }
                            }
                        },
                        {
                            "$project": product_fields  # Keep only required fields
                        }
                    ]


                if category_ids and len(category_ids) > 0:
                    # products = store_db['products'].find(query, product_fields)
                    if is_normal_sync:
                        products = list(store_db['products'].aggregate(pipeline))
                    else:
                        products = store_db['products'].find({"categories": {"$in": category_ids}}, product_fields)
                    if products:
                        for product in products:
                            product_id = product['id']
                            product_variants = product.get('variants', [])  # This corresponds to products collection

                            if int(base_price_group) != 100056:
                                # Fetch the price list from the database for the current product
                                product_price_list = db['product_price_lists'].find_one({"parent_product_id": product_id})
                                if product_price_list:
                                    product_price_list = mongo_db.process_data(product_price_list)
                                    p_l_variants = product_price_list.get('variants', [])
                                    
                                    for variant in product_variants:
                                        variant_id = variant['id']
                                        # Check if the variant exists in the second array (variant_B)
                                        p_l_variant = next((v for v in p_l_variants if v['variant_id'] == variant_id), None)
                                        price_lists = p_l_variant.get('price_list', []) if p_l_variant else []
                                        
                                        existing_price = 0
                                        # Check and update the price list for the specific distributor
                                        if int(base_price_group) == 100057:
                                            existing_price = p_l_variant.get('variant_price', 0)
                                        else:
                                            existing_price_list = next((pl for pl in price_lists if pl['price_list_id'] == int(base_price_group)), None)
                                            existing_price = existing_price_list.get('price', 0) if existing_price_list else 0
                                        new_price = None
                                        if existing_price:
                                            # Update existing price list
                                            if price_calculation == 'percentage':
                                                if markup_price >= 0:
                                                    new_price = existing_price + (existing_price * markup_price / 100)
                                                else:
                                                    new_price = existing_price - (existing_price * abs(markup_price) / 100)
                                            elif price_calculation == 'absolute':
                                                if markup_price >= 0:
                                                    new_price = existing_price + markup_price
                                                else:
                                                    new_price = existing_price - abs(markup_price)
                                        # Add a new price list entry
                                        if new_price:
                                            new_price = max(0, new_price)
                                            new_price = round(new_price, 2)
                                            base_price_list = next((pl for pl in price_lists if pl['price_list_id'] == int(distributor_id)), None)
                                            if base_price_list:
                                                base_price_list['price'] = new_price
                                            else:
                                                new_price_list = {
                                                    "price_list_id": distributor_id,
                                                    "name": distributor_name,
                                                    "price": new_price
                                                }
                                                price_lists.append(new_price_list)
                                            
                                            upsert_product_variant_and_price_list(db, product_id, variant_id, distributor_id, distributor_name, new_price)
      
                                    # Ensure updated data is saved back to the database
                                    db['product_price_lists'].update_one({"parent_product_id": product_id}, {"$set": {"variants": p_l_variants, "updated_at": datetime.now(timezone.utc).isoformat(timespec='seconds').replace('+00:00', 'Z'),"updated_by": rule_name}}, upsert=True)
                                
                            elif int(base_price_group) == 100056:
                                    # Fetch the price list from the database for the current product
                                    product_price_list = db['product_price_lists'].find_one({"parent_product_id": product_id})
                                    if product_price_list:
                                        product_price_list = mongo_db.process_data(product_price_list)
                                        p_l_variants = product_price_list.get('variants', [])

                                        for variant in product_variants:
                                            variant_id = variant['id']
                                            variant_sku = variant['sku']
                                            # Check if the variant exists in the second array (variant_B)
                                            p_l_variant = next((v for v in p_l_variants if v['variant_id'] == variant_id), None)
                                            
                                            # Check and update the price list for the specific distributor
                                            price_lists = p_l_variant.get('price_list', []) if p_l_variant else []

                                            query = text("SELECT cost FROM skuvault_catalog WHERE sku = :sku")
                                            sku_cost = conn.execute(query, {"sku": variant_sku}).fetchone()
                                            if not sku_cost:
                                                logging.warning(f"No cost found for SKU {variant_sku}")
                                                continue
                                            cost = str(sku_cost[0]) if sku_cost else None
                                        
                                            new_price = None
                                            if cost:
                                                cost = float(cost)
                                                # Update existing price list
                                                if price_calculation == 'percentage':
                                                    if markup_price >= 0:
                                                        new_price = cost + (cost * markup_price / 100)
                                                    else:
                                                        new_price = cost - (cost * abs(markup_price) / 100)
                                                elif price_calculation == 'absolute':
                                                    if markup_price >= 0:
                                                        new_price = cost + markup_price
                                                    else:
                                                        new_price = cost - abs(markup_price)

                                            # Add a new price list entry
                                            if new_price:
                                                new_price = max(0, new_price)
                                                new_price = round(new_price, 2)
                                                base_price_list = next((pl for pl in price_lists if pl['price_list_id'] == int(distributor_id)), None)
                                                if base_price_list:
                                                    base_price_list['price'] = new_price
                                                else:
                                                    new_price_list = {
                                                        "price_list_id": distributor_id,
                                                        "name": distributor_name,
                                                        "price": new_price
                                                    }
                                                    price_lists.append(new_price_list)
                                                upsert_product_variant_and_price_list(db, product_id, variant_id, distributor_id, distributor_name, new_price)


                                        # Ensure updated data is saved back to the database
                                        db['product_price_lists'].update_one({"parent_product_id": product_id}, {"$set": {"variants": p_l_variants, "updated_at": datetime.now(timezone.utc).isoformat(timespec='seconds').replace('+00:00', 'Z'),"updated_by": rule_name}}, upsert=True)

            # If successful, update sync_status to "success"
            db['price_list_rules'].update_one({"_id": ObjectId(str(rule_id))}, {"$set": {"sync_status": "done", "error_message": None}})
            if not is_normal_sync:
                update_flag_in_static_price_lists(store_id, variant_ids)                
    except Exception as e:
        # logging.error(f"Error applying price lists rules: {e}")
        variant_id_log = locals().get('variant_id', 'unknown')
        logging.error(f"Error applying price lists rules for variant_id {variant_id_log}: {e}")
        db['price_list_rules'].update_one({"_id": ObjectId(str(rule_id))}, {"$set": {"sync_status": "failed", "error_message": f"Variant ID: {variant_id_log}, Error: {str(e)}"}})
    finally:
        if conn:
            conn.close()

def fetch_updated_variants_by_admin_app(store_id):
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    
    updated_variants = db["static_price_lists"].aggregate([
        {"$unwind": "$variants"},
        {"$match": {"variants.is_updated_admin": True}},
        {"$project": {"_id": 0, "variant_id": "$variants.variant_id"}}
    ])
    
    return [doc["variant_id"] for doc in updated_variants]

def update_flag_in_static_price_lists(store_id, variant_ids):
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    
    db["static_price_lists"].update_many(
        {"variants.variant_id": {"$in": variant_ids}},  # Match any variant_id in the list
        {"$set": {"variants.$[].is_updated_admin": False}}  # Update all matched variants in the array
    )


def upsert_product_variant_and_price_list(db, product_id, variant_id, distributor_id, distributor_name, new_price):
    # Check if the product exists
    product_result = db['static_price_lists'].find_one({"product_id": product_id})

    if product_result:
        # Check if the variant exists
        variant = next((v for v in product_result.get("variants", []) if v["variant_id"] == variant_id), None)

        if variant:
            # Check if the price_list with given price_list_id exists
            price_list = next((p for p in variant.get("price_list", []) if p["price_list_id"] == distributor_id), None)

            if price_list:
                # Update the price in the existing price_list
                db['static_price_lists'].update_one(
                    {
                        "product_id": product_id, "variants.variant_id": variant_id
                    },
                    {
                        "$set": {"variants.$.price_list.$[p].price": new_price}
                    },
                    array_filters=[{"p.price_list_id": distributor_id}]
                )
            else:
                # Add a new price_list entry
                db['static_price_lists'].update_one(
                    {
                        "product_id": product_id,
                        "variants.variant_id": variant_id
                    },
                    {
                        "$push": {
                            "variants.$.price_list": {
                                "price_list_id": distributor_id,
                                "name": distributor_name,
                                "price": new_price
                            }
                        }
                    }
                )
        else:
            # Add a new variant with the price_list
            db['static_price_lists'].update_one(
                {"product_id": product_id},
                {
                    "$push": {
                        "variants": {
                            "variant_id": variant_id,
                            "price_list": [
                                {
                                    "price_list_id": distributor_id,
                                    "name": distributor_name,
                                    "price": new_price
                                }
                            ]
                        }
                    }
                }
            )
    else:
        # If the product doesn't exist, create a new document with the product, variant, and price_list
        db['static_price_lists'].insert_one(
            {
                "product_id": product_id,
                "variants": [
                    {
                        "variant_id": variant_id,
                        "price_list": [
                            {
                                "price_list_id": distributor_id,
                                "name": distributor_name,
                                "price": new_price
                            }
                        ]
                    }
                ]
            }
        )


def fetch_static_price_lists(store_id):
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    # Desired static price list order
    custom_sort_order = [53, 52, 51, 100054, 100055]

    static_price_lists = []
    try:
        price_list_distributors = db["price_list_distributors"]
        # Use MongoDB's `$expr` to sort by the custom order
        pipeline = [
            {"$match": {"status": "active"}},
            {"$addFields": {
                "sort_index": {
                    "$indexOfArray": [custom_sort_order, "$id"]
                }
            }},
            {"$sort": {"sort_index": 1}},
            {"$project": {"id": 1, "title": 1, "_id": 0}}
        ]
        cursor = price_list_distributors.aggregate(pipeline)
        static_price_lists = [{"id": doc["id"], "name": doc["title"], "active": True} for doc in cursor]
    except Exception as e:
        print(f"Error fetching price list distributors: {e}")

    return static_price_lists

def log_price_change(store_id, product_id, price_logs):
    if not price_logs:
        return  # No logs to process, exit early

    conn = pg_db.get_connection(store_id)
    try:
        # Mark all previous logs as inactive for this product_id
        update_query = text("""
            UPDATE pricelist_change_logs
            SET is_active = FALSE
            WHERE product_id = :product_id
        """)
        conn.execute(update_query, {"product_id": product_id})

        # Prepare data for bulk insert
        insert_data = []
        for log in price_logs:
            insert_data.append({
                "price_list_id": log["price_list_id"],
                "product_id": product_id,
                "variant_id": log["variant_id"],
                "parent_product_name": log["parent_product_name"],
                "parent_product_sku": log["parent_product_sku"],
                "variant_name": log["variant_name"],
                "variant_sku": log["variant_sku"],
                "old_price": log["old_price"],
                "updated_price": log["new_price"],
                "is_active": True,
                "updated_by": log["updated_by"],
                "updated_at": datetime.now(timezone.utc).isoformat(timespec='seconds').replace('+00:00', 'Z')
            })

        # Bulk insert new logs
        insert_query = text("""
            INSERT INTO pricelist_change_logs (
                price_list_id, product_id, variant_id, parent_product_name, parent_product_sku,
                variant_name, variant_sku, old_price, updated_price, is_active, updated_by, updated_at
            ) VALUES (
                :price_list_id, :product_id, :variant_id, :parent_product_name, :parent_product_sku,
                :variant_name, :variant_sku, :old_price, :updated_price, :is_active, :updated_by, :updated_at
            )
        """)
        conn.execute(insert_query, insert_data)
        conn.commit()

    except Exception as e:
        logging.error(f"An error occurred while logging price changes: {e}")
        conn.rollback()
        raise e
    finally:
        conn.close()


def update_price_list_from_csv(store_id, csv_data, username, filename, is_from_multi_thred=False):
    store = mongo_db.get_store_by_id(store_id)
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    product_ids = []

    # Fetch price lists
    price_lists, _ = bc_price_list.fetch_price_lists(store)
    static_price_lists = fetch_static_price_lists(store_id)

    # Extract active price list mappings
    active_price_list_map = {pl['name']: pl['id'] for pl in price_lists['data'] if pl['active']}
    active_static_price_list_map = {pl['name']: pl['id'] for pl in static_price_lists if pl['active']}
    price_list_mapping = {**active_price_list_map, **active_static_price_list_map}

    # Collect all SKUs from CSV
    parent_skus = [row['SKU'] for row in csv_data]

    # Fetch product price list entries in a single query
    product_price_list_entries = list(db["product_price_lists"].find({"$or": [
        {"parent_product_sku": {"$in": parent_skus}},  # Fetch by parent SKU
        {"variants.variant_sku": {"$in": parent_skus}}  # Fetch by variant SKU
    ]}))

    # Create mappings for quick lookup
    sku_to_variant_id = {}  # Maps SKU -> variant_id
    parent_to_variants_map = {}  # Maps parent SKU -> list of variant_ids
    sku_to_product_id = {}  # Maps SKU (both parent and variant) -> product_id

    for entry in product_price_list_entries:
        parent_sku = entry["parent_product_sku"]
        parent_product_id = entry.get("parent_product_id")  # Fetch parent_product_id

        sku_to_product_id[parent_sku] = parent_product_id  # Map parent SKU to product ID
        parent_to_variants_map[parent_sku] = []

        for variant in entry.get("variants", []):
            variant_sku = variant["variant_sku"]
            variant_id = variant["variant_id"]

            sku_to_variant_id[variant_sku] = variant_id
            parent_to_variants_map[parent_sku].append(variant_id)

            # Ensure only correct parent product ID is assigned to variants
            if variant_sku not in sku_to_product_id:
                sku_to_product_id[variant_sku] = parent_product_id  

    # print("sku_to_product_id", sku_to_product_id)
    
    product_price_updates = {}  # Store product_id-specific updates
    # print("sku_to_product_id", sku_to_product_id)
    for row in csv_data:
        entered_sku = row['SKU']
        variant_id = sku_to_variant_id.get(entered_sku, None)
        variant_ids = parent_to_variants_map.get(entered_sku, [])
        product_id = sku_to_product_id.get(entered_sku)  # This will now return correct product_id
        product_ids.append(product_id)
        # print('product_id', product_id)

        # **Reset for each row to prevent data mixing**
        payload_data = []
        default_price_data = []

        for price_list_name, price in row.items():
            if price_list_name not in ("SKU", "Wholesale") and price.strip():
                price_list_id = price_list_mapping.get(price_list_name)
                if price_list_id:
                    if variant_id:
                        # SKU from CSV is a variant SKU
                        payload_data.append({
                            "price": price,
                            "price_list_id": price_list_id,
                            "variant_id": variant_id
                        })
                    else:
                        # SKU from CSV is a parent SKU, apply price to all variants
                        for var_id in variant_ids:
                            payload_data.append({
                                "price": price,
                                "price_list_id": price_list_id,
                                "variant_id": var_id
                            })

        # Handle Wholesale price
        wholesale_price = row.get("Wholesale", "").strip()
        if wholesale_price:
            if variant_id:
                default_price_data.append({
                    "variant_id": variant_id,
                    "price": wholesale_price
                })
            elif variant_ids:
                for var_id in variant_ids:
                    default_price_data.append({
                        "variant_id": var_id,
                        "price": wholesale_price
                    })
                # Add the product-level default price update if it's a parent SKU
                default_price_data.append({
                    "variant_id": None,
                    "price": wholesale_price
                })

        if product_id:
            if product_id not in product_price_updates:
                product_price_updates[product_id] = {
                    "default_price": [],
                    "data": []
                }

        product_price_updates[product_id]["default_price"].extend(default_price_data)
        product_price_updates[product_id]["data"].extend(payload_data)

    # Track errors
    errors_found = False
    error_messages = []

    old_data_map = {}
    if product_ids:
        product_ids = list(set(product_ids))
        old_data_map = _fetch_old_data_of_products(store_id, product_ids)
        logger.info(f"old_data_map_from_csv: {old_data_map}")

    for product_id, price_update in product_price_updates.items():
        errors = update_product_price_list(store, product_id, price_update, username, old_data_map)
        
        if len(errors) > 0:  # If errors exist, collect them
            errors_found = True
            error_messages.extend(errors)

    errors_text = "\n".join(error_messages) if errors_found else ""

    if not is_from_multi_thred:
        db["user_preference"].update_one({"type": "price_list_import"}, {"$set": {"status": "completed"}}, upsert=True)

        # Call the email function with necessary details
        email_util.send_price_list_import_email(
            store_id=store_id,
            errors=errors_text,
            recipient_email=username,
            user_name=username,
            filename=filename,
            created_date_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )
    else:
        return errors_text

    # logger.info(f"Call from update_price_list_from_csv")
    # if old_data_map != {}:
    #     task.submit_task('check_promo_product_and_create_ticket', (store['id'], product_ids, old_data_map))





def update_product_price_list(store, product_id, req_body, username, old_data_map):
    db = mongo_db.get_admin_db_client_for_store_id(store['id'])

    # Prepare batch updates & logs
    updates, price_logs, bc_payload_map = [], [], {}
    errors = []

    #  Extract default_price payload
    default_price_payload = req_body.get("default_price", [])

    # Update default prices using the helper function
    if default_price_payload:
        try:
            default_price_logs = update_default_price(store, product_id, default_price_payload, username)
            if default_price_logs:
                price_logs.extend(default_price_logs)
        except ValueError as e:
            return {"message": str(e), "status": 404}

    # Fetch necessary data from MongoDB in **one query** for efficiency
    product = db["product_price_lists"].find_one({"parent_product_id": int(product_id)})
    static_price_list_doc = db['static_price_lists'].find_one({"product_id": int(product_id)})

    
    # Fetch all available price lists from BigCommerce
    price_lists, _ = bc_price_list.fetch_price_lists(store)

    # Fetch static price lists from MongoDB
    static_price_lists = list(db["price_list_distributors"].find({"status": "active"}, {"id": 1, "title": 1, "_id": 0, "derived": 1}))
    static_price_lists = [{"id": doc["id"], "name": doc["title"], "active": True, "derived": doc["derived"]} for doc in static_price_lists]

    # static_price_list_ids = {pl["id"] for pl in static_price_lists}

    # Extract price_list_ids that have derived = "true"
    derived_price_list_ids = {doc["id"] for doc in static_price_lists if doc.get("derived") == "true"}


    # Combine static_price_lists into price_lists
    for static_price_list in static_price_lists:
        price_lists['data'].append({
            'id': static_price_list['id'],
            'name': static_price_list['name'],
            'date_created': None,  # Placeholder
            'date_modified': None,  # Placeholder
            'active': static_price_list['active']
        })

    price_list_meta = {pl['id']: pl['name'] for pl in price_lists['data']}

    parent_product_name = product.get('parent_product_name')
    parent_product_sku = product.get('parent_product_sku')

    # Dictionary to map variant_id to variant data
    variant_map = {variant['variant_id']: variant for variant in product.get('variants', [])}

    # Iterate over each entry in the request body
    for entry in req_body['data']:
        variant_id = entry.get('variant_id')
        price_list_id = entry.get('price_list_id')
        new_price = entry.get('price')

        variant = variant_map.get(variant_id)

        # Extract additional variant details
        variant_name = variant.get('variant_name')
        variant_sku = variant.get('variant_sku')

        # Fetch the old price for logging
        old_price_entry = next((pl for pl in variant.get('price_list', []) if pl['price_list_id'] == price_list_id), None)
        old_price = old_price_entry['price'] if old_price_entry else None

        if price_list_id in [pl['id'] for pl in static_price_lists]:
            if not static_price_list_doc:
                # If the product does not exist in static_price_lists, create it
                static_price_list_doc = {
                    "product_id": int(product_id),
                    "variants": []
                }

            # Find or create the variant entry in static_price_lists
            static_variant = next((v for v in static_price_list_doc['variants'] if v['variant_id'] == variant_id), None)
            if not static_variant:
                static_variant = {"variant_id": variant_id, "price_list": []}
                static_price_list_doc['variants'].append(static_variant)

            # Update or add the price_list entry
            price_list_found = False
            for price_list in static_variant['price_list']:
                if price_list['price_list_id'] == price_list_id:
                    price_list['price'] = float(new_price)
                    price_list_found = True
                    break

            if not price_list_found:
                static_variant['price_list'].append({
                    "price_list_id": price_list_id,
                    "name": price_list_meta[price_list_id],
                    "price": float(new_price)
                })
            
            # If the price_list_id is in the derived list, set the flag
            if price_list_id in derived_price_list_ids:
                static_variant["is_updated_admin"] = True

            # Update product_price_lists as well
            variant['price_list'] = [
                pl for pl in variant.get('price_list', []) if pl['price_list_id'] != price_list_id
            ]
            variant['price_list'].append({
                "price_list_id": price_list_id,
                "name": price_list_meta[price_list_id],
                "price": float(new_price)
            })

            updates.append(UpdateOne(
                {
                    "parent_product_id": int(product_id),
                    "variants.variant_id": variant_id
                },
                {
                    "$set": {
                    "variants.$[v].price_list": variant['price_list'],
                    "updated_at": datetime.utcnow().replace(tzinfo=timezone.utc).isoformat(timespec='seconds').replace('+00:00', 'Z'),
                    "updated_by": username
                    }
                },
                array_filters=[{"v.variant_id": variant_id}]
            ))
        else:
            # Handle non-static price list updates (for BigCommerce and MongoDB)
            try:
                new_price = float(new_price)
            except ValueError:
                return {"message": f"Invalid price value {new_price} for variant {variant_id}, price_list {price_list_id}", "status": 400}

            price_list_found = False
            for price_list in variant.get('price_list', []):
                if price_list['price_list_id'] == price_list_id:
                    price_list['price'] = new_price
                    price_list_found = True
                    break

            if not price_list_found:
                variant['price_list'].append({
                    "price_list_id": price_list_id,
                    "name": price_list_meta.get(price_list_id, "Unknown Price List"),
                    "price": new_price
                })

            updates.append(UpdateOne(
                {
                    "parent_product_id": int(product_id),
                    "variants.variant_id": variant_id
                },
                {
                    "$set": {
                        "variants.$[v].price_list": variant['price_list'],
                        "updated_at": datetime.utcnow().replace(tzinfo=timezone.utc).isoformat(timespec='seconds').replace('+00:00', 'Z'),
                        "updated_by": username
                    }
                },
                array_filters=[{"v.variant_id": variant_id}]
            ))

            # Collect BigCommerce payload
            bc_entry = {
                "variant_id": variant_id,
                "currency": "usd",
                "price": new_price
            }

            if price_list_id not in bc_payload_map:
                bc_payload_map[price_list_id] = []
            bc_payload_map[price_list_id].append(bc_entry)

        new_price = None if new_price == "" else new_price
        if old_price is not None:
            old_price = float(old_price)
        if new_price is not None:
            new_price = float(new_price)
        
        if old_price is not None or new_price is not None:
            if old_price == new_price:
                continue
            price_logs.append({
                "price_list_id": price_list_id,
                "variant_id": variant_id,
                "parent_product_name": parent_product_name,
                "parent_product_sku": parent_product_sku,
                "variant_name": variant_name,
                "variant_sku": variant_sku,
                "old_price": old_price,
                "new_price": new_price,
                "updated_by": username
            })
        
    # Perform all updates in bulk for MongoDB
    if updates:
        db["product_price_lists"].bulk_write(updates)

    # Upsert the static_price_lists document
    if static_price_list_doc:
        db['static_price_lists'].update_one(
            {"product_id": int(product_id)},
            {"$set": static_price_list_doc},
            upsert=True
        )
    logger.info(f"Call from update_product_price_list")
    if old_data_map != {}:
        task.submit_task('check_promo_product_and_create_ticket', (store['id'], [product_id], old_data_map))
    # task.submit_task('update_price_list_logs', (store['id'], product_id, price_logs))

    # Call BigCommerce API for each price_list_id with the collected payload
    for price_list_id, bigcommerce_payload in bc_payload_map.items():
        bc_response, status_code = bc_price_list.update_price_list(store, price_list_id, bigcommerce_payload)
        if status_code != 200:
            error = {"message": f"BigCommerce update failed for price_list_id {price_list_id}", "bc_response": bc_response, "status": status_code}
            errors.append(error)
            return error
        
    return errors


# Function to split list into batches of max_size
def batch(iterable, max_size):
    """
    Splits the iterable into batches ensuring no duplicate variant_id appears in the same batch.
    """
    batches = []
    batch_set = set()  # To track variant_ids in the current batch
    current_batch = []

    for item in iterable:
        variant_id = item.get("id")  # Extract variant_id
        
        # If variant_id is already in the current batch, start a new batch
        if variant_id in batch_set or len(current_batch) >= max_size:
            batches.append(current_batch)
            current_batch = [item]  # Start a new batch
            batch_set = {variant_id}  # Reset batch set
        else:
            current_batch.append(item)
            batch_set.add(variant_id)

    # Append the last batch if it's not empty
    if current_batch:
        batches.append(current_batch)

    return batches

def update_default_price(store, product_id, default_price_payload, username):
    redis_client = redis_util.get_redis_client(store['id'])
    db = mongo_db.get_admin_db_client_for_store_id(store['id'])
    updates = []
    bc_payload = []  # Payload for BigCommerce
    default_price_updated = False
    price_logs = []
    bc_variants = []
    
    # Fetch the product document
    product = db["product_price_lists"].find_one({"parent_product_id": int(product_id)})

    parent_product_sku = product.get("parent_product_sku")
    parent_product_name = product.get("parent_product_name", "Unknown")
   
    # Fetch variant data from BigCommerce
    bc_variants = bc_products.fetch_bc_product_variants(store, product_id)
    bc_variants_map = {variant["id"]: variant for variant in bc_variants} if bc_variants else {}

    # Create a dictionary for faster variant lookup (O(1) time complexity)
    variant_map = {variant["variant_id"]: variant for variant in product.get("variants", [])}

    # Iterate through the default price list
    for entry in default_price_payload:
        variant_id = entry.get("variant_id")
        new_price = entry.get("price")
        new_price = float(new_price) if new_price != "" else 0
        old_price = None
        variant_name = None
        variant_sku = None
        
        if variant_id is None:
            parent_product_price_logs = update_parent_product_default_price(store, product, product_id, new_price, username, updates)
            if parent_product_price_logs:
                price_logs.extend(parent_product_price_logs)
            continue

        # Update variant-level default price
        variant = variant_map.get(variant_id)
        if variant:
            old_price = variant.get("variant_price")
            variant["variant_price"] = new_price

            # Check if variant_sku matches parent_product_sku for product-level update
            if variant.get("variant_sku") == parent_product_sku:
                product["default_price"] = new_price
                default_price_updated = True

            bc_variant = bc_variants_map.get(variant_id, {})
            sale_price = bc_variant.get("sale_price", 0)
            
            # Prepare payload for BigCommerce based on sale_price
            if sale_price not in (0, None):
                bc_payload.append({
                    "id": variant_id,  # BigCommerce variant ID
                    "sale_price": new_price
                })
            else:
                bc_payload.append({
                    "id": variant_id,  # BigCommerce variant ID
                    "price": new_price
                })

        if old_price is not None:
            old_price = float(old_price)
        if new_price is not None:
            new_price = float(new_price)

        if old_price is not None or new_price is not None:
            if old_price == new_price:
                continue
            price_logs.append({
                "price_list_id": None,
                "variant_id": variant_id,
                "parent_product_name": parent_product_name,
                "parent_product_sku": parent_product_sku,
                "variant_name": variant_name,
                "variant_sku": variant_sku,
                "old_price": old_price,
                "new_price": new_price,
                "updated_by": username
            })
        
        # Prepare MongoDB update operation for the variant
        updates.append(UpdateOne(
            {
                "parent_product_id": int(product_id),
                "variants.variant_id": variant_id
            },
            {
                "$set": {
                    "variants.$[v].variant_price": new_price,
                    "updated_at": datetime.utcnow().replace(tzinfo=timezone.utc).isoformat(timespec='seconds').replace('+00:00', 'Z'),
                    "updated_by": username
                }
            },
            array_filters=[{"v.variant_id": variant_id}]
        ))

    # Add product-level default price update if applicable
    if default_price_updated:
        updates.append(UpdateOne(
            {"parent_product_id": int(product_id)},
            {"$set": {
                "default_price": product["default_price"],
                "updated_at": datetime.utcnow().replace(tzinfo=timezone.utc).isoformat(timespec='seconds').replace('+00:00', 'Z'),
                "updated_by": username
            }}
        ))

    # Perform bulk updates in MongoDB
    if updates:
        db["product_price_lists"].bulk_write(updates)
    
    redis_key = f"admin_update:{store['id']}:{product_id}"
    redis_client.set(redis_key, "1", ex=7)  # Expires in 7 seconds
    
    if bc_payload:
        for batch_payload in batch(bc_payload, 49):  # Split into batches of 49
            res, status_code = bc_product.update_bc_product_variants(store, batch_payload)
            if status_code != 200:
                error = {"message": f"Failed to update BigCommerce variants. Status Code: {status_code}, Response: {res}"}
                logger.error(error)

    return price_logs


def update_parent_product_default_price(store, product, product_id, new_price, username, updates):
    """
    Handle cases where variant_id is null and update parent product's default price.
    """
    redis_client = redis_util.get_redis_client(store['id'])
    price_logs = []
    bc_product_data = bc_products.fetch_bc_product(store, product_id)
    if not bc_product_data:
        error = {"message": f"Failed to fetch BigCommerce product data for product_id {product_id}"}
        logger.error(error)
        return

    sale_price = bc_product_data.get("data", {}).get("sale_price")

    if sale_price not in (0, None):
        bc_payload = {"sale_price": new_price}
    else:
        bc_payload = {"price": new_price}

    old_price = product.get("default_price")
    product["default_price"] = new_price
    updates.append(UpdateOne(
        {"parent_product_id": int(product_id)},
        {"$set": {
            "default_price": new_price,
            "updated_at": datetime.utcnow().replace(tzinfo=timezone.utc).isoformat(timespec='seconds').replace('+00:00', 'Z'),
            "updated_by": username
        }}
    ))
    parent_product_name = product.get("parent_product_name", "Unknown")
    parent_product_sku = product.get("parent_product_sku")

    redis_key = f"admin_update:{store['id']}:{product_id}"
    redis_client.set(redis_key, "1", ex=7)  # Expires in 7 seconds

    res, status_code = bc_products.update_bc_product(store, bc_payload, product_id)
    if status_code != 200:
        error = {"message": f"Failed to update BigCommerce product price. Status Code: {status_code}, Response: {res}"}
        logger.error(error)
        return

    if old_price is not None:
        old_price = float(old_price)
    if new_price is not None:
        new_price = float(new_price)

    if old_price is not None or new_price is not None:
        if old_price == new_price:
            return price_logs
        price_logs.append({
            "price_list_id": None,
            "variant_id": None,
            "parent_product_name": parent_product_name,
            "parent_product_sku": parent_product_sku,
            "variant_name": None,
            "variant_sku": None,
            "old_price": old_price,
            "new_price": new_price,
            "updated_by": username
        })

    return price_logs


def price_list_csv_export(store_id, query_params, username):
    store = mongo_db.get_store_by_id(store_id)
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    storefront_db = mongo_db.get_store_db_client_for_store_id(store_id)
    conn = pg_db.get_connection(store_id)
    try:
        tag_filter = query_params.get("tag_filter")
        classification_filter = query_params.get("classification_filter")
        classified_as_filter = query_params.get("classified_as_filter")
        products_filter = query_params.get("products_filter")
        user_filter = query_params.get("user_filter")
        supplier_filter = query_params.get("supplier_filter")

        # Fetch price lists from BigCommerce
        static_price_lists = fetch_static_price_lists(store_id)
        price_lists, _ = bc_price_list.fetch_price_lists(store)

        # Merge static price lists
        price_lists['data'].extend([{
            'id': pl['id'], 'name': pl['name'], 'date_created': None, 'date_modified': None, 'active': pl['active']
        } for pl in static_price_lists])

        # Move price_list_id 7 to the top if active
        sorted_price_lists = sorted(price_lists['data'], key=lambda x: (x['id'] != 7, not x['active']))
        
        # Generate price list mappings
        price_list_meta = {
            f"price_{i+1}": {'name': pl['name'], 'active': pl['active']}
            for i, pl in enumerate(sorted_price_lists, start=0) if pl['active']
        }
        price_list_map = {pl['id']: f"price_{i+1}" for i, pl in enumerate(sorted_price_lists, start=0) if pl['active']}
        reversed_price_list_map = {v: k for k, v in price_list_map.items()}


        variant_sku_list = []
        filter_query = []

        def fetch_skus(query, param_name, values):
            result = conn.execute(text(query), {param_name: tuple(values)}).fetchall()
            return [row[0] for row in result]

        if tag_filter:
            tag_skus = fetch_skus("SELECT sku FROM product_tags WHERE tag_id IN :tag_filter_list", 'tag_filter_list', tag_filter.split(','))
            tag_query = {"parent_product_sku": {"$in": tag_skus}}
            filter_query.append(tag_query)
        
        if tag_filter:
            tag_skus = fetch_skus("SELECT variant_sku FROM product_tags WHERE tag_id IN :tag_filter_list", 'tag_filter_list', tag_filter.split(','))
            variant_sku_list = [sku for sku in tag_skus if sku is not None]

        if classified_as_filter:
            classified_skus = fetch_skus("SELECT parent_sku FROM replenishment_dashboard WHERE classified_as_id IN :classified_as_filter_list", 'classified_as_filter_list', classified_as_filter.split(','))
            classified_as_query = {"parent_product_sku": {"$in": classified_skus}}
            filter_query.append(classified_as_query)

        if classification_filter:
            classification_skus = fetch_skus("SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE classification IN :classification_filter_list AND parent_sku IS NOT NULL AND parent_sku != ''", 'classification_filter_list', classification_filter.split(','))
            classification_query = {"parent_product_sku": {"$in": classification_skus}}
            filter_query.append(classification_query)

        if user_filter:
            query = """
                SELECT ARRAY_AGG(DISTINCT suppliers) 
                FROM user_supplier_mapping 
                WHERE user_name = :user_name
            """
            res = conn.execute(text(query), {'user_name': user_filter})
            suppliers_list = res.fetchone()[0] or []

            query = "SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE primary_supplier = ANY(:suppliers_list) AND parent_sku IS NOT NULL AND parent_sku != ''"
            result = conn.execute(text(query), {'suppliers_list': suppliers_list}).fetchall()
            user_skus = []
            for row in result:
                user_skus.append(row[0])
            user_query = {"parent_product_sku": {"$in": user_skus}}
            filter_query.append(user_query)

        if supplier_filter:
            supplier_filter_list = [supplier.strip() for supplier in supplier_filter.split(';')]
            query = "SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE primary_supplier ILIKE ANY(:supplier_filter_list) AND parent_sku IS NOT NULL AND parent_sku != ''"
            result = conn.execute(text(query), {'supplier_filter_list': supplier_filter_list}).fetchall()
            supplier_skus = [row[0] for row in result]
            supplier_query = {"parent_product_sku": {"$in": supplier_skus}}
            filter_query.append(supplier_query)

        if products_filter:
            filter_query.append({"parent_product_id": {"$in": list(map(int, products_filter.split(',')))}})

        additional_query = {"$and": [{"syncing": False}] + filter_query} if filter_query else {"syncing": False}

        product_price_list_fields = {
            "parent_product_id": 1,
            "parent_product_name": 1,
            "parent_product_sku": 1,
            "created_at": 1,
            "updated_at": 1,
            "updated_by": 1,
            "default_price": 1,
            "variants": 1,
            "price_update_status": 1
        }
        products, total_data_length = common_util.get_records_price_list(
            db, "product_price_lists", {}, product_price_list_fields, additional_query
        )
        # Extract variant SKUs from product variants
        all_variant_skus = [variant['variant_sku'] for product in products for variant in product['variants']]
        all_parent_skus = [product['parent_product_sku'] for product in products]

        all_parent_product_ids = list({int(product['parent_product_id']) for product in products if 'parent_product_id' in product})

        # Fetching cost, inventory level and monthly data at product level
        month_names, day_difference = common_util.get_month_array_for_meta(6)

        if all_parent_product_ids != []:
            product_inventory_docs_cursor = storefront_db["products"].find(
            {"id": {"$in": all_parent_product_ids}},
                {"id": 1, "inventory_level": 1}
            )
            product_inventory_docs = list(product_inventory_docs_cursor)

            # Create a mapping: parent_product_id -> inventory_level
            product_inventory_map = {doc['id']: doc.get('inventory_level', 0) for doc in product_inventory_docs}

            query = text(f"""
                SELECT product_id, month_1, month_2, month_3, month_4, month_5, month_6, month_7
                FROM {analytics_db.AnalyticsDB.get_replenishment_products_table()}
                WHERE product_id = ANY(:product_ids)
            """)
            result = conn.execute(query, {"product_ids": all_parent_product_ids}).fetchall()

            # Build mapping: { product_id: {month_1: X, ..., month_7: Y} }
            product_monthly_data_map = {
                row[0]: {
                    f"month_{i}": int(row[i]) if row[i] is not None else None
                    for i in range(1, 8)
                } for row in result
            }
        else:
            product_monthly_data_map = {}

        # Fetching cost, inventory level and monthly data at variant level
        sku_inventory_map = {}
        if all_variant_skus != []:
            # Query skuvault_catalog for costs of these specific variant SKUs
            query = text("SELECT sku, cost FROM skuvault_catalog WHERE sku IN :variant_skus")
            result = conn.execute(query, {'variant_skus': tuple(all_variant_skus)})
            sku_cost_data = result.fetchall()

            # Build inventory map
            cursor = storefront_db["products"].find(
                {
                    "variants.sku": {"$in": all_variant_skus}
                },
                {
                    "variants.sku": 1,
                    "variants.inventory_level": 1
                }
            )

            for product_doc in cursor:
                for variant in product_doc.get("variants", []):
                    sku = variant.get("sku")
                    inventory_level = variant.get("inventory_level")
                    if sku in all_variant_skus:
                        sku_inventory_map[sku] = inventory_level

            # ✅ NEW: Fetch month_1 to month_7 from analytics.replenishment_variants in PG
            query = text(f"""
                SELECT sku, month_1, month_2, month_3, month_4, month_5, month_6, month_7
                FROM {analytics_db.AnalyticsDB.get_replenishment_variants_table()}
                WHERE sku = ANY(:skus)
            """)
            result = conn.execute(query, {"skus": all_variant_skus}).fetchall()

            # Build mapping: { product_id: {month_1: X, ..., month_7: Y} }
            variant_monthly_data_map = {
                row[0]: {
                    f"month_{i}": int(row[i]) if row[i] is not None else None
                    for i in range(1, 8)
                } for row in result
            }
        else:
            sku_cost_data = []
            variant_monthly_data_map = {}

        # Create a mapping of SKU to cost
        sku_cost_map = {row[0]: row[1] for row in sku_cost_data}


        # Fetch classification for parent_product_sku
        if all_parent_skus != []:
            classification_query = text("""SELECT DISTINCT classification, parent_sku FROM skuvault_catalog WHERE parent_sku IN :parent_product_skus""")
            classification_results = conn.execute(classification_query, {'parent_product_skus': tuple(all_parent_skus)}).fetchall()

            classification_map = {}
            for row in classification_results:
                if row[1] in classification_map:
                    classification_map[row[1]].append(row[0])
                else:
                    classification_map[row[1]] = [row[0]]

            # Convert lists to comma-separated strings
            classification_map = {sku: ", ".join(classifications) for sku, classifications in classification_map.items()}
        else:
            classification_map = {}

        # Fetch user data in bulk
        updated_by_users = {product.get('updated_by') for product in products if product.get('updated_by')}
        user_data_map = store_db.fetch_users_by_usernames(store_id, updated_by_users) if updated_by_users else {}

        parent_product_ids = [product['parent_product_id'] for product in products if product.get('parent_product_id')]
        price_list_update_map = {}

        if parent_product_ids:
            query = text("""
                SELECT product_id, updated_at, updated_by 
                FROM pricelist_change_logs 
                WHERE product_id IN :parent_product_ids AND is_active = TRUE
            """)
            result = conn.execute(query, {'parent_product_ids': tuple(parent_product_ids)}).fetchall()
            
            price_list_update_map = {
                row[0]: {'updated_at': row[1], 'updated_by': row[2]} for row in result
            }

        
        # Loop over each product and calculate the price at the product level
        for product in products:
            # Check price_list_update_data collection for updated fields
            parent_product_id = product.get('parent_product_id')
            parent_product_sku = product.get('parent_product_sku', None)
            product_updated_at = product.get('updated_at')
            product['updated_at'] = product.get('updated_at')
            product['inventory_level'] = product_inventory_map.get(parent_product_id, None)
            product['month_1'] = product_monthly_data_map.get(parent_product_id, {}).get('month_1', None)
            product['month_2'] = product_monthly_data_map.get(parent_product_id, {}).get('month_2', None)
            product['month_3'] = product_monthly_data_map.get(parent_product_id, {}).get('month_3', None)
            product['month_4'] = product_monthly_data_map.get(parent_product_id, {}).get('month_4', None)
            product['month_5'] = product_monthly_data_map.get(parent_product_id, {}).get('month_5', None)
            product['month_6'] = product_monthly_data_map.get(parent_product_id, {}).get('month_6', None)
            product['month_7'] = product_monthly_data_map.get(parent_product_id, {}).get('month_7', None)

            # Calculate total_sum of months
            monthly_data = product_monthly_data_map.get(parent_product_id, {})
            total_sum = sum([
                int(monthly_data.get(f'month_{i}', 0) or 0) for i in range(2, 8)
            ])

            # Safely handle inventory_level
            inventory_level = product.get("inventory_level") or 0
            try:
                inventory_level = int(inventory_level)
            except (ValueError, TypeError):
                inventory_level = 0
            inventory_level = max(inventory_level, 1)  # prevent divide-by-zero

            turn_rate = ((int(total_sum) / inventory_level) * 365) / day_difference
            weeks_on_hand = 52 / turn_rate if turn_rate != 0 else 1

            product['turn_rate'] = round(turn_rate, 2)
            product['weeks_on_hand'] = round(weeks_on_hand, 2)

            # Apply batch-fetched price list updates
            if parent_product_id in price_list_update_map:
                update_data = price_list_update_map[parent_product_id]
                price_list_updated_at_str = update_data['updated_at']
                
                if product_updated_at and price_list_updated_at_str:
                    product_updated_at_dt = datetime.strptime(product_updated_at, "%Y-%m-%dT%H:%M:%SZ")
                    price_list_updated_at_dt = datetime.strptime(price_list_updated_at_str, "%Y-%m-%dT%H:%M:%SZ")
                
                    if product_updated_at_dt <= price_list_updated_at_dt or abs((product_updated_at_dt - price_list_updated_at_dt).total_seconds()) <= 3:
                        product['updated_at'] = price_list_updated_at_dt.strftime("%Y-%m-%dT%H:%M:%SZ")
                        product['updated_by'] = update_data['updated_by']
            
            product['classification'] = classification_map.get(parent_product_sku, "")


            # Assign user-friendly name for updated_by
            updated_by = product.get('updated_by')
            product['updated_by'] = user_data_map.get(updated_by, {}).get("name", updated_by)
            
            # Filter variants by variant_sku_list
            product['variants'] = [v for v in product['variants'] if v.get('variant_sku') in variant_sku_list] or product['variants']
            
            # Initialize pricing data
            price_list_data = {key: "" for key in price_list_meta}  # Default empty prices
            variant_prices = []
            variant_costs = []

            for variant in product['variants']:
                variant_sku = variant.get('variant_sku')
                variant['variant_price'] = product['default_price'] if variant.get('variant_price') is None else variant.get('variant_price')
                variant['cost'] = sku_cost_map.get(variant_sku, None)
                variant['inventory_level'] = sku_inventory_map.get(variant_sku, None)
                variant['month_1'] = variant_monthly_data_map.get(variant_sku, {}).get('month_1', None)
                variant['month_2'] = variant_monthly_data_map.get(variant_sku, {}).get('month_2', None)
                variant['month_3'] = variant_monthly_data_map.get(variant_sku, {}).get('month_3', None)
                variant['month_4'] = variant_monthly_data_map.get(variant_sku, {}).get('month_4', None)
                variant['month_5'] = variant_monthly_data_map.get(variant_sku, {}).get('month_5', None)
                variant['month_6'] = variant_monthly_data_map.get(variant_sku, {}).get('month_6', None)
                variant['month_7'] = variant_monthly_data_map.get(variant_sku, {}).get('month_7', None)

                # Calculate total_sum of months
                monthly_data = variant_monthly_data_map.get(variant['variant_sku'], {})
                total_sum = sum([
                    int(monthly_data.get(f'month_{i}', 0) or 0) for i in range(2, 8)
                ])

                inventory_level = variant.get("inventory_level") or 0
                try:
                    inventory_level = int(inventory_level)
                except (ValueError, TypeError):
                    inventory_level = 0
                inventory_level = max(inventory_level, 1)  # Avoid division by 0

                variant_turn_rate = ((int(total_sum) / inventory_level) * 365) / day_difference
                variant_weeks_on_hand = 52 / variant_turn_rate if variant_turn_rate != 0 else 1

                # variant_turn_rate = (((int(total_sum) / int(variant["inventory_level"]) if int(variant["inventory_level"]) != 0 else 1) * 365) / day_difference)                           
                # variant_weeks_on_hand = 52 / variant_turn_rate if variant_turn_rate != 0 else 1

                variant['variant_turn_rate'] = round(variant_turn_rate, 2)
                variant['variant_weeks_on_hand'] = round(variant_weeks_on_hand, 2)
                
                # Collect valid prices and costs
                if variant['variant_price'] is not None:
                    variant_prices.append(variant['variant_price'])
                if variant['cost'] is not None:
                    variant_costs.append(variant['cost'])

                # Process price list efficiently
                for price in variant['price_list']:
                    price_list_key = price_list_map.get(price['price_list_id'])
                    if price_list_key:
                        price_value = float(price['price'])
                        if price_list_data[price_list_key]:  # Already has a value
                            min_price, max_price = map(float, price_list_data[price_list_key].split('-')) if '-' in price_list_data[price_list_key] else (float(price_list_data[price_list_key]), float(price_list_data[price_list_key]))
                            price_list_data[price_list_key] = f"{min(min_price, price_value)}-{max(max_price, price_value)}" if min_price != max_price else str(min_price)
                        else:
                            price_list_data[price_list_key] = str(price_value)

            # Set default_price as a range or single value
            if variant_prices:
                product['default_price'] = f"{min(variant_prices)}-{max(variant_prices)}" if len(set(variant_prices)) > 1 else str(min(variant_prices))
            
            # Set cost_range as a range or single value
            if variant_costs:
                product['cost_range'] = f"{min(variant_costs)}-{max(variant_costs)}" if len(set(variant_costs)) > 1 else str(min(variant_costs))
            else:
                product['cost_range'] = None

            # Assign formatted price lists to product
            # product['price_list'] = [{key: price_list_data[key], "price_list_id": i+1} for i, key in enumerate(price_list_meta)]
            product['price_list'] = [{
                price_list_meta[key]['name']: price_list_data[key],  # Replace `price_X` with actual name
                "price_list_id": i+1
            } for i, key in enumerate(price_list_meta)]
            
            # Process variant price list updates
            for variant in product['variants']:
                variant['parent_product_id'] = product['parent_product_id']
                variant['parent_product_name'] = product['parent_product_name']

                updated_price_list = []
                
                for price_list_key, meta in price_list_meta.items():
                    price_list_id = reversed_price_list_map.get(price_list_key)
                    price_list_name = meta['name']  # Extract actual price list name
                    price_entry = {'price_list_id': price_list_id, price_list_name: ""}

                    for price in variant['price_list']:
                        if price['price_list_id'] == price_list_id:
                            price_entry[price_list_name] = str(price.pop('price'))
                            break

                    updated_price_list.append(price_entry)

                variant['price_list'] = updated_price_list


        utc_now = datetime.now(timezone.utc)
        cst_tz = pytz.timezone('America/Chicago')  # Get CST timezone
        cst_now = utc_now.astimezone(cst_tz)  # Convert UTC to CST
        created_date = cst_now.strftime("%m-%d-%Y_%H:%M")
        zip_filename = f"PriceList_{created_date}.zip"

        # Prepare CSV data
        csv_content = io.StringIO()
        
        # Create headers - start with fixed columns
        headers = ['Product Name', 'SKU', 'Variant Name', 'Classification', 'Current Stock', 'Cost', 'Wholesale']
        
        # Add price list names as headers
        price_list_headers = [pl['name'] for pl in sorted_price_lists if pl['active']]
        headers.extend(price_list_headers)

        # Add month headers
        month_headers = [month_names[f"month_{i}"] for i in reversed(range(1, 8))]
        headers.extend(month_headers)

        # Add `updated_by` and `updated_at` (only for products, not variants)
        headers.extend(['Weeks On Hand', 'Turn Rate', 'Created At', 'Updated By', 'Updated At'])

        writer = csv.DictWriter(csv_content, fieldnames=headers)
        writer.writeheader()

        price_list_names = [pl['name'] for pl in sorted_price_lists if pl['active']]

        # Write data rows
        for product in products:
            # First write parent product row
            parent_row = {
                'Product Name': product['parent_product_name'],
                'SKU': product['parent_product_sku'],
                'Variant Name': "",
                "Classification": product.get('classification', ""),
                "Current Stock": product.get('inventory_level', None),
                'Cost': product.get('cost_range', ''),  # Show full range for parent
                'Wholesale': product.get('default_price', '')  # Show full range for parent
            }

            # Pre-fill all price list columns with empty values
            for price_list_name in price_list_headers:
                parent_row[price_list_name] = ''

            # Map price list values to their names
            for price_list_entry in product.get('price_list', []):
                for price_list_name, price_value in price_list_entry.items():
                    if price_list_name in price_list_names:
                        parent_row[price_list_name] = price_value

            for i in reversed(range(1, 8)):  # use all 7 months
                month_key = f"month_{i}"
                month_label = month_names.get(month_key, month_key)  # e.g., "Apr"
                parent_row[month_label] = product.get(month_key, '')

            parent_row['Weeks On Hand'] = product.get('weeks_on_hand', '')
            parent_row['Turn Rate'] = product.get('turn_rate', '')
            parent_row['Created At'] = product.get('created_at', '')
            parent_row['Updated By'] = product.get('updated_by', '')
            parent_row['Updated At'] = product.get('updated_at', '')

            writer.writerow(parent_row)

            # Process variant rows
            for variant in product.get('variants', []):
                variant_row = {
                    'Product Name': product['parent_product_name'],
                    'SKU': variant['variant_sku'],
                    'Variant Name': variant['variant_name'],
                    "Classification": "",
                    "Current Stock": variant.get('inventory_level', None),
                    'Cost': variant.get('cost', ''),
                    'Wholesale': variant.get('variant_price', '')
                }

                # Pre-fill all price list columns with empty values
                variant_row.update({name: '' for name in price_list_names})

                # Map price list values to their names for variants
                for price_list_entry in variant.get('price_list', []):
                    for price_list_name, price_value in price_list_entry.items():
                        if price_list_name in price_list_names:
                            variant_row[price_list_name] = price_value

                for i in reversed(range(1, 8)):
                    month_key = f"month_{i}"
                    month_label = month_names.get(month_key, month_key)
                    variant_row[month_label] = variant.get(month_key, '')

                variant_row['Weeks On Hand'] = variant.get('variant_weeks_on_hand', '')
                variant_row['Turn Rate'] = variant.get('variant_turn_rate', '')
                writer.writerow(variant_row)

            # Add a blank row between products
            writer.writerow(dict.fromkeys(headers, ''))

        # Compress CSV into ZIP
        zip_buffer = io.BytesIO()
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            zip_file.writestr(zip_filename.replace('.zip', '.csv'), csv_content.getvalue())
        
        zip_buffer.seek(0)

        # Send email with the CSV
        email_util.send_price_list_csv_data_email(
            store_id=store_id,
            file_content=zip_buffer,
            recipient_email=username,
            filename=zip_filename,
            user_name=username,
            created_date=created_date
        )
    except Exception as e:
        logger.error(f"Error in price_list_csv_export: {e}")
        raise e
    finally:
        conn.close()

def chunked_iterable(iterable, size):
    """Yield successive `size`-sized chunks from iterable."""
    it = iter(iterable)
    while True:
        chunk = list(islice(it, size))
        if not chunk:
            break
        yield chunk

def chunked_iterable_bc(iterable, chunk_size):
    for i in range(0, len(iterable), chunk_size):
        yield iterable[i:i + chunk_size]


def update_price_list_with_cost_plus_percentage(store_id, price_list_id, percentage, is_replace=False):
    store = mongo_db.get_store_by_id(store_id)
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    conn = pg_db.get_connection(store_id)

    try:
        PRICE_LIST_NAME_MAP = {
            7: "Value Tier Price List",
            1: "Tier 1 Price List",
            4: "Tier 2 Price List",
            8: "Tier Pro Price List",
            13: "Distributor",
            14: "TCD Warehouse",
            15: "MVD Warehouse",
            52: "VIP"
        }
        price_list_name = PRICE_LIST_NAME_MAP.get(price_list_id, "")

        query = {}
        if not is_replace:
            query["variants.price_list.price_list_id"] = {"$ne": price_list_id}

        # query = {"parent_product_id": 26491}
        # if not is_replace:
        #     query = {
        #         "$and": [
        #             {"parent_product_id": 26491},
        #             {"variants.price_list.price_list_id": {"$ne": price_list_id}}
        #         ]
        #     }

        product_docs = list(db["product_price_lists"].find(query))
        if not product_docs:
            logger.info("No products matched the query.")
            return

        all_variant_skus = []
        for doc in product_docs:
            for variant in doc.get("variants", []):
                sku = variant.get("variant_sku")
                if sku:
                    all_variant_skus.append(sku)

        unique_skus = list(set(all_variant_skus))

        sku_to_cost = {}
        CHUNK_SIZE = 1000
        for chunk in chunked_iterable(unique_skus, CHUNK_SIZE):
            placeholders = ', '.join([f":sku{i}" for i in range(len(chunk))])
            sku_param_dict = {f"sku{i}": sku for i, sku in enumerate(chunk)}
            query = text(f"SELECT sku, cost FROM skuvault_catalog WHERE sku IN ({placeholders})")
            response = conn.execute(query, sku_param_dict)
            for row in response.fetchall():
                sku, cost = row
                if cost is not None:
                    sku_to_cost[sku] = float(cost)

        bulk_ops = []
        bc_payload_map = {}
        errors = []

        for doc in product_docs:
            doc_id = doc["_id"]

            for variant in doc.get("variants", []):
                sku = variant.get("variant_sku")
                variant_id = variant.get("variant_id")
                if not sku or sku not in sku_to_cost:
                    continue

                cost = sku_to_cost[sku]
                new_price = round(cost * (1 + percentage / 100), 2)

                price_list = variant.get("price_list", [])
                price_list_map = {entry["price_list_id"]: entry for entry in price_list}
                existing_entry = price_list_map.get(price_list_id)

                if existing_entry:
                    if is_replace:
                        # Update price only
                        bulk_ops.append(UpdateOne(
                            {"_id": doc_id},
                            {
                                "$set": {
                                    "variants.$[variantElem].price_list.$[priceElem].price": new_price
                                }
                            },
                            array_filters=[
                                {"variantElem.variant_sku": sku},
                                {"priceElem.price_list_id": price_list_id}
                            ]
                        ))
                else:
                    # Append new price list entry
                    new_price_entry = {
                        "price_list_id": price_list_id,
                        "name": price_list_name,
                        "price": new_price
                    }

                    bulk_ops.append(UpdateOne(
                        {"_id": doc_id},
                        {
                            "$push": {
                                "variants.$[variantElem].price_list": new_price_entry
                            }
                        },
                        array_filters=[
                            {"variantElem.variant_sku": sku}
                        ]
                    ))

                if variant_id:
                    bc_entry = {
                        "variant_id": variant_id,
                        "currency": "usd",
                        "price": new_price
                    }
                    if price_list_id not in bc_payload_map:
                        bc_payload_map[price_list_id] = []
                    bc_payload_map[price_list_id].append(bc_entry)

        if bulk_ops:
            result = db["product_price_lists"].bulk_write(bulk_ops)
            logger.info(f"Bulk update result: {result.bulk_api_result}")
        else:
            logger.info("No documents required updating.")

        MAX_BC_PAYLOAD_SIZE = 400

        # Send payloads to BigCommerce
        for pl_id, bigcommerce_payload in bc_payload_map.items():
            for chunk in chunked_iterable_bc(bigcommerce_payload, MAX_BC_PAYLOAD_SIZE):
                bc_response, status_code = bc_price_list.update_price_list(store, pl_id, chunk)
                logger.error(f"BigCommerce response for price_list_id {pl_id}: {bc_response}")
                if status_code != 200:
                    error = {
                        "message": f"BigCommerce update failed for price_list_id {pl_id}",
                        "bc_response": bc_response,
                        "status": status_code
                    }
                    logger.error(error)
                    errors.append(error)

        if errors:
            return {"errors": errors}

    except Exception as e:
        logger.error(f"Error in update_price_list_with_cost_plus_percentage: {e}")
        raise e
    finally:
        conn.close()


def migrate_distributor_price_to_vip(store_id):
    db = mongo_db.get_admin_db_client_for_store_id(store_id)

    VIP_PRICE_LIST_ID = 52
    DISTRIBUTOR_PRICE_LIST_ID = 13
    VIP_PRICE_LIST_NAME = "VIP"

    product_price_lists_collection = db["product_price_lists"]
    static_price_lists_collection = db["static_price_lists"]

    # Step 1: Fetch documents where VIP price is missing in any variant
    query = {
        "variants.price_list.price_list_id": {"$ne": VIP_PRICE_LIST_ID}
    }
    # query = {"parent_product_id": 3066}  # filter only that product 
    product_docs = list(product_price_lists_collection.find(query))

    product_bulk_updates = []
    static_bulk_updates = {}

    for doc in product_docs:
        doc_id = doc["_id"]
        parent_product_id = doc.get("parent_product_id")
        for variant in doc.get("variants", []):
            variant_id = variant.get("variant_id")
            if not variant_id:
                continue

            # Skip if VIP already exists
            existing_price_lists = variant.get("price_list", [])
            price_list_ids = [p.get("price_list_id") for p in existing_price_lists]
            if VIP_PRICE_LIST_ID in price_list_ids:
                continue

            # Look for Distributor price
            distributor_price_entry = next((p for p in existing_price_lists if p.get("price_list_id") == DISTRIBUTOR_PRICE_LIST_ID), None)
            if not distributor_price_entry:
                continue

            vip_price = distributor_price_entry["price"]
            vip_entry = {
                "price_list_id": VIP_PRICE_LIST_ID,
                "name": VIP_PRICE_LIST_NAME,
                "price": vip_price
            }

            # Step 1: Update product_price_lists
            product_bulk_updates.append(UpdateOne(
                {"_id": doc_id},
                {"$push": {"variants.$[variantElem].price_list": vip_entry}},
                array_filters=[{"variantElem.variant_id": variant_id}]
            ))

            # Step 2: Prepare static_price_lists updates
            if parent_product_id not in static_bulk_updates:
                static_bulk_updates[parent_product_id] = {}

            static_bulk_updates[parent_product_id][variant_id] = vip_price

    # Execute bulk update for product_price_lists
    if product_bulk_updates:
        result = product_price_lists_collection.bulk_write(product_bulk_updates)
        logger.info(f"product_price_lists updated: {result.bulk_api_result}")
    else:
        logger.info("No product_price_lists updates needed.")

    # Step 2: Handle static_price_lists bulk updates
    final_static_ops = []
    for product_id, variant_map in static_bulk_updates.items():
        static_doc = static_price_lists_collection.find_one({"product_id": product_id})

        if not static_doc:
            # Create new document
            variants_array = []
            for variant_id, vip_price in variant_map.items():
                variants_array.append({
                    "variant_id": variant_id,
                    "price_list": [{
                        "price_list_id": VIP_PRICE_LIST_ID,
                        "name": VIP_PRICE_LIST_NAME,
                        "price": vip_price
                    }]
                })
            final_static_ops.append(UpdateOne(
                {"product_id": product_id},
                {"$set": {"product_id": product_id, "variants": variants_array}},
                upsert=True
            ))
        else:
            # Update existing document
            existing_variants = {v["variant_id"]: v for v in static_doc.get("variants", [])}
            for variant_id, vip_price in variant_map.items():
                existing_variant = existing_variants.get(variant_id)

                if existing_variant:
                    price_list_ids = [p["price_list_id"] for p in existing_variant.get("price_list", [])]
                    if VIP_PRICE_LIST_ID not in price_list_ids:
                        final_static_ops.append(UpdateOne(
                            {"product_id": product_id, "variants.variant_id": variant_id},
                            {"$push": {
                                "variants.$.price_list": {
                                    "price_list_id": VIP_PRICE_LIST_ID,
                                    "name": VIP_PRICE_LIST_NAME,
                                    "price": vip_price
                                }
                            }}
                        ))
                else:
                    final_static_ops.append(UpdateOne(
                        {"product_id": product_id},
                        {"$push": {
                            "variants": {
                                "variant_id": variant_id,
                                "price_list": [{
                                    "price_list_id": VIP_PRICE_LIST_ID,
                                    "name": VIP_PRICE_LIST_NAME,
                                    "price": vip_price
                                }]
                            }
                        }}
                    ))

    if final_static_ops:
        result = static_price_lists_collection.bulk_write(final_static_ops)
        logger.info(f"static_price_lists updated: {result.bulk_api_result}")
    else:
        logger.info("No static_price_lists updates needed.")


def sync_skuvault_and_price_lists_data(store_id):
    pg_skuvault_sales_util.update_sku_vault_purchase_orders(store_id)
    logger.info("SkuVault purchase orders updated")
    time.sleep(3)

    sync_price_lists_in_mongodb(store_id)
    logger.info("Price lists synced in MongoDB")

    pg_analytics_util.refresh_profitability_product_customer_groups_table(store_id)
    logger.info("Profitability product customer groups table refreshed")


def check_promo_product_and_create_ticket(store_id, product_ids, old_data_map):
    redis_client = redis_util.get_redis_client(store_id)
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    conn = pg_db.get_connection(store_id)
    # logger.info(f"old_data_map: {old_data_map}")
    try:
        product_price_lists_collection = db["product_price_lists"]

        # Step 1: Single Mongo query to fetch all promo products
        matching_products = list(product_price_lists_collection.find({
            "parent_product_id": {"$in": product_ids},
            "categories": 2869
        }, {
            "parent_product_id": 1,
            "parent_product_name": 1,
            "default_price": 1,
            "variants": 1
        }))

        if not matching_products:
            logger.info("No products found with promo category.")
            return

        # Step 2: Build maps
        product_name_map = {}
        product_variants_map = {}

        for product in matching_products:
            pid = product["parent_product_id"]
            # product_name_map[pid] = product.get("parent_product_name", "No Name")
            pname = product.get("parent_product_name", "No Name")
            product_name_map[pid] = pname
            product_variants_map[pid] = {
                "variants": product.get("variants", []),
                "default_price": product.get("default_price", 0)
            }

        # Step 3: Generate descriptions using helper
        product_price_description_map = _generate_price_list_summary(product_variants_map, old_data_map)

        query = text("""
            SELECT c.id, p.owner_username, m.id AS module_id,
                CASE WHEN apa.username = '<EMAIL>' THEN apa.username ELSE NULL END as user_access
            FROM agile_project_columns c
            JOIN agile_projects p ON c.project_id = p.id
            JOIN agile_project_modules m ON m.project_id = p.id
            LEFT JOIN agile_project_access apa ON apa.project_id = p.id AND apa.username = '<EMAIL>'
            WHERE c.project_id = :project_id
            ORDER BY c.id
            LIMIT 1
        """)
        response = conn.execute(query, {"project_id": 178})
        column_id, owner_username, module_id, user_access = response.fetchone()
        
        # Step 5: Create cards
        for product_id in product_name_map:
            product_name = product_name_map[product_id]
            
            # Redis Lock Key
            redis_key = f"promo_card_created:{product_id}"

            # Check Redis key
            if redis_client.exists(redis_key):
                logger.info(f"Redis: Card already created recently for '{product_name}'. Skipping.")
                continue

            # Check for recent card
            check_query = text("""
                SELECT id FROM agile_project_cards
                WHERE title = :title
                  AND created_at >= NOW() - INTERVAL '15 seconds'
                  AND project_id = :project_id
                LIMIT 1
            """)
            recent_card = conn.execute(check_query, {
                "title": product_name,
                "project_id": 178
            }).fetchone()

            logger.info(f"recent_card: {recent_card}")

            if recent_card:
                logger.info(f"DB: Card already exists for '{product_name}' within last 15 seconds. Skipping.")
                continue

            description = product_price_description_map.get(product_id, "")

            assigned_to = user_access if user_access else owner_username
            payload = {
                "title": f"{product_name}",
                "description": description,
                "current_column_id": column_id,
                "status": 0,
                "priority": 0,
                "assigned_to": assigned_to,
                "spent_time": "0h",
                "estimation": "0h",
                "due_date": None,
                "start_date": None,
                "is_archived": False,
                "parent_card_id": None,
                "child_card_id": None,
            }

            # Set Redis lock BEFORE card creation to avoid race condition
            redis_client.set(redis_key, "1", ex=15)  # expires in 15 seconds

            agile_project_utils.create_card(
                store_id, payload,
                username=owner_username,
                project_id=178,
                module_id=module_id
            )
            logger.info(f"Card created for product {product_id}")

    except Exception as e:
        logger.error(traceback.format_exc())
        raise e
    finally:
        conn.close()


# def _generate_price_list_summary(product_variants_map, old_data_map):
#     product_price_description_map = {}

#     for product_id, product_data in product_variants_map.items():
#         variants = product_data.get("variants", [])
#         default_price = product_data.get("default_price", 0)

#         price_list_map = defaultdict(lambda: {"name": "", "prices": []})
#         all_prices = [default_price] if default_price is not None else []

#         for variant in variants:
#             variant_price = variant.get("variant_price")
#             if variant_price is not None:
#                 all_prices.append(variant_price)

#             for price_entry in variant.get("price_list", []):
#                 price_list_id = price_entry["price_list_id"]
#                 name = price_entry["name"]
#                 price = price_entry["price"]
#                 price_list_map[price_list_id]["name"] = name
#                 price_list_map[price_list_id]["prices"].append(price)

#         # === Prepare New Price Map ===
#         new_price_map = {}

#         if all_prices:
#             min_combined = min(all_prices)
#             max_combined = max(all_prices)
#             combined_desc = f"${min_combined}" if min_combined == max_combined else f"${min_combined}-{max_combined}"
#             new_price_map["Wholesale"] = combined_desc

#         for pl in price_list_map.values():
#             prices = pl["prices"]
#             name = pl["name"]
#             if not prices:
#                 continue
#             min_price = min(prices)
#             max_price = max(prices)
#             price_desc = f"${min_price}" if min_price == max_price else f"${min_price}-{max_price}"
#             new_price_map[name] = price_desc

#         # === Prepare Old Price Map ===
#         old_price_map = old_data_map.get(str(product_id), {})

#         # === Generate Final HTML ===
#         final_html = f"""
#         <div data-lexical-layout-container="true" style="grid-template-columns: 1fr 1fr; display: grid; gap: 10px;">
#             {html_template.generate_price_table("Old Price", old_price_map)}
#             {html_template.generate_price_table("New Price", new_price_map)}
#         </div>
#         """

#         product_price_description_map[product_id] = final_html

#     return product_price_description_map

def _generate_price_list_summary(product_variants_map, old_data_map):
    product_price_description_map = {}

    # Define only the order of specific price lists we want to prioritize
    PRIORITY_ORDER = [7, 1, 4, 8, 13, 15, 14, 52]

    def sort_price_map(price_map):
        """Helper function to sort price map according to requirements"""
        # Create a list of (name, price) tuples with their order
        price_items = []
        for name, price in price_map.items():
            if name == "Wholesale":
                # Wholesale always comes first
                price_items.append((name, price, -1))
                continue
            
            # Find the price list ID from the product data
            price_list_id = None
            for product_data in product_variants_map.values():
                for variant in product_data.get("variants", []):
                    for pl in variant.get("price_list", []):
                        if pl["name"] == name:
                            price_list_id = pl["price_list_id"]
                            break
                    if price_list_id:
                        break
                if price_list_id:
                    break

            # Get order based on priority list
            order = PRIORITY_ORDER.index(price_list_id) if price_list_id in PRIORITY_ORDER else len(PRIORITY_ORDER)
            price_items.append((name, price, order))
        
        # Sort by order and then by name
        sorted_items = sorted(price_items, key=lambda x: (x[2], x[0]))
        return {name: price for name, price, _ in sorted_items}

    for product_id, product_data in product_variants_map.items():
        variants = product_data.get("variants", [])
        default_price = product_data.get("default_price", 0)

        # Collect all prices
        all_prices = [default_price] if default_price is not None else []
        new_price_map = {}

        # Add Wholesale price
        if all_prices:
            min_combined = min(all_prices)
            max_combined = max(all_prices)
            combined_desc = f"${min_combined}" if min_combined == max_combined else f"${min_combined}-{max_combined}"
            new_price_map["Wholesale"] = combined_desc

        # Collect variant prices and price list prices
        for variant in variants:
            variant_price = variant.get("variant_price")
            if variant_price is not None:
                all_prices.append(variant_price)

            for price_entry in variant.get("price_list", []):
                name = price_entry["name"]
                price = price_entry["price"]
                prices = new_price_map.get(name, [])
                prices.append(price)
                new_price_map[name] = prices

        # Convert price lists to min-max format
        for name, prices in new_price_map.items():
            if name != "Wholesale":
                min_price = min(prices)
                max_price = max(prices)
                new_price_map[name] = f"${min_price}" if min_price == max_price else f"${min_price}-{max_price}"

        # Get and sort old price map
        old_price_map = old_data_map.get(str(product_id), {})

        # Sort both maps
        new_price_map = sort_price_map(new_price_map)
        old_price_map = sort_price_map(old_price_map)

        # Generate HTML
        final_html = f"""
        <div data-lexical-layout-container="true" style="grid-template-columns: 1fr 1fr; display: grid; gap: 10px;">
            {html_template.generate_price_table("Old Price", old_price_map)}
            {html_template.generate_price_table("New Price", new_price_map)}
        </div>
        """

        product_price_description_map[product_id] = final_html

    return product_price_description_map



def _fetch_old_data_of_products(store_id, product_ids):
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    product_price_lists_collection = db["product_price_lists"]
    
    try:
        # Fetch all product_price_list records for the given product_ids in one query
        products = list(product_price_lists_collection.find({
            "parent_product_id": {"$in": product_ids}
        }))
        
        # Build a map from product_id to its variants and default_price
        product_variants_map = {}
        for product in products:
            product_id = product.get("parent_product_id")
            if product_id:
                product_variants_map[product_id] = {
                    "variants": product.get("variants", []),
                    "default_price": product.get("default_price")
                }

        result_map = {}

        for product_id, product_data in product_variants_map.items():
            variants = product_data.get("variants", [])
            default_price = product_data.get("default_price", 0)

            # Collect all prices including default and variant prices
            all_prices = [default_price] if default_price is not None else []

            # Price list aggregation
            price_list_map = defaultdict(lambda: {"name": "", "prices": []})

            for variant in variants:
                variant_price = variant.get("variant_price")
                if variant_price is not None:
                    all_prices.append(variant_price)

                for price_entry in variant.get("price_list", []):
                    price_list_id = price_entry["price_list_id"]
                    name = price_entry["name"]
                    price = price_entry["price"]
                    price_list_map[price_list_id]["name"] = name
                    price_list_map[price_list_id]["prices"].append(price)

            product_price_output = {}

            # Optionally add combined price info if needed
            if all_prices:
                min_combined = min(all_prices)
                max_combined = max(all_prices)
                combined_price_desc = f"${min_combined}" if min_combined == max_combined else f"${min_combined}-{max_combined}"
                product_price_output["Wholesale"] = combined_price_desc

            # Format individual price list prices
            for pl in price_list_map.values():
                prices = pl["prices"]
                name = pl["name"]
                if not prices:
                    continue

                min_price = min(prices)
                max_price = max(prices)

                if min_price == max_price:
                    price_desc = f"${min_price}"
                else:
                    price_desc = f"${min_price}-{max_price}"

                product_price_output[name] = price_desc

            result_map[product_id] = product_price_output

        return result_map
    
    except Exception as e:
        logger.error(traceback.format_exc())
        raise e


def update_multi_store_price_list_from_csv(store_id, csv_data, username, filename):
    try:
        # Set a reasonable max_workers based on the number of stores
        max_workers = min(len(csv_data), 10)  # Cap at 10 concurrent threads
        
        def process_store(storeID, data):
            """Helper function to process a single store"""
            try:
                errors = update_price_list_from_csv(storeID, data, username, filename, is_from_multi_thred=True)
                return {"store_id": storeID, "status": "success", "errors": errors}
            except Exception as e:
                logger.error(f"Error processing store {storeID}: {e}")
                return {"store_id": storeID, "status": "error", "error": str(e)}
        
        # Use ThreadPoolExecutor for concurrent execution
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_store = {
                executor.submit(process_store, storeID, data): storeID 
                for storeID, data in csv_data.items()
            }
            
            # Collect results as they complete
            results = []
            for future in as_completed(future_to_store):
                storeID = future_to_store[future]
                try:
                    result = future.result()
                    results.append(result)
                    logger.info(f"Completed processing store {storeID}: {result['status']}")
                except Exception as e:
                    logger.error(f"Exception occurred for store {storeID}: {e}")
                    results.append({"store_id": storeID, "status": "error", "error": str(e)})
        
        # Log summary
        successful_stores = [r for r in results if r['status'] == 'success']
        failed_stores = [r for r in results if r['status'] == 'error']
        
        logger.info(f"Multi-store processing completed. Success: {len(successful_stores)}, Failed: {len(failed_stores)}")
        
        if failed_stores:
            logger.error(f"Failed stores: {[r['store_id'] for r in failed_stores]}")
        
        # Combine all error messages
        all_errors = []
        
        # Collect errors from successful stores (these are the error_text from update_price_list_from_csv)
        for result in successful_stores:
            if result.get('errors'):
                all_errors.append(f"Store {result['store_id']}: {result['errors']}")
        
        # Collect errors from failed stores
        for result in failed_stores:
            error_msg = result.get('error', 'Unknown error')
            all_errors.append(f"Store {result['store_id']}: {error_msg}")
        
        # Combine all errors into a single text
        errors_text = "\n".join(all_errors) if all_errors else ""
        
        # Update database status
        db = mongo_db.get_admin_db_client_for_store_id(store_id)
        db["user_preference"].update_one(
            {"type": "multi_store_price_list_import"}, 
            {"$set": {"status": "completed"}}, 
            upsert=True
        )

        # Send acknowledgment email with combined error information
        email_util.send_price_list_import_email(
            store_id=store_id,
            errors=errors_text,
            recipient_email=username,
            user_name=username,
            filename=filename,
            created_date_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )
        
    except Exception as e:
        logger.error(traceback.format_exc())
        raise e


# def remove_old_price_list_logs(store_id):
#     conn = pg_db.get_connection(store_id)
#     try:
#         query = text("""
#             DELETE FROM pricelist_change_logs
#             WHERE updated_at::timestamp < NOW() - INTERVAL '30 days'
#         """)
#         conn.execute(query)
#         conn.commit()
#         logger.info("Old price list logs removed")
#     except Exception as e:
#         logger.error(traceback.format_exc())
#         raise e
#     finally:
#         conn.close()

