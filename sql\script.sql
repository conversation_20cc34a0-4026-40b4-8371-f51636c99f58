-- Table: public.customer_addresses

-- DROP TABLE IF EXISTS public.customer_addresses;

CREATE TABLE IF NOT EXISTS public.table_metadata
(
    table_name varchar(255) primary key,
    last_updated_time timestamp with time zone
)

CREATE TABLE IF NOT EXISTS public.customer_addresses
(
    customer_id bigint,
    customer_address_id bigint NOT NULL,
    first_name text COLLATE pg_catalog."default",
    last_name text COLLATE pg_catalog."default",
    company text COLLATE pg_catalog."default",
    address_line_1 text COLLATE pg_catalog."default",
    address_line_2 text COLLATE pg_catalog."default",
    city text COLLATE pg_catalog."default",
    state text COLLATE pg_catalog."default",
    postal_code text COLLATE pg_catalog."default",
    country_code text COLLATE pg_catalog."default",
    address_type text COLLATE pg_catalog."default",
    phone text COLLATE pg_catalog."default",
    CONSTRAINT customer_addresses_pkey PRIMARY KEY (customer_address_id)
)

TABLESPACE pg_default;

ALTER TABLE IF EXISTS public.customer_addresses
    OWNER to midwestgoods;

-- Table: public.customer_form_fields

-- DROP TABLE IF EXISTS public.customer_form_fields;

CREATE TABLE IF NOT EXISTS public.customer_form_fields
(
    customer_id bigint NOT NULL,
    custom_field_name text COLLATE pg_catalog."default" NOT NULL,
    custom_field_value text COLLATE pg_catalog."default",
    CONSTRAINT customer_form_fields_pkey PRIMARY KEY (customer_id, custom_field_name)
)

TABLESPACE pg_default;

ALTER TABLE IF EXISTS public.customer_form_fields
    OWNER to midwestgoods;

-- Table: public.customers

-- DROP TABLE IF EXISTS public.customers;

CREATE TABLE IF NOT EXISTS public.customers
(
    customer_id bigint NOT NULL,
    first_name text COLLATE pg_catalog."default",
    last_name text COLLATE pg_catalog."default",
    company text COLLATE pg_catalog."default",
    email text COLLATE pg_catalog."default",
    phone text COLLATE pg_catalog."default",
    notes text COLLATE pg_catalog."default",
    accepts_product_review_abandoned_cart_emails boolean,
    date_created timestamp with time zone,
    date_modified timestamp with time zone,
    tax_exempt_category text COLLATE pg_catalog."default",
    registration_ip_address text COLLATE pg_catalog."default",
    "store_credit_in_USD" bigint,
    customer_group_id bigint,
    customer_group_name text COLLATE pg_catalog."default",
    CONSTRAINT customers_pkey PRIMARY KEY (customer_id)
)

TABLESPACE pg_default;

ALTER TABLE IF EXISTS public.customers
    OWNER to midwestgoods;

-- Table: public.product_categories

-- DROP TABLE IF EXISTS public.product_categories;

CREATE TABLE IF NOT EXISTS public.product_categories
(
    product_id bigint NOT NULL,
    category_id bigint NOT NULL,
    category_name text COLLATE pg_catalog."default",
    CONSTRAINT product_categories_pkey PRIMARY KEY (category_id, product_id)
)

TABLESPACE pg_default;

ALTER TABLE IF EXISTS public.product_categories
    OWNER to midwestgoods;

-- Table: public.product_custom_fields

-- DROP TABLE IF EXISTS public.product_custom_fields;

CREATE TABLE IF NOT EXISTS public.product_custom_fields
(
    product_id bigint,
    custom_field_id bigint NOT NULL,
    custom_field_name text COLLATE pg_catalog."default",
    custom_field_value text COLLATE pg_catalog."default",
    CONSTRAINT product_custom_fields_pkey PRIMARY KEY (custom_field_id)
)

TABLESPACE pg_default;

ALTER TABLE IF EXISTS public.product_custom_fields
    OWNER to midwestgoods;

-- Table: public.products

-- DROP TABLE IF EXISTS public.products;

CREATE TABLE IF NOT EXISTS public.products
(
    product_id bigint NOT NULL,
    product_name text COLLATE pg_catalog."default",
    product_type text COLLATE pg_catalog."default",
    sku text COLLATE pg_catalog."default",
    bin_picking_number text COLLATE pg_catalog."default",
    universal_product_code text COLLATE pg_catalog."default",
    global_trade_item_number text COLLATE pg_catalog."default",
    manufacturer_part_number text COLLATE pg_catalog."default",
    date_created timestamp with time zone,
    date_modified timestamp with time zone,
    brand_id bigint,
    brand_name text COLLATE pg_catalog."default",
    page_title text COLLATE pg_catalog."default",
    custom_url text COLLATE pg_catalog."default",
    description text COLLATE pg_catalog."default",
    is_featured boolean,
    condition text COLLATE pg_catalog."default",
    sort_order bigint,
    preorder_release_date bigint,
    preorder_message text COLLATE pg_catalog."default",
    availability text COLLATE pg_catalog."default",
    availability_description text COLLATE pg_catalog."default",
    weight double precision,
    width double precision,
    depth double precision,
    height double precision,
    price double precision,
    cost_price double precision,
    retail_price double precision,
    sale_price double precision,
    fixed_cost_shipping_price double precision,
    is_free_shipping boolean,
    is_preorder_only boolean,
    is_price_hidden boolean,
    price_hidden_label text COLLATE pg_catalog."default",
    order_quantity_minimum bigint,
    order_quantity_maximum bigint,
    gift_wrapping_options_type text COLLATE pg_catalog."default",
    tax_class_id bigint,
    product_tax_code text COLLATE pg_catalog."default",
    inventory_level bigint,
    inventory_warning_level bigint,
    open_graph_type text COLLATE pg_catalog."default",
    open_graph_title text COLLATE pg_catalog."default",
    open_graph_use_product_name boolean,
    base_variant_id bigint,
    view_count bigint,
    CONSTRAINT products_pkey PRIMARY KEY (product_id)
)

TABLESPACE pg_default;

ALTER TABLE IF EXISTS public.products
    OWNER to midwestgoods;

-- Table: public.variants

-- DROP TABLE IF EXISTS public.variants;

CREATE TABLE IF NOT EXISTS public.variants
(
    product_id bigint,
    variants_id bigint NOT NULL,
    variants_sku text COLLATE pg_catalog."default",
    variants_sku_id double precision,
    product_name text COLLATE pg_catalog."default",
    variant_options text COLLATE pg_catalog."default",
    variants_cost_price double precision,
    variants_price double precision,
    variants_sale_price double precision,
    variants_retail_price double precision,
    variants_weight double precision,
    variants_width double precision,
    variants_height double precision,
    variants_depth double precision,
    variants_is_free_shipping boolean,
    variants_fixed_cost_shipping_price double precision,
    variants_purchasing_disabled boolean,
    variants_purchasing_disabled_message text COLLATE pg_catalog."default",
    variants_upc text COLLATE pg_catalog."default",
    variants_inventory_level bigint,
    variants_inventory_warning_level bigint,
    variants_bin_picking_number text COLLATE pg_catalog."default",
    out_of_stock_date timestamp without time zone,
    CONSTRAINT variants_pkey PRIMARY KEY (variants_id)
)

TABLESPACE pg_default;

ALTER TABLE IF EXISTS public.variants
    OWNER to midwestgoods;