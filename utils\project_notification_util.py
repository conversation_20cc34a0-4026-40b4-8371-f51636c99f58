from mongo_db import store_db
import mongo_db
import logging
import traceback
logger = logging.getLogger()
import logging
import pg_db
from sqlalchemy import text
from utils import store_util, email_util
import pg_db
import re

class ProjectNotifications:
    TICKET_CREATED = "ticket_created"
    TICKET_ASSIGNED = "ticket_assigned"
    COMMENT_ADDED = "comment_added"

def send_project_notification(store_id, event_type, entity_id):
    # pass
    try:
        if event_type == ProjectNotifications.TICKET_CREATED or event_type == ProjectNotifications.TICKET_ASSIGNED:
            _send_ticket_assigned_or_created_notification(store_id, entity_id, event_type)
        if event_type == ProjectNotifications.COMMENT_ADDED:
            _send_comment_added_notification(store_id, entity_id)
    except Exception as e:
        logger.error(traceback.format_exc())


def _send_ticket_assigned_or_created_notification(store_id, ticket_id, event_type):
    conn = pg_db.get_connection(store_id)
    try:
        query = """
            SELECT
                c.title, 
                c.card_identifier, 
                c.assigned_to, 
                p.name AS project_name, 
                p.owner_username,
                p.id AS project_id
            FROM agile_project_cards c
            INNER JOIN agile_projects p ON c.project_id = p.id
            WHERE c.id = :ticket_id
        """
        result = conn.execute(text(query), {"ticket_id": ticket_id}).fetchone()

        if result:
            assigned_to = result[2]
            owner_username = result[4]
            project_id = result[5]
            card_identifier = result[1]
            
            owner_notification_enabled = False
            owner_ps_notification_enabled = check_project_specific_notification_enabled(conn, project_id, owner_username)
            if owner_ps_notification_enabled:
                owner_notification_enabled = check_if_notification_enabled(store_id, event_type, 'Projects', 'Owner Notification', owner_username)
               
            assigned_to_notification_enabled = False
            assigned_to_ps_notification_enabled = False
            if assigned_to:
                assigned_to_ps_notification_enabled = check_project_specific_notification_enabled(conn, project_id, assigned_to)
                if assigned_to_ps_notification_enabled:
                    assigned_to_notification_enabled = check_if_notification_enabled(store_id, event_type, 'Projects', 'User Notification', assigned_to)

            total_users = []
            usernames = []
            if assigned_to and assigned_to_notification_enabled:
                usernames.append(assigned_to)
            if owner_notification_enabled:
                usernames.append(owner_username)
            usernames = list(set(usernames))  # Remove duplicates if any

            total_users.append(assigned_to)
            total_users.append(owner_username)
            total_users = list(set(total_users))  # Remove duplicates if any

            if not usernames:
                logger.warning(f"No users with notifications enabled for ticket {ticket_id}")
                return

            # Fetch user name map from MongoDB
            username_map = store_db.fetch_users_by_usernames(store_id, total_users)

            content = {
                "title": result[0],
                "card_identifier": result[1],
                "project_name": result[3],
                "assignee_name": username_map.get(assigned_to, {}).get("name", assigned_to),
                "project_url": f"https://adminapp.midwestgoods.com/projects/all-projects/{project_id}?view=agile-board",
                "ticket_url": f"https://adminapp.midwestgoods.com/projects/all-projects/{project_id}/ticket/{ticket_id}/{card_identifier}?view=comments"
            }

            recipient_emails = usernames

            email_util.send_project_notification_email(store_id, content, recipient_emails, username_map, event_type)

        else:
            logger.warning(f"No ticket found with id {ticket_id}")

    except Exception as e:
        logger.error(f"Error sending project notification: {e}")
        logger.error(traceback.format_exc())

    finally:
        conn.close()


def _send_comment_added_notification(store_id, comment_id):
    conn = pg_db.get_connection(store_id)
    try:
        query = """
            SELECT
                c.title,
                c.card_identifier,
                c.assigned_to,
                p.name AS project_name,
                p.owner_username,
                p.id AS project_id,
                com.comment,
                com.card_id
            FROM agile_card_comments com
            INNER JOIN agile_project_cards c ON com.card_id = c.id
            INNER JOIN agile_projects p ON c.project_id = p.id
            WHERE com.id = :comment_id
        """
        result = conn.execute(text(query), {"comment_id": comment_id}).fetchone()

        if result:
            title = result[0]
            card_identifier = result[1]
            assigned_to = result[2]
            project_name = result[3]
            owner_username = result[4]
            project_id = result[5]
            comment = result[6]
            ticket_id = result[7]
            
            # Extract emails from comment (e.g. #<EMAIL>)
            comment_emails = re.findall(r'#([\w\.-]+@[\w\.-]+)', comment or '')
            comment_emails = list(set(comment_emails))  # Remove duplicates

            # Check notification for assigned_to
            assigned_to_notification_enabled = False
            assigned_to_ps_notification_enabled = False
            if assigned_to:
                assigned_to_ps_notification_enabled = check_project_specific_notification_enabled(conn, project_id, assigned_to)
                if assigned_to_ps_notification_enabled:
                    assigned_to_notification_enabled = check_if_notification_enabled(store_id, "comment_added", 'Projects', 'User Notification', assigned_to)

            # Check notification for owner_username
            owner_notification_enabled = False
            owner_ps_notification_enabled = check_project_specific_notification_enabled(conn, project_id, owner_username)
            if owner_ps_notification_enabled:
                owner_notification_enabled = check_if_notification_enabled(store_id, "comment_added", 'Projects', 'Owner Notification', owner_username)

            # Check notification for each comment_email
            enabled_comment_emails = []
            for email in comment_emails:
                comment_email_ps_notification_enabled = check_project_specific_notification_enabled(conn, project_id, email)
                if comment_email_ps_notification_enabled:
                    if check_if_notification_enabled(store_id, "comment_added", 'Projects', 'User Notification', email):
                        enabled_comment_emails.append(email)

            total_users = []
            usernames = []
            if assigned_to and assigned_to_notification_enabled:
                usernames.append(assigned_to)
            if owner_notification_enabled:
                usernames.append(owner_username)
            usernames.extend(enabled_comment_emails)
            usernames = list(set(usernames))  # Remove duplicates

            total_users.append(assigned_to)
            total_users.append(owner_username)
            total_users.extend(enabled_comment_emails)
            total_users = list(set(total_users))  # Remove duplicates if any

            if not usernames:
                logger.warning(f"No users with notifications enabled for comment {comment_id}")
                return

            # Fetch user name map from MongoDB
            username_map = store_db.fetch_users_by_usernames(store_id, total_users)
            
            # Sanitize comment: replace <p>Name#email</p> with <p>Actual Name</p>
            def replace_email_mentions(match):
                name_part = match.group(1).strip()
                email = match.group(2).strip()
                actual_name = username_map.get(email, {}).get("name", name_part)
                return f'<span style="color: #1a73e8;">{actual_name}</span>'

            cleaned_comment = re.sub(r'<p>(.*?)#([\w\.-]+@[\w\.-]+)</p>', replace_email_mentions, comment or '')

            content = {
                "title": title,
                "card_identifier": card_identifier,
                "project_name": project_name,
                "comment": cleaned_comment,
                "assignee_name": username_map.get(assigned_to, {}).get("name", assigned_to),
                "project_url": f"https://adminapp.midwestgoods.com/projects/all-projects/{project_id}?view=agile-board",
                "ticket_url": f"https://adminapp.midwestgoods.com/projects/all-projects/{project_id}/ticket/{ticket_id}/{card_identifier}?view=comments"
            }

            recipient_emails = usernames

            event_type = "comment_added"
            
            if recipient_emails:
                email_util.send_project_notification_email(store_id, content, recipient_emails, username_map, event_type)
        else:
            logger.warning(f"No comment found with id {comment_id}")

    except Exception as e:
        logger.error(traceback.format_exc())
        logger.error(f"Error sending comment added notification: {e}")
    finally:
        conn.close()

def check_project_specific_notification_enabled(conn, project_id, username):
    try:
        is_notification_enabled = False
        if username and project_id:
            query = """
                SELECT is_notification_enabled
                FROM agile_project_access WHERE project_id = :project_id AND username = :username
            """
            result = conn.execute(text(query), {"project_id": project_id, "username": username}).fetchone()
            if result:
                is_notification_enabled = result[0] 
        return is_notification_enabled
    except Exception as e:
        logger.error(traceback.format_exc())
        return False

def check_if_notification_enabled(store_id, event_type, module_name, notification_type, email_id):
    try:
        data = mongo_db.fetch_one_document_from_admin_collection(
            store_id, mongo_db.EMAIL_NOTIFICATION_MASTER, {"module_name": module_name}
        )
        if not data:
            return True  # No module found, notifications enabled by default

        for section in data.get('sections', []):
            if section.get('notification_type') == notification_type:
                for notification in section.get('notifications', []):
                    if notification.get('name') == event_type:
                        enabled_users = notification.get('enabled_users', [])
                        if email_id in enabled_users:
                            return True  # Event type found and user is enabled
                        else:
                            return False  # Event type found and user is not discontinued
                # If we reach here, notification_type matched but event_type not found
                return False  # Event type not found, notifications enabled by default
        # If we reach here, notification_type not found
        return False  # Notification type not found, notifications enabled by default
    except Exception as e:
        logger.error(traceback.format_exc())
        return False  # On error, disable notifications for safety

    

    

