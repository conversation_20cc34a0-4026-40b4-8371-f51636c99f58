import time
import datetime
from utils import bc_util, redis_util, store_util, email_util
import logging
import traceback
import copy
import mongo_db
from mongo_db import task_db
from config import appconfig
import threading
from utils import admin_app_notification_util

logger = logging.getLogger()

HOUR_IN_SECOND = 3600
DAY_IN_SECOND = 24 * HOUR_IN_SECOND
MONTH_IN_SECOND = 30 * DAY_IN_SECOND


def purge_task_log(store_id, time_minutes=1440):
    task_db.delete_older_task(store_id, time_minutes)

def check_and_update_graphql_token(store):
    result = False
    store_id = store['id']
    token = redis_util.get_graphql_token(store_id)
    current_time = int(time.time()) + (HOUR_IN_SECOND)
    if not token or token['expires_at'] <= current_time:
        expires_at = int(time.time()) + MONTH_IN_SECOND
        bc_api = store_util.get_bc_api_creds(store)
        token = bc_util.get_bc_graphql_token(bc_api, expires_at)
        if token:
            redis_util.update_graphql_token(store_id, token, expires_at)
            result = True
    else:
        result = True
    
    return result

def update_catalog_data(store_id, adhoc):
    import task
    task.update_brands.apply_async(args=[store_id, adhoc])
    task.update_categories.apply_async(args=[store_id, adhoc])
    task.update_product_card_cache.apply_async(args=[store_id, adhoc])
    task.update_products.apply_async(args=[store_id, adhoc])

def update_customer_data(store_id, adhoc):
    import task
    task.update_customers.apply_async(args=[store_id, adhoc])
    task.update_customer_groups.apply_async(args=[store_id, adhoc])
    task.update_price_list_assignment.apply_async(args=[store_id, adhoc])

def build_store(store_id, adhoc):
    #print("Building Store...")
    store = store_util.get_store_by_id(store_id)
    if store:
        result = check_and_update_graphql_token(store)
        update_customer_data(store_id, adhoc)
        update_catalog_data(store_id, adhoc)
        import task
        task.update_webpages.apply_async(args=[store_id, adhoc])
        task.update_bc_redirects.apply_async(args=[store_id, adhoc])
        
        return result, {"message": "Completed successfully"}

def bc_webhook_executor(store):
    bc_webhook_url = "v3/hooks"
    webhook_list = ["category", "product", "order"]
    bc_api = store_util.get_bc_api_creds(store)
    for i in webhook_list:
        # Create webhooks whos register in webhooklist...
        req_data = {
            "scope": "store/" + i + "/*",
            "destination": "https://api.atlantixdigital.com/bc/webhook",
            "is_active": True
        }
        bc_util.create_bc_webhook(bc_api, bc_webhook_url, req_data)

def get_workers():
    from task import celery
    i = celery.control.inspect()
    tasks = {}
    workers = []
    r = i.registered()
    if r and len(r) > 0:
        for worker, task_list in r.items():
            workers.append(worker)
            for _t in task_list:
                tasks[_t] = worker
    
    return workers, tasks

def _get_task_unique_name(store_id, task_name):
    return task_name + ":" + store_id

def _get_task_name(_task):
    req = _task.get('request', None)
    name = None
    if req:
        name = req.get('name', None)
        args = req.get('args', None)
        if args and len(args) > 0:
            name = _get_task_unique_name(args[0], name)
    return name

def _get_task_list_from_celery():
    from task import celery
    i = celery.control.inspect()
    task_status = {}

    _s = i.scheduled()
    if _s and len(_s):
        for worker, task_list in _s.items(): 
            for _t in task_list: 
                task_name = _get_task_name(_t)
                _task = task_status.get(task_name, {})
                _task[worker] = "scheduled"
                task_status[task_name] = _task

    # _s = i.active()
    # if _s and len(_s):
    #     for worker, task_list in _s.items(): 
    #         for _t in task_list: 
    #             task_name = _get_task_name(_t)
    #             _task = task_status.get(task_name, {})
    #             _task[worker] = "active"
    #             task_status[task_name] = _task

    _s = i.reserved()
    if _s and len(_s):
        for worker, task_list in _s.items(): 
            for _t in task_list: 
                task_name = _get_task_name(_t)
                _task = task_status.get(task_name, {})
                _task[worker] = "reserved"
                task_status[task_name] = _task

    return task_status

def is_task_scheduled(store_id, task_name, tasks):
    _task_name = _get_task_unique_name(store_id, task_name)
    if tasks and len(tasks) > 0:
        _t = tasks.get(_task_name, None)
        if _t:
            for worker, status in _t.items():
                if status in ["scheduled", "reserved"]:
                    return True 
    return False

def store_task_executor(task_context, store_id, is_adhoc, task_function, *args):
    task_name = task_context.task
    start_time = int(datetime.datetime.now(datetime.timezone.utc).timestamp())
    # if task_name == "update_skuvault_sales":
    #     logger.error(f"store_id: {store_id}, task: {task_name} is_adhoc: {is_adhoc}, start_time: {start_time}")
    task_db.create_task_log(store_id, task_context.id, task_name, task_context.parent_id, task_context.root_id)
    outcome = False
    message = ""
    try:
        if args:
            task_function(*args)
        else:
            task_function()
        outcome = True
    except Exception as ex:
        outcome = False
        message = "Exception: " + str(traceback.format_exc())
        logger.error(message)
    end_time = int(datetime.datetime.now(datetime.timezone.utc).timestamp())
    task = task_db.end_task(store_id, task_context.id,task_name, (end_time - start_time), outcome, message)
    # if task_name == "update_skuvault_sales":
    #     logger.error(f"{task_name} completed successfully. is_adhoc: {is_adhoc}")
    if not is_adhoc and task:
        interval_seconds = task.get("interval_seconds", -1)
        run_at = task.get("run_at", None)
        # if task_name == "update_skuvault_sales":
        #     logger.error(f"{task_name}:  interval_seconds: {interval_seconds}, run_at: {run_at}")
        if run_at and run_at != "":
            cur_time = datetime.datetime.now()
            next_run = datetime.datetime.strptime(str(cur_time.date()) + ' ' + run_at, '%Y-%m-%d %H:%M:%S')
            if next_run < cur_time:
                next_run = next_run + datetime.timedelta(days=1)
            interval_seconds = (next_run - cur_time).seconds
        task_list =  _get_task_list_from_celery()
        is_task_already_scheduled = is_task_scheduled(store_id, task_name, task_list)
        # if task_name == "update_skuvault_sales":
        #     logger.error(f"{task_name}:  interval_seconds: {interval_seconds}, is_task_already_scheduled: {is_task_already_scheduled}")
        if interval_seconds > 0 and not is_task_already_scheduled:
            from task import celery
            # if task_name == "update_skuvault_sales":
            #     logger.error(f"Rescheduling task: {task_name}, interval_seconds: {interval_seconds}")
            celery.send_task(task_name, args=(store_id,False),countdown=interval_seconds)

    if not outcome:
        email_util.send_job_failed_notification(store_id, task_name, message)
        admin_app_notification_util.generate_admin_app_notification(store_id, admin_app_notification_util.AdminAppNotificationUtil.JOB_FAILED, task_name)

def clear_task_queue():
    print("clear_task_queue")
    redis_cli = redis_util.get_celery_redis_client()
    redis_cli.flushdb()

def register_tasks():

    logger.info("register_tasks")
    print("register_tasks")

    task_list = mongo_db.fetch_store_tasks()

    if not task_list or len(task_list) == 0:
        logger.error("No task found...")
        return
    
    stores = store_util.get_active_stores()

    from task import celery
    tasks = _get_task_list_from_celery()
    
    for store in stores:
        store_tasks = task_list.get(store['id'], None)
        if store_tasks:
            for _id, _task in store_tasks.items():
                is_task_active = _task.get("is_active", False)
                logger.info(f"store id: {store['id']}, task name: {_id}, is_active: {is_task_active}")
                print(f"store id: {store['id']}, task name: {_id}, is_active: {is_task_active}")
                if is_task_active:
                    try:
                        task = copy.deepcopy(_task)
                        task = task_db.create_new_task(store, task) 
                        #logger.error(f"registering task {task['_id']} for store {store['id']}")
                        run_after_registration = False #task.get("run_after_registration", True)
                        interval_seconds = task.get("interval_seconds", -1)
                        run_at = task.get("run_at", None)
                        task_name = _id
                        if not is_task_scheduled(store['id'], task_name, tasks):
                            if run_after_registration:
                                logger.info(f"Running task {task_name}, store id: {store['id']}")
                                print(f"Running task {task_name}, store id: {store['id']}")
                                celery.send_task(task_name, args=(store["id"],False))
                                interval_seconds = -1
                            elif run_at and run_at != "":
                                cur_time = datetime.datetime.now()
                                next_run = datetime.datetime.strptime(str(cur_time.date()) + ' ' + run_at, '%Y-%m-%d %H:%M:%S')
                                if next_run < cur_time:
                                    next_run = next_run + datetime.timedelta(days=1)
                                interval_seconds = (next_run - cur_time).seconds
                            if interval_seconds > 0:
                                logger.info(f"scheduling task {task_name}, store id: {store['id']}, interval_seconds: {interval_seconds}")
                                print(f"scheduling task {task_name}, store id: {store['id']}, interval_seconds: {interval_seconds}")
                                celery.send_task(task_name, args=(store["id"],False), countdown=interval_seconds)
                        else:
                            logger.info(f"{task_name} is already scheduled...")
                            print(f"{task_name} is already scheduled...")
                    except Exception as ex:
                        logger.error(str(traceback.format_exc()))
                        print(str(traceback.format_exc()))


def _revoke_duplicate_tasks(store_id, celery_tasks):
    tasks_map = {}
    from task import celery
    for w, tasks in celery_tasks.items():
        for _task in tasks:
            if "args" in _task and len(_task["args"]) > 0:
                task_store_id = _task["args"][0]
                logger.error(f"store_id: {store_id}, task store_id: {task_store_id}")
                if task_store_id == store_id:
                    task_id = _task['name'] + ":" + task_store_id
                    logger.error(f"task_id: {task_id}")
                    _scheduled_task = tasks_map.get(task_id, None)
                    if not _scheduled_task:
                        tasks_map[task_id] = {
                                            "id": _task["id"],
                                            "task_id": task_id,
                                            "worker": w,
                                            "obj": _task
                                        }
                    else:
                        logger.info(f"Revoking task id: {_task['id']}, task_name: {_task['name']}")
                        celery.control.revoke(_task["id"], terminate=True)

def cancel_duplicate_scheduled_tasks(store_id):
    from task import celery
    r = celery.control.inspect()
    reserved_tasks = r.reserved()
    scheduled_tasks = r.scheduled()
    worker_tasks = {}
    for w, tasks in reserved_tasks.items():
        w_tasks = worker_tasks.get(w, [])
        w_tasks.extend(tasks)
        worker_tasks[w] = w_tasks
    for w, tasks in scheduled_tasks.items():
        w_tasks = worker_tasks.get(w, [])
        w_tasks.extend(tasks)
        worker_tasks[w] = w_tasks
    _revoke_duplicate_tasks(store_id, worker_tasks)


def cancel_store_duplicate_scheduled_tasks():
    try:
        stores = store_util.get_active_stores()
        for store in stores:
            cancel_duplicate_scheduled_tasks(store['id'])
    except Exception as ex:
        outcome = False
        message = "Exception: " + str(traceback.format_exc())
        logger.info(message)


class TaskRunner(threading.Thread):
     
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            logger.info('Creating the TaskRunner')
            print("Creating the TaskRunner")
            cls._instance = super(TaskRunner, cls).__new__(cls)
        return cls._instance
     
    def __init__(self):
         super(TaskRunner, self).__init__()
         self.is_terminated = False
 
    def run(self):
        try:
            logger.info("Registering tasks 1...")
            print("Registering tasks 1...")
            time.sleep(10)
            clear_task_queue()
            logger.info("Registering tasks...")
            print("Registering tasks...")
            register_tasks()
            logger.info("Tasks registered...")
            print("Tasks registered...")
        except Exception as ex:
            message = "TaskRunner Exception: " + str(traceback.format_exc())
            logger.info(message)
            print(message)