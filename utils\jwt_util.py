import string
import secrets
from jwt import encode
import jwt
import logging
import traceback

logger = logging.getLogger()

alphabet = string.ascii_letters + string.digits

def secret_generator(n=64):
    return ''.join(secrets.choice(alphabet) for i in range(n))

def encode_jwt(payload, secret, jwt_token_algorithm='HS256'):
    encode(payload, secret, algorithm=jwt_token_algorithm)

def validate_jwt(jwt_token, client_id, secret):
    payload = None
    try:
        if jwt_token:
            tokenAlgo = jwt.get_unverified_header(jwt_token)['alg']
            jwtAlgoList = []
            if tokenAlgo:
                jwtAlgo = tokenAlgo
            
            jwtAlgoList.append(jwtAlgo)
            payload = jwt.decode(jwt=jwt_token, key=secret, algorithms=jwtAlgoList)
        else:
            raise Exception("Authorization header is empty")
    except Exception as e:
        logger.error(str(traceback.format_exc()))
        raise Exception("Unauthorized!!")

    return payload