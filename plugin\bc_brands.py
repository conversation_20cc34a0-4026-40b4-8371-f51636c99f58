from graphql import brands_query
from mongo_db import catalog_db
import plugin

def process_brand(node):
    node["_id"] = node["entityId"]
    del node["entityId"]
    return node

def fetch_brands_graphql(store):
    return plugin.fetch_all_with_pagination(store=store, query_builder=brands_query, page_size=1000, 
            resource_name="brands", resource_processor=process_brand, 
            db_collection = catalog_db.BRANDS_COLLECTION)

def fetch_brands_rest_api(store):
    query_params = {
        "include_fields": "name,page_title,meta_keywords,meta_description,image_url,custom_url",
        "limit": 0,
        "page": 250
    }
    api = "v3/catalog/brands"
    return plugin.fetch_all_by_rest_api(store, api, limit_per_req=250, query_params=query_params, 
            db_collection=catalog_db.BRANDS_COLLECTION, db_process_threshold=250)

def fetch_all_brands(store):
    return fetch_brands_rest_api(store)