from threading import Lock
from celery import Celery, current_task
from celery.signals import worker_ready
import logging
from pg_db_utils import bulk_orders_util, pg_replenishment_util, pg_skuvault_sales_util, profitability_reports_util
from pg_db_utils import pg_order_util
import project_manager
from task import task_helper, catalog_tasks, content_tasks
from utils import customer_product_price_util, redis_util, product_util, cart_util, gsheet_util, email_util, customer_util, order_util, notification_util, price_list_util, admin_app_notification_util, liquidated_products_util, project_notification_util, bulk_order_notification_util
from config import appconfig
import mongo_db
import salesforce
import pg_db_utils
from pg_db_utils import pg_analytics_util, pg_customer_util, pg_product_util, pg_order_util, pg_skuvault_util, customer_activities_util, pg_replenishment_reserved_util
import rule_engine
import traceback
from plugin import bc_product, bc_products
from project_manager import custom_field_mapping
from zoho import expiry_date_util

logger = logging.getLogger()

log_purge_period = 2*24*60

task_list = [
    {
        "label": "Terminate Idle DB Connection",
        "name": "terminate_idle_db_connection",
        "description": "Terminate Idle DB Connection",
        "interval_seconds": 600,
        "run_after_registration": False
    },
    {
        "label": "Bigcommerce Webhook Product Updated",
        "name": "webhook_product_updated",
        "description": "Task to execute webhook product updates",
        "interval_seconds": 10
    },
    {
        "label": "Bigcommerce Webhook Product inventory Updated",
        "name": "webhook_product_inventory_updated",
        "description": "Task to execute webhook product inventory updates",
        "interval_seconds": -1
    },
    {
        "label": "Bigcommerce Webhook Customer Updated",
        "name": "webhook_customer_updated",
        "description": "Task to execute webhook customer updates",
        "interval_seconds": 10,
    },
    {
        "label": "Bigcommerce Webhook Order Created",
        "name": "webhook_order_created",
        "description": "Task to execute webhook order created",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Bigcommerce Webhook Order Updated",
        "name": "webhook_order_updated",
        "description": "Task to execute webhook order updated",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Sync Custom Price Mapping",
        "name": "customer_product_mapping",
        "description": "Task to fetch custom pricing from googlesheet",
        "interval_seconds": 300,
        "run_after_registration": False
    },
    {
        "label": "Delete Bigcommerce Abandoned Carts",
        "name": "delete_bc_cart_task",
        "description": "Task to delete bigcommerce abandoned carts",
        "interval_seconds": 60,
        "run_after_registration": False
    },
    {
        "label": "Purge Task Log",
        "name": "purge_task_log",
        "description": "Task to purge task log",
        "interval_seconds": 24 * 3600,
        "is_store_task": False
    },
    {
        "label": "Sync Home Page Product Listing Cache",
        "name": "update_product_card_cache",
        "description": "Task to update Home Page Product Listing Cache",
        "interval_seconds": 3600
    },
    {
        "label": "Sync Bigcommerce Data",
        "name": "build_store",
        "description": "Task to fetch All Data From Bigcommerce",
        "interval_seconds": -1,
        "run_after_registration": False,
        "run_at": "08:00:00"
    },
    {
        "label": "Sync Bigcommerce Customer Groups",
        "name": "update_customer_groups",
        "description": "Task to fetch All Customer Groups From Bigcommerce",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Sync Bigcommerce Customers",
        "name": "update_customers",
        "description": "Task to fetch All Customers From Bigcommerce",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Sync Bigcommerce Price List",
        "name": "update_price_list_assignment",
        "description": "Task to sync Price List From Bigcommerce",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Sync Bigcommerce Brands",
        "name": "update_brands",
        "description": "Task to sync Brands From Bigcommerce",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Sync Bigcommerce Categories",
        "name": "update_categories",
        "description": "Task to sync Categories From Bigcommerce",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Sync Bigcommerce Products",
        "name": "update_products",
        "description": "Task to sync Products From Bigcommerce",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Sync Bigcommerce Webpages",
        "name": "update_webpages",
        "description": "Task to sync Webpages From Bigcommerce",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Send Customer Rest Password Email",
        "name": "send_reset_password_email",
        "description": "Task to send reset password to customer email",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Send New Account Email",
        "name": "send_new_account_email",
        "description": "Task to send new account link to customer email",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Salesforce Update Customer Rep",
        "name": "update_salesforce_data",
        "description": "Task to update salesforce customer rep",
        "interval_seconds": 3600,
        "run_after_registration": False
    },
    {
        "label": "Sync Bigcommerce Redirects",
        "name": "update_bc_redirects",
        "description": "Task to update redirects from bigcommerce",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Update Analytics Product Table",
        "name": "update_pg_products",
        "description": "Task to update product table for analytics",
        "interval_seconds": 3600,
        "run_after_registration": True
    },
    {
        "label": "Update Analytics Skuvault Table",
        "name": "update_pg_skuvault_catalog",
        "description": "Task to update skuvault table for analytics",
        "interval_seconds": 1800,
        "run_after_registration": True
    },
    {
        "label": "Update Analytics Order Table",
        "name": "update_pg_orders",
        "description": "Task to update orders table for analytics",
        "interval_seconds": 600,
        "run_after_registration": True
    },
    {
        "label": "Update Analytics Customer Table",
        "name": "update_pg_customers",
        "description": "Task to update customers table for analytics",
        "interval_seconds": 60,
        "run_after_registration": True
    },
    {
        "label": "Update Analytics Table",
        "name": "update_analytics_tables",
        "description": "Task to update analytics table for replenishment",
        "interval_seconds": 4000,
        "run_after_registration": False
    },
    {
        "label": "Run rule engine",
        "name": "run_rule_engine",
        "description": "Run rule engine explicitly.",
        "interval_seconds": 300,
        "run_after_registration": True
    },
    {
        "label": "No order notification",
        "name": "no_order_notification",
        "description": "No order notification task.",
        "interval_seconds": 300,
        "run_after_registration": True
    },
    {
        "label": "Store system logs check",
        "name": "store_system_logs_check",
        "description": "Store system logs check task.",
        "interval_seconds": 900,
        "run_after_registration": True
    },
    {
        "label": "Twillio notification check",
        "name": "twillio_notification_check",
        "description": "Twillio notification check task.",
        "interval_seconds": -1,
        "run_after_registration": False,
        "run_at": "15:00:00"
    },
    {
        "label": "Cancel Duplicate Tasks",
        "name": "cancel_stores_duplicate_tasks",
        "description": "Cancel Duplicate Tasks.",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Update reserved product log table for replenishment",
        "name": "update_reserved_product_log_table",
        "description": "Task to update reserved product log table for replenishment",
        "interval_seconds": 3600,
        "run_after_registration": False
    },
    {
        "label": "Update blocked order's line item available quantity",
        "name": "update_blocked_order_lineitem",
        "description": "Task to release blocked order's line item blocked qty and mark it as available.",
        "interval_seconds": 3600,
        "run_after_registration": False
    },
    {
        "label": "Send replenishment csv file into mail",
        "name": "send_replenishment_csv_mail",
        "description": "Task to send replenishment csv file into mail.",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Send replenishment dashboard csv file into mail",
        "name": "send_replenishment_dashboard_csv_mail",
        "description": "Task to send replenishment dashboard csv file into mail.",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Safety stock check",
        "name": "safety_stock_check",
        "description": "Task to check safety stock and send notification.",
        "interval_seconds": -1,
        "run_after_registration": False,
        "run_at": "13:00:00"
    },
    {
        "label": "Create product in background in Bigcommerce",
        "name": "create_product_in_background",
        "description": "Task to Create product in background in Bigcommerce.",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Sync Price list from BigCommerce to Admin app",
        "name": "sync_price_lists_from_bigcommerce_to_admin_app",
        "description": "Task to Sync Price list from BigCommerce to Admin app.",
        "interval_seconds": -1,
        "run_after_registration": False,
        "run_at": "13:00:00"
    },
    {
        "label": "Update Variants Shipping Groups Table",
        "name": "update_variants_shipping_groups",
        "description": "Task to update variants shipping groups table.",
        "interval_seconds": 3600,
        "run_after_registration": False
    },
    {
        "label": "Update NY Resctricted Zip Codes Table",
        "name": "update_ny_restricted_zip_codes",
        "description": "Task to update ny restricted zip codes table.",
        "interval_seconds": 3600,
        "run_after_registration": False
    },
       {
        "label": "Bigcommerce Webhook Product Variants Metafields",
        "name": "webhook_product_variant_metafields",
        "description": "Task to execute webhook product variant metafields.",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Bigcommerce Webhook Product Metafields",
        "name": "webhook_product_metafields",
        "description": "Task to execute webhook product metafields.",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Project module tickets create",
        "name": "create_ticket",
        "description": "Task to create ticket in project for associated module.",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Project module tickets update",
        "name": "update_ticket",
        "description": "Task to update ticket in project for associated module.",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Project module resources update",
        "name": "update_resource",
        "description": "Task to update resource when ticket is updated in project for associated module.",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Update Product Price List",
        "name": "webhook_product_pricelist",
        "description": "Task to update product price list.",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Update Resource value using custom field",
        "name": "update_custom_field_resource",
        "description": "Task to update resource value using custom field which is mapped to resource.",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Update Custom Field value",
        "name": "update_custom_field",
        "description": "Task to update custom field value using resource which is mapped to custom field.",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Update products price using price list rules",
        "name": "update_price_list_rule",
        "description": "Task to update products price using price list rules.",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Generate admin app notification",
        "name": "generate_admin_app_notification",
        "description": "Task to Generate admin app notification.",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Send product customer tracking report into mail",
        "name": "send_product_customer_tracking_mail",
        "description": "Task to send product customer tracking report into mail.",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Remove old notifications (30 days)",
        "name": "remove_old_notifications",
        "description": "Task to remove old notifications (30 days).",
        "interval_seconds": -1,
        "run_after_registration": False,
        "run_at": "13:00:00"
    },
    {
        "label": "Send liquidation report csv file into mail",
        "name": "send_liquidated_products_csv_mail",
        "description": "Task to send liquidation report csv file into mail.",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Send Webhook inactive notification",
        "name": "send_webhook_inactive_notification",
        "description": "Task to send webhook inactive notification.",
        "interval_seconds": 600,
        "run_after_registration": False
    },
    {
        "label": "Sync Customer specific product price mapping",
        "name": "customer_price_mapping",
        "description": "Task to fetch customer specific product price mapping from googlesheet.",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Clear orders ticket from project mapping",
        "name": "clear_orders_ticket_from_project_mapping",
        "description": "Task to clear completed orders ticket from project mapping.",
        "interval_seconds": 86400,
        "run_after_registration": False
    },
    {
        "label": "Update Multiple Product Price List",
        "name": "webhook_multiple_product_pricelist",
        "description": "Task to update multiple product price list.",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Update Price List from CSV",
        "name": "update_price_list_from_csv",
        "description": "Task to update price list from csv.",
        "interval_seconds": -1,
        "run_after_registration": False
    },
    {
        "label": "Export Price List CSV",
        "name": "price_list_csv_export",
        "description": "Task to export price list csv.",
        "interval_seconds": -1,
        "run_after_registration": False
    }
    # {
    #     "label": "Update variant",
    #     "name": "webhook_variant_update",
    #     "description": "Task to update variant.",
    #     "interval_seconds": -1,
    #     "run_after_registration": False
    # }
]

class TaskManager:
    _instance = None
    _task_runner = None
    _lock = None

    def __new__(cls):
        if cls._instance is None:
            logger.info('Creating the TaskManager')
            cls._lock = Lock()
            cls._instance = super(TaskManager, cls).__new__(cls)
            
        return cls._instance
    
    def __init__(self):
        print("Entering TaskManager")
        logger.info("Entering TaskManager")
        try:
            task_helper.clear_task_queue()
            broker_url = appconfig.get_celery_broker_url()
            logger.info(f"broker_url: {broker_url}")
            print(f"broker_url: {broker_url}")
            self._CELERY = Celery(appconfig.get_celery_app_name(), 
                                broker=broker_url, 
                                backend=broker_url,
                                task_track_started=True,
                                broker_connection_retry_on_startup=False,
                                visibility_timeout=172800,
                                broker_transport_options={'visibility_timeout': 172800},
                                result_backend_transport_options={'visibility_timeout': 172800})
            app_env = appconfig.get_app_env()
            logger.info(f"APP_ENV: {app_env}")
            print(f"APP_ENV: {app_env}")
            if app_env == 'Production':
                TaskManager._task_runner = task_helper.TaskRunner()
                TaskManager._task_runner.start() 
        except Exception as ex:
            print(str(traceback.format_exc()))

        logger.info("Exiting TaskManager")
        print("Exiting TaskManager")

    def get_celery(self):
        return self._CELERY

    def submit_task(self, task_name, args=(), queue=None):
        task = None
        if queue:
            task = self._CELERY.send_task(task_name, args, queue=queue)
        else:
            task = self._CELERY.send_task(task_name, args)

        return task

_TASK_MANAGER = TaskManager()

celery = _TASK_MANAGER.get_celery()

@worker_ready.connect
def at_start(sender, **k):
    with sender.app.connection() as conn:
        logger.info("at_start")
        print("at_start")
        #sender.app.send_task("register_store_tasks")
        #sender.app.send_task('webhook_product_updated')
        #sender.app.send_task('webhook_customer_updated')

@celery.on_after_configure.connect
def setup_periodic_tasks(sender, **kwargs):   
    pass 
    #sender.add_periodic_task(60, register_store_tasks.s(), name='register_store_tasks')
    #sender.add_periodic_task(300, cancel_stores_duplicate_tasks.s(), name='cancel_stores_duplicate_tasks')
    #sender.add_periodic_task(1800, update_all_stores_cache.s(), name='update_all_stores_cache')

def submit_task(task_name, args=(), queue=None):
    _TASK_MANAGER.submit_task(task_name, args, queue)

@celery.task(name ="clear_task_queue")
def clear_task_queue():
    task_helper.clear_task_queue()

@celery.task(name ="register_store_tasks")
def register_store_tasks():
    logger.info("Start register_store_tasks")
    print("Start register_store_tasks")
    #task_helper.TaskRunner().start()  
    print("End register_store_tasks")
    logger.info("End register_store_tasks")  

#################################################################################################################
##  Notification Tasks Start 
#################################################################################################################
    
@celery.task(name ="twillio_notification_check")
def twillio_notification_check(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, notification_util.twillio_notification_check, store_id)

@celery.task(name ="no_order_notification")
def no_order_notification(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, notification_util.no_order_check, store_id)

@celery.task(name ="store_system_logs_check")
def store_system_logs_check(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, notification_util.payment_error_check, store_id)


#################################################################################################################
##  Notification Tasks End 
#################################################################################################################


#################################################################################################################
##  Webhook Tasks Start 
#################################################################################################################
@celery.task(name ="webhook_product_updated")
def webhook_product_updated(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, product_util.process_webhook_product_updates, store_id)

@celery.task(name ="webhook_product_inventory_updated")
def webhook_product_inventory_updated(store_id, product_id, variant_id, method, value, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, product_util.process_webhook_product_inventory_updates, store_id, product_id, variant_id, method, value)

@celery.task(name ="webhook_customer_updated")
def webhook_customer_updated(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, customer_util.process_webhook_customer_updates, store_id)

@celery.task(name ="webhook_order_created")
def webhook_order_created(store_id, payload, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, order_util.process_order_created_webhook, store_id, payload)

@celery.task(name ="webhook_order_updated")
def webhook_order_updated(store_id, payload, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, order_util.process_order_updated_webhook, store_id, payload)

@celery.task(name ="update_order_audit_report")
def update_order_audit_report(store_id, order_id, customer_id, order_total, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, order_util.update_order_audit_report, store_id, order_id, customer_id, order_total)

@celery.task(name ="refresh_all_order_audits")
def refresh_all_order_audits(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, order_util.refresh_all_order_audits, store_id)

@celery.task(name ="webhook_product_variant_metafields")
def webhook_product_variant_metafields(store_id, metafield_id, variant_id, operation, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_product_util.process_variants_shipping_groups_webhook, store_id, metafield_id, variant_id, operation)

@celery.task(name ="webhook_product_metafields")
def webhook_product_metafields(store_id, metafield_id, product_id, operation, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_product_util.process_products_shipping_groups_webhook, store_id, metafield_id, product_id, operation)

@celery.task(name ="create_ticket")
def create_ticket(store_id, payload, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, project_manager.create_ticket, store_id, payload)

@celery.task(name ="update_ticket")
def update_ticket(store_id, payload, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, project_manager.update_ticket, store_id, payload)

@celery.task(name ="update_resource")
def update_resource(store_id, payload, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, project_manager.update_resource_from_ticket, store_id, payload)

@celery.task(name ="update_custom_field_resource")
def update_custom_field_resource(store_id, payload, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, custom_field_mapping.update_record_using_custom_field, store_id, payload)

@celery.task(name ="update_custom_field")
def update_custom_field(store_id, payload, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, custom_field_mapping.update_custom_field_using_resource, store_id, payload)

@celery.task(name ="webhook_product_pricelist")
def webhook_product_pricelist(store_id, price_list_id, variant_id, operation, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, price_list_util.process_product_price_list_webhook, store_id, price_list_id, variant_id, operation)

@celery.task(name ="webhook_multiple_product_pricelist")
def webhook_multiple_product_pricelist(store_id, payload, operation, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, price_list_util.process_multiple_product_price_list_webhook, store_id, payload, operation)

# @celery.task(name ="webhook_variant_update")
# def webhook_variant_update(store_id, product_id, variant_id, adhoc=True):
#     task_helper.store_task_executor(current_task.request, store_id, adhoc, product_util.process_variant_update_webhook, store_id, product_id, variant_id)


#################################################################################################################
##  Webhook Tasks End 
#################################################################################################################

@celery.task(name ="customer_product_mapping")
def customer_product_mapping(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, gsheet_util.fetch_mapping_data, store_id)

@celery.task(name ="delete_bc_cart_task")
def delete_bc_cart_task(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, cart_util.delete_abandoned_bc_cart, store_id)

@celery.task(name ="purge_task_log")
def purge_task_log(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, task_helper.purge_task_log, store_id, log_purge_period)

@celery.task(name ="update_product_card_cache")
def update_product_card_cache(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, catalog_tasks.update_product_cache, store_id)

@celery.task(name ="build_store")
def build_store(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, task_helper.build_store, store_id, adhoc)

@celery.task(name ="update_customer_groups")
def update_customer_groups(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, customer_util.update_all_customer_groups, store_id)

@celery.task(name ="update_customers")
def update_customers(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, customer_util.update_all_customers, store_id)

@celery.task(name ="update_price_list_assignment")
def update_price_list_assignment(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, customer_util.update_price_list_assignments, store_id)

@celery.task(name ="update_price_list")
def update_price_list(store_id, price_list_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, customer_util.update_price_list, store_id, price_list_id)

@celery.task(name ="update_brands")
def update_brands(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, catalog_tasks.update_brands, store_id)

@celery.task(name ="update_categories")
def update_categories(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, catalog_tasks.update_categories, store_id)

@celery.task(name ="update_products")
def update_products(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, catalog_tasks.update_products, store_id)

@celery.task(name ="update_products_delta")
def update_products_delta(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, catalog_tasks.periodic_catalog_update, store_id)

@celery.task(name ="update_products_complex_rules")
def update_products_complex_rules(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, catalog_tasks.update_products_complex_rules, store_id)

@celery.task(name ="update_webpages")
def update_webpages(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, content_tasks.update_webpages, store_id)

@celery.task(name ="update_bc_redirects")
def update_bc_redirects(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, content_tasks.update_redirects, store_id)

@celery.task(name ="send_reset_password_email")
def send_reset_password_email(store_id, customer_id):
    task_helper.store_task_executor(current_task.request, store_id, True, email_util.send_reset_password_email, store_id, customer_id)

@celery.task(name ="send_new_account_email")
def send_new_account_email(store_id, email):
    task_helper.store_task_executor(current_task.request, store_id, True, email_util.send_new_account_email, store_id, email)    

@celery.task(name ="flush_cache")
def flush_cache(store_id, adhoc=True):
    redis_util.flush_db(store_id)

@celery.task(name ="upsert_documents")
def upsert_documents(store, collection, documents):
    mongo_db.upsert_documents(store, collection, documents)

@celery.task(name ="update_salesforce_data")
def update_salesforce_data(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, salesforce.update_data, store_id)

@celery.task(name ="update_salesforce_order_details")
def update_salesforce_order_details(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, salesforce.update_salesforce_orders, store_id)

@celery.task(name ="clear_older_salesforce_orders_detail")
def clear_older_salesforce_orders_detail(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, salesforce.clear_older_salesforce_orders, store_id)

@celery.task(name ="update_product_price_list_with_logs")
def update_product_price_list_with_logs(store_id, products, old_data, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, product_util.product_processor_price_list, store_id, products, old_data)

@celery.task(name ="update_price_list_logs")
def update_price_list_logs(store_id, product_id, logs, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, price_list_util.log_price_change, store_id, product_id, logs)

@celery.task(name ="cleanup_msda_pmts_unlinked_products_documents")
def cleanup_msda_pmts_unlinked_products_documents(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, bc_products.remove_msda_pmts_unlinked_products_documents, store_id)

#################################################################################################################
##  Analytics Tasks Start
#################################################################################################################

@celery.task(name ="customer_activity_analytics")
def customer_activity_analytics(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, customer_activities_util.process_customer_activities, store_id)    

@celery.task(name ="cart_inventory_report")
def cart_inventory_report(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, customer_activities_util.process_cart_inventory_report, store_id)    


@celery.task(name ="update_analytics_tables")
def update_analytics_tables(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_analytics_util.refresh_replenishment_tables, store_id)    

@celery.task(name ="update_analytics_daily_sales_tables")
def update_analytics_daily_sales_tables(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_replenishment_util.create_daily_sales_tables, store_id)    

@celery.task(name ="update_all_profitability_related_tables")
def update_all_profitability_related_tables(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_analytics_util.refresh_all_profitability_tables, store_id)    

@celery.task(name ="update_customers_trend_table")
def update_customers_trend_table(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_analytics_util.refresh_customers_trend_table, store_id)    

@celery.task(name ="update_products_revenue_table")
def update_products_revenue_table(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_analytics_util.refresh_products_revenue_table, store_id)    

@celery.task(name ="update_customers_trend_shipping_cost_table")
def update_customers_trend_shipping_cost_table(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_analytics_util.refresh_customers_trend_shipping_cost_table, store_id)    

@celery.task(name ="update_orders_analytics_table")
def update_orders_analytics_table(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_analytics_util.refresh_orders_analytics_table, store_id)

@celery.task(name ="update_profitability_product_customer_groups_table")
def update_profitability_product_customer_groups_table(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_analytics_util.refresh_profitability_product_customer_groups_table, store_id)      

@celery.task(name ="send_replenishment_csv_mail")
def send_replenishment_csv_mail(store_id, query_params, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_replenishment_util.get_replenishment_aggregate_data_csv, store_id, query_params)

@celery.task(name ="daily_sales_replenishment_csv_mail")
def daily_sales_replenishment_csv_mail(store_id, query_params, call_from_time_trigger=False, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_replenishment_util.get_replenishment_daily_sold_aggregate_data_csv, store_id, query_params, call_from_time_trigger)

@celery.task(name ="daily_sales_replenishment_csv_time_trigger")
def daily_sales_replenishment_csv_time_trigger(store_id, query_params={}, call_from_time_trigger=True, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_replenishment_util.get_replenishment_daily_sold_aggregate_data_csv, store_id, query_params, call_from_time_trigger)

@celery.task(name ="no_sold_products_analytics_csv_mail")
def no_sold_products_analytics_csv_mail(store_id, query_params, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_replenishment_util.get_no_sold_products_analytics_data_csv, store_id, query_params)  

@celery.task(name ="send_discontinued_products_data_csv_mail")
def send_discontinued_products_data_csv_mail(store_id, query_params, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_replenishment_util.get_discontinued_products_data_csv, store_id, query_params)  

@celery.task(name ="send_replenishment_dashboard_csv_mail")
def send_replenishment_dashboard_csv_mail(store_id, query_params, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_replenishment_util.get_replenishment_dashboard_aggregate_data_csv, store_id, query_params)  

@celery.task(name ="update_reserved_product_log_table")
def update_reserved_product_log_table(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_replenishment_reserved_util.refresh_replenishment_reserved_table, store_id)    
   
@celery.task(name ="update_pg_skuvault_catalog")
def update_pg_skuvault_catalog(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_skuvault_util.update_sku_vault, store_id)    

@celery.task(name ="update_pg_orders")
def update_pg_orders(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_order_util.increment_update, store_id)    

@celery.task(name ="update_pg_customers")
def update_pg_customers(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_customer_util.incremental_update, store_id)    
   
@celery.task(name ="update_pg_products")
def update_pg_products(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_product_util.update_product_data, store_id)    

@celery.task(name ="update_product_categories_mapping")
def update_product_categories_mapping(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_product_util.update_product_categories_only, store_id)    

@celery.task(name ="process_customer_login")
def process_customer_login(store_id, customer_id, ip_address, timestamp, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_customer_util.process_customer_session_details, store_id, customer_id, ip_address, timestamp)

@celery.task(name ="update_blocked_order_lineitem")
def update_blocked_order_lineitem(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, order_util.process_blocked_order_lineitems, store_id)   

@celery.task(name ="update_variants_shipping_groups")
def update_variants_shipping_groups(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_product_util.update_variants_shipping_groups_table, store_id) 

@celery.task(name ="update_ny_restricted_zip_codes")
def update_ny_restricted_zip_codes(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_order_util.fetch_NY_city_restricted_zip_codes, store_id)  

@celery.task(name ="send_product_customer_tracking_mail")
def send_product_customer_tracking_mail(store_id, query_params, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, bulk_orders_util.get_product_customer_tracking_report_csv, store_id, query_params)    

@celery.task(name ="send_bulk_products_global_report_mail")
def send_bulk_products_global_report_mail(store_id, query_params, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, bulk_orders_util.get_bulk_products_gloabl_report_csv, store_id, query_params)    

@celery.task(name ="send_order_placed_email_to_compliance")
def send_order_placed_email_to_compliance(store_id, order_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, bulk_orders_util.send_order_placed_email_to_compliance_team, store_id, order_id)    

@celery.task(name ="clear_orders_ticket_from_project_mapping")
def clear_orders_ticket_from_project_mapping(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, order_util.clear_orders_ticket_for_project_mapping, store_id)    

@celery.task(name ="update_skuvault_sales")
def update_skuvault_sales(store_id, adhoc=True, days_range=1):
   logger.error("============================update_skuvault_sales==================")
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_skuvault_sales_util.update_sku_vault_sales, store_id, days_range)    

@celery.task(name ="update_skuvault_sales_cost")
def update_skuvault_sales_cost(store_id, payload, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_skuvault_sales_util.update_sku_vault_sales_cost, store_id, payload)    

@celery.task(name ="update_skuvault_purchase_orders")
def update_skuvault_purchase_orders(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_skuvault_sales_util.update_sku_vault_purchase_orders, store_id)    

@celery.task(name ="send_customer_profitability_csv_mail")
def send_customer_profitability_csv_mail(store_id, query_params, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, profitability_reports_util.get_customer_profitability_csv, store_id, query_params)

@celery.task(name ="send_product_wise_profitability_csv_mail")
def send_product_wise_profitability_csv_mail(store_id, query_params, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, profitability_reports_util.get_product_wise_profitability_csv, store_id, query_params)

@celery.task(name ="send_suppliers_profitability_csv_mail")
def send_suppliers_profitability_csv_mail(store_id, query_params, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, profitability_reports_util.get_suppliers_profitability_csv, store_id, query_params)

@celery.task(name ="send_classification_profitability_csv_mail")
def send_classification_profitability_csv_mail(store_id, query_params, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, profitability_reports_util.get_classification_profitability_csv, store_id, query_params)

@celery.task(name ="send_orders_profitability_csv_mail")
def send_orders_profitability_csv_mail(store_id, query_params, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, profitability_reports_util.get_orders_profitability_csv, store_id, query_params)

@celery.task(name ="send_brands_profitability_csv_mail")
def send_brands_profitability_csv_mail(store_id, query_params, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, profitability_reports_util.get_brands_profitability_csv, store_id, query_params)

#################################################################################################################
##  Analytics Tasks End
#################################################################################################################

EXECUTE_RULES_TASK = "execute_rules"
@celery.task(name = EXECUTE_RULES_TASK)
def execute_rules(store, rule_type, payload):
    rule_engine.execute_rules(store, rule_type, payload)

@celery.task(name = "run_rule_engine")
def run_rule_engine(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, rule_engine.run_all_rules, store_id)    
        
@celery.task(name ="cancel_duplicate_tasks")
def cancel_duplicate_tasks(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, task_helper.cancel_duplicate_scheduled_tasks, store_id)    

@celery.task(name ="cancel_stores_duplicate_tasks")
def cancel_stores_duplicate_tasks(adhoc=True):
   task_helper.cancel_store_duplicate_scheduled_tasks()


@celery.task(name ="terminate_idle_db_connection")
def terminate_idle_db_connection(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_db_utils.terminate_idle_connections, store_id)

@celery.task(name ="safety_stock_check")
def safety_stock_check(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, pg_db_utils.safety_stock_check, store_id)    

@celery.task(name ="create_product_in_background")
def create_product_in_background(store_id, id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, bc_product.create_product_in_background, store_id, id)

@celery.task(name ="sync_price_lists_from_bigcommerce_to_admin_app")
def sync_price_lists_in_mongodb(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, price_list_util.sync_price_lists_in_mongodb, store_id)

@celery.task(name ="update_price_list_rule")
def update_price_list_rule(store_id, rule_id, is_normal_sync=False, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, price_list_util.update_price_list_using_rules, store_id, rule_id, is_normal_sync)

@celery.task(name ="price_list_products_logs")
def price_list_products_logs(store_id, product_id, price_logs, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, price_list_util.log_price_change, store_id, product_id, price_logs)

@celery.task(name ="update_price_list_from_csv")
def update_price_list_from_csv(store_id, csv_data, username, filename, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, price_list_util.update_price_list_from_csv, store_id, csv_data, username, filename)

@celery.task(name ="update_multi_store_price_list_from_csv")
def update_multi_store_price_list_from_csv(store_id, csv_data, username, filename, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, price_list_util.update_multi_store_price_list_from_csv, store_id, csv_data, username, filename)

@celery.task(name ="price_list_csv_export")
def price_list_csv_export(store_id, query_params, username, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, price_list_util.price_list_csv_export, store_id, query_params, username)

@celery.task(name ="update_price_list_with_cost_plus_percentage")
def update_price_list_with_cost_plus_percentage(store_id, price_list_id, percentage, is_replace, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, price_list_util.update_price_list_with_cost_plus_percentage, store_id, price_list_id, percentage, is_replace)

@celery.task(name ="migrate_distributor_price_to_vip")
def migrate_distributor_price_to_vip(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, price_list_util.migrate_distributor_price_to_vip, store_id)

@celery.task(name ="sync_skuvault_and_price_lists_data")
def sync_skuvault_and_price_lists_data(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, price_list_util.sync_skuvault_and_price_lists_data, store_id)

# @celery.task(name ="remove_old_price_list_logs")
# def remove_old_price_list_logs(store_id, adhoc=True):
#    task_helper.store_task_executor(current_task.request, store_id, adhoc, price_list_util.remove_old_price_list_logs, store_id)


#################################################################################################################
##  Google Sheet Price List Tasks
#################################################################################################################

@celery.task(name ="sync_google_sheet_price_list")
def sync_google_sheet_price_list(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, price_list_util.sync_google_sheet_price_list, store_id)


#################################################################################################################

@celery.task(name ="update_express_order_table")
def update_express_order_table(store_id, express_orders):
    order_util.update_express_order_table(store_id, express_orders)

@celery.task(name ="customer_price_mapping")
def customer_price_mapping(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, customer_product_price_util.fetch_customer_product_price_mapping, store_id)

@celery.task(name ="insert_customer_price_into_sheet")
def insert_customer_price_into_sheet(store_id, payload, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, customer_product_price_util.insert_customer_product_price, store_id, payload)

@celery.task(name ="update_customer_price_into_sheet")
def update_customer_price_into_sheet(store_id, payload, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, customer_product_price_util.update_customer_product_price, store_id, payload)

@celery.task(name ="delete_customer_price_into_sheet")
def delete_customer_price_into_sheet(store_id, payload, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, customer_product_price_util.delete_customer_product_price_rows, store_id, payload)
       
       
#################################################################################################################
##  Admin app notification tasks
#################################################################################################################

@celery.task(name ="generate_admin_app_notification")
def generate_admin_app_notification(store_id, event_type, entity_id, payload=None, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, admin_app_notification_util.generate_admin_app_notification, store_id, event_type, entity_id, payload)

@celery.task(name ="remove_old_notifications")
def remove_old_notifications(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, admin_app_notification_util.remove_old_notifications, store_id)


#################################################################################################################
##  Product Liquidation report task
#################################################################################################################

@celery.task(name ="send_liquidated_products_csv_mail")
def send_liquidated_products_csv_mail(store_id, query_params, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, liquidated_products_util.get_liquidated_products_csv, store_id, query_params) 


@celery.task(name ="send_webhook_inactive_notification")
def send_webhook_inactive_notification(store_id, adhoc=True):
   task_helper.store_task_executor(current_task.request, store_id, adhoc, admin_app_notification_util.send_webhook_inactive_notification, store_id)

#################################################################################################################
##  Zoho Expiry date task
#################################################################################################################

@celery.task(name ="update_zoho_data")
def update_zoho_data(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, expiry_date_util.get_expiry_dates, store_id)

@celery.task(name ="update_zoho_returns_data")
def update_zoho_returns_data(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, expiry_date_util.update_zoho_returns_data, store_id)


#################################################################################################################
##  Project Notification task
#################################################################################################################

@celery.task(name ="send_project_notification")
def send_project_notification(store_id, event_type, entity_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, project_notification_util.send_project_notification, store_id, event_type, entity_id)


#################################################################################################################
##  Check Promo products and create ticket task
#################################################################################################################

@celery.task(name ="check_promo_product_and_create_ticket")
def check_promo_product_and_create_ticket(store_id, product_ids, old_data_map, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, price_list_util.check_promo_product_and_create_ticket, store_id, product_ids, old_data_map)

#################################################################################################################
##  Bulk order Notification task
#################################################################################################################

@celery.task(name ="send_bulk_order_notification")
def send_bulk_order_notification(store_id, payload, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, bulk_order_notification_util.send_bulk_order_notification, store_id, payload)


@celery.task(name ="send_bulk_order_reminder_email_to_reps")
def send_bulk_order_reminder_email_to_reps(store_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, bulk_order_notification_util.send_bulk_order_reminder_email_to_reps, store_id)

@celery.task(name ="send_bulk_order_updated_notification")
def send_bulk_order_updated_notification(store_id, order_id, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, bulk_order_notification_util.send_bulk_order_updated_notification, store_id, order_id)

@celery.task(name ="update_order_notes")
def update_order_notes(store_id, order_id, username, customer_id, normal_order=False, adhoc=True):
    task_helper.store_task_executor(current_task.request, store_id, adhoc, bulk_order_notification_util.update_order_notes, store_id, order_id, username, customer_id, normal_order)