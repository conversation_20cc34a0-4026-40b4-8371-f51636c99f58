import gspread
from oauth2client.service_account import ServiceAccountCredentials
from utils import redis_util, store_util
import logging
import traceback

logger = logging.getLogger()


gs_token = **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

sheetId = '1xNeP2pir_ypJmX7eieb--aH-KUzwMOHi6apvUB0f6RU'

def get_gsclient():
    scopes = ['https://spreadsheets.google.com/feeds','https://www.googleapis.com/auth/drive']
    creds = ServiceAccountCredentials.from_json_keyfile_dict(gs_token, scopes)
    return gspread.authorize(creds)

def fetch_mapping_data(store_id):
    store = store_util.get_store_by_id(store_id)
    if not store:
        return
    client = get_gsclient()
    sheet = client.open_by_key(sheetId)
    mapping_sheet = sheet.worksheet('Mapping')
    data = mapping_sheet.get_all_records()
    mapping_data = {}
    customers = {}
    products = {}
    cust_prod_detail = []
    for row in data:
        row_data = ["","","","",""]
        if 'Customer ID' in row and row['Customer ID'] and 'Product ID' in row \
                and row['Product ID']:
            try:
                customer_id = str(row['Customer ID'])
                product_id = str(row['Product ID'])
                price = None
                if 'Price' in row and row['Price']:
                    price = float(row['Price'])
                    if not customer_id in mapping_data:
                        mapping_data[customer_id] = {}
                    if price > 0:
                        mapping_data[customer_id][product_id] = price
            except Exception as ex:
                logger.error(str(traceback.format_exc()))

    redis_util.update_customer_price_mapping(store_id, mapping_data)