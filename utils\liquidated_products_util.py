from mongo_db import store_db
import logging
import datetime
from utils import email_util
import csv
import io
import pytz
from mongo_db import fetch_one_document_from_admin_collection, fetchall_documents_from_storefront_collection, fetch_one_document_from_storefront_collection

logger = logging.getLogger()



def get_liquidated_products_csv(store_id, query_params):
    product_ids = query_params.get('product_ids', None)
    if product_ids and isinstance(product_ids, str):
        product_ids = [int(pid.strip()) for pid in product_ids.split(',') if pid.strip().isdigit()]

    user_details = store_db.fetch_user_by_username(store_id, query_params['username'])
    user_name = user_details['name'] if 'name' in user_details and user_details else ''

    if not product_ids or product_ids == []:
        # Step 1: Fetch liquidated product IDs from the store_info collection
        store_info_query = {"type": "liquidated_products"}
        store_info_projection = {"product_ids": 1}
        store_info_record = fetch_one_document_from_admin_collection(
            store_id=store_id,
            collection_name="store_info",
            query=store_info_query,
            projection=store_info_projection
        )

        if not store_info_record or "product_ids" not in store_info_record:
            return {
                "data": [],
                "total_count": 0,
                "message": "No liquidated products found."
            }

        product_ids = store_info_record["product_ids"]
        if not product_ids:
            return {
                "data": [],
                "total_count": 0,
                "message": "No liquidated products found."
            }

    # Step 2: Fetch products data based on the retrieved product IDs
    products_query = {"_id": {"$in": product_ids}}
    products_projection = {
        "id": 1, "name": 1, "sku": 1, "price": 1, "calculated_price": 1, "inventory_level": 1, "variants": 1, "custom_fields": 1
    }
    products_data = fetchall_documents_from_storefront_collection(
        store_id=store_id,
        collection_name="products",
        query=products_query,
        projection=products_projection
    )
    products = list(products_data)
    
    # Step 3: Process products with inline logic
    processed_products = []
    for product in products:
        # Fetch latest new_price from LIQUIDATE_PRODUCT_PRICE_LOGS
        price_log_query = {"product_id": product["_id"]}
        price_log_projection = {"log_history": 1}
        price_log_record = fetch_one_document_from_storefront_collection(
            store_id=store_id,
            collection_name="liquidate_product_price_logs",
            query=price_log_query,
            projection=price_log_projection
        )

        latest_price = None
        if price_log_record and "log_history" in price_log_record:
            log_history = price_log_record["log_history"]
            latest_log = max(log_history, key=lambda log: log.get("updated_at", 0), default=None)
            if latest_log and "new_price" in latest_log:
                latest_price = latest_log["new_price"]

        # Extract `pack_count` from `custom_field`
        custom_fields = product.get("custom_fields", [])
        # print(custom_fields)
        pack_count = next(
            (
                int(field["value"]) 
                for field in custom_fields 
                if field["name"].lower().strip() == "pack count" and field["value"].isdigit()
            ),
            None
        )
        # print(pack_count)

        # Variant-level calculations
        variants_info = []
        for variant in product.get("variants", []):
            inventory_level = variant.get("inventory_level", 0)
            variant_pack_count = pack_count  # Use the same `pack_count` for variants

            piece_count = inventory_level * variant_pack_count if variant_pack_count else None

            price_per_piece = (
                latest_price
                if latest_price is not None and latest_price != ""
                # else (variant.get("price", 0) / variant_pack_count if variant_pack_count else None)
                else (
                    (variant.get("calculated_price", 0) / variant_pack_count)
                    if variant_pack_count is not None and variant.get("calculated_price", 0) else 0
                )
            )

            option_values = variant.get("option_values", [])
            variant_name = " ".join(option.get("label", "") for option in option_values) if option_values else ""

            variants_info.append({
                # "id": variant.get("id"),
                "Variant Name": variant_name,
                "SKU": variant.get("sku"),
                "Inventory Packs": inventory_level,
                "Pack Count": variant_pack_count,
                "Piece Count": piece_count,
                "Price Per Piece": round(price_per_piece, 2),
                # "Calculated Price": variant.get("calculated_price", 0)
            })

        # Parent-level calculations
        inventory_level = product.get("inventory_level", 0)
        piece_count = inventory_level * pack_count if pack_count else None
        
        price_per_piece = (
            latest_price
            if latest_price is not None and latest_price != ""
            # else (product.get("price", 0) / pack_count if pack_count else None)
            else (
                (product.get("calculated_price", 0) / pack_count)
                if pack_count is not None and product.get("calculated_price", 0) else 0
            )
        )
        processed_products.append({
            "Product Name": product.get("name"),
            "SKU": product.get("sku"),
            "Variant Name": "",
            "Variant Count": len(product.get("variants", [])),
            "Inventory Packs": inventory_level,
            "Pack Count": pack_count,
            "Piece Count": piece_count,
            "Price Per Piece": round(price_per_piece, 2),
            "variants_info": variants_info,
        })

    processed_products.sort(key=lambda x: x["Product Name"])

    # Create CSV content in memory
    if processed_products:
        utc_now = datetime.datetime.now(pytz.utc)
        cst_now = utc_now.astimezone(pytz.timezone("America/Chicago"))
        created_date = cst_now.strftime("%m-%d-%Y_%H:%M")
        file_name = f"Replenishment_{created_date}.csv"
        csv_content = io.StringIO()

        parent_keys = [
            "Product Name", "SKU", "Variant Name", "Variant Count",
            "Inventory Packs", "Pack Count",
            "Piece Count", "Price Per Piece"
        ]

        writer = csv.DictWriter(csv_content, fieldnames=parent_keys)
        writer.writeheader()

        for row in processed_products:
            parent_row = {key: row[key] for key in parent_keys}
            writer.writerow(parent_row)
            for variant in row.get("variants_info", []):
                variant_row = {
                    **{key: row[key] if key == "Product Name" else '' for key in parent_keys}, 
                    **variant
                }
                writer.writerow(variant_row)

        email_util.send_liquidated_products_csv_data_email(
            store_id, csv_content, query_params["username"], file_name, user_name, created_date
        )