from plugin import bc_cart
from utils import store_util, redis_util
from pg_db import customers_db
import pg_db
from mongo_db import cart_db, fetchall_documents_from_storefront_collection
import logging
import traceback
import datetime
from sqlalchemy.exc import IntegrityError

logger = logging.getLogger()

def _build_customer_activity_model(customer_activity):
    activity_time = datetime.datetime.fromtimestamp(customer_activity['last_activity_timestamp'])
    bc_cart_id = customer_activity.get("bc_cart_id", None)
    return customers_db.CustomerCarts(
        customer_id=int(customer_activity['customer_id']),
        channel=customer_activity['channel'],
        bc_cart_id=bc_cart_id,
        cart_line_items=customer_activity['cart_line_items'],
        cart_value=customer_activity['cart_value'],
        total_quantity=customer_activity['total_quantity'],
        last_activity_timestamp=activity_time
    )

def _persist_activity_data(store_id, activity_models):
    session = pg_db.get_session(store_id)
    try:
        with session.begin():
            for model in activity_models:
                try:
                    session.merge(model)
                except Exception as ex:
                    logger.error(traceback.format_exc())
    except Exception as ex:
            logger.error(traceback.format_exc())
    finally:
        session.commit()
        session.close()

def _build_customer_cart_model(customer_id, channel, line_item_count, cart_value, total_quantities, activity_timestamp, cart_id=None):
    customer_activity = {
        "customer_id": customer_id,
        "channel": channel,
        "cart_line_items": line_item_count,
        "cart_value": cart_value,
        "total_quantity": total_quantities,
        "last_activity_timestamp": activity_timestamp,
        "bc_cart_id": cart_id
    }

    return _build_customer_activity_model(customer_activity)

def _fetch_cart(store, customer_ids):
    fields = {
                "customer_id": 1,
                "line_items": 1,
                "status": 1
    }
    query = {
        "customer_id": {"$in": customer_ids}
    }
    return cart_db.fetch_carts(store, query, fields)

def get_bc_cart(store, bc_cart_id):
    result = None
    res = bc_cart.fetch_cart(store, bc_cart_id)
    if res.status_code < 299:
        result = res.json()['data']
    return result

def process_express_customer_activities(store_id):
    store = store_util.get_store_by_id(store_id)
    if store:
        channel = "express"
        customer_activities = redis_util.get_customer_activities(store_id, channel)
        if customer_activities and len(customer_activities) > 0:
            customer_ids = []
            for customer_id in customer_activities.keys():
                customer_ids.append(int(customer_id))
            
            processed_customers = {}
            models = []
            carts = _fetch_cart(store, customer_ids)
            if carts:
                for cart in carts:
                    if cart["status"] == "active":
                        customer_id = cart["customer_id"]
                        activity = customer_activities[str(customer_id)]
                        line_items = cart.get("line_items", {})
                        line_item_count = len(line_items)
                        total_quantities = 0
                        cart_value = 0
                        for key, line_item in line_items.items():
                            sale_price = line_item.get("sale_price", 0)
                            quantity = line_item.get("quantity", 0)
                            cart_value += (sale_price * quantity)
                            total_quantities += quantity
                        models.append(_build_customer_cart_model(customer_id=customer_id, channel=channel, line_item_count=line_item_count, cart_value=cart_value, total_quantities=total_quantities, activity_timestamp=activity['timestamp']))
                        processed_customers[str(customer_id)] = True

            for customer_id, customer_activity in customer_activities.items():
                if not str(customer_id) in processed_customers:
                    models.append(_build_customer_cart_model(customer_id=int(customer_id), channel=channel, line_item_count=0, cart_value=0, total_quantities=0, activity_timestamp=customer_activity['timestamp']))
                    processed_customers[str(customer_id)] = True
            _persist_activity_data(store_id, models)

            redis_util.get_customer_activities(store_id, channel, clear=True)
    else:
        logger.error(f"customer_activities.process_customer_activities: {store_id} store doesn't exist.")


def process_bc_storefront_customer_activities(store_id):
    store = store_util.get_store_by_id(store_id)
    if store:
        customer_activities = redis_util.get_customer_activities(store_id, "main")

        if customer_activities and len(customer_activities) > 0:
            customer_ids = []
            cart_ids = []
            for customer_id, customer_activity in customer_activities.items():
                customer_ids.append(int(customer_id))
                cart_ids.append(str(customer_activity['cart_id']))
            
            processed_customers = {}
            models = []
            if cart_ids:
                for cart_id in cart_ids:
                    cart = get_bc_cart(store, cart_id)
                    if cart:
                        customer_id = cart["customer_id"]
                        if customer_id is None or str(customer_id) not in customer_activities:
                            logger.warning(f"Customer ID {customer_id} from cart {cart_id} not found in customer activities.")
                            continue

                        activity = customer_activities.get(str(customer_id))
                        line_items = cart.get("line_items", {})
                        line_item_count = 0
                        total_quantities = 0
                        cart_value = 0
                        for key, line_item in line_items.items():
                            if line_item and len(line_item):
                                for item in line_item:
                                    sale_price = item.get("sale_price", 0)
                                    quantity = item.get("quantity", 0)
                                    cart_value += (sale_price * quantity)
                                    total_quantities += quantity
                                    line_item_count += 1
                        models.append(_build_customer_cart_model(customer_id=customer_id, channel='midwest', line_item_count=line_item_count, cart_value=cart_value, total_quantities=total_quantities, activity_timestamp=activity['timestamp'], cart_id=cart_id))
                        processed_customers[str(customer_id)] = True

            for customer_id, customer_activity in customer_activities.items():
                if not str(customer_id) in processed_customers:
                    models.append(_build_customer_cart_model(customer_id=int(customer_id), channel='midwest', line_item_count=0, cart_value=0, total_quantities=0, activity_timestamp=customer_activity['timestamp'], cart_id=customer_activity['cart_id']))
                    processed_customers[str(customer_id)] = True
            _persist_activity_data(store_id, models)

            redis_util.get_customer_activities(store_id, "main", clear=True)
    else:
        logger.error(f"customer_activities.process_customer_activities: {store_id} store doesn't exist.")

def process_customer_activities(store_id):
    process_express_customer_activities(store_id)
    process_bc_storefront_customer_activities(store_id)

def process_cart_inventory_report(store_id):
    store = store_util.get_store_by_id(store_id)
    if store:
        cart_projection = {
            "customer_id": 1, "status": 1, "line_items": 1, "products": 1, "created_at": 1, "modified_at": 1, "cart_updated": 1, "bc_cart_id": 1
        }
        carts_data = fetchall_documents_from_storefront_collection(
            store_id=store_id,
            collection_name="cart",
            query={},
            projection=cart_projection
        )
        data = []
        if carts_data:
            for cart in carts_data:
                line_items = cart.get('line_items', {})
                customer_id = cart.get('customer_id', 0)
                bc_cart_id = cart.get('bc_cart_id', '')
                for sku, item_details in line_items.items():
                        data.append({
                            'variant_sku': sku,
                            'product_name': item_details.get('name', 'Unknown Product'),
                            'variant_id': item_details.get('variant_id', 0),
                            'product_id': item_details.get('product_id', 0),
                            'customer_id': customer_id,
                            'cart_quantity': item_details.get('quantity', 0),
                            'bc_cart_id': bc_cart_id
                        })
        
        if len(data) > 0:
            save_data(store_id, data, _build_cart_line_item_model)

    else:
        logger.error(f"customer_activities.process_customer_activities: {store_id} store doesn't exist.")


def _build_cart_line_item_model(mapping):
    # activity_time = datetime.datetime.fromtimestamp(customer_activity['last_activity_timestamp'])
    return customers_db.CartLineItemsInventory(
        customer_id=int(mapping['customer_id']),
        variant_id=mapping['variant_id'],
        variant_sku=mapping['variant_sku'],
        product_id=mapping['product_id'],
        product_name=mapping['product_name'],
        cart_quantity=mapping['cart_quantity'],
        bc_cart_id=mapping['bc_cart_id']
    )

def save_data(store_id, dto_list, dto_to_model_convertor):
    session = pg_db.get_session(store_id)
    try:
        customers_db.CartLineItemsInventory.clear_table(store_id, session)
        for dto in dto_list:
            model = dto_to_model_convertor(dto)
            try:
                session.merge(model)  # Merge handles existing records
            except IntegrityError:
                session.rollback()  # Rollback only the failing transaction
                logger.warning(f"Skipping duplicate entry for: {dto}")
    except Exception as ex:
        logger.error("Caught error in cart_line_items_util.save_data: store_id: " + str(store_id))
        logger.error(traceback.format_exc())
    finally:
        session.commit()
        session.close()

