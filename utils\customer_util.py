from mongo_db import customer_db
import mongo_db
from plugin import bc_customers, bc_price_list
from utils import redis_util, store_util
from datetime import datetime, timezone
import logging
from pg_db_utils import pg_customer_util
from utils import admin_app_notification_util
import pg_db as db
from sqlalchemy import text
import traceback
logger = logging.getLogger()

# customer scopes ...
CUSTOMER_CREATED_SCOPE = "store/customer/created"

def get_customer_by_id(store, customer_id):
    return customer_db.fetch_customer_by_id(store, int(customer_id))


def process_webhook_customer_updates(store_id):
    store = store_util.get_store_by_id(store_id)
    if store:
        updates = redis_util.dqueue_webhook_customer_updates(store_id)
        deletions = redis_util.dqueue_webhook_customer_delete(store_id)
        if updates and len(updates) > 0:
            customers = []
            new_customers = []
            for update in updates:
                if update['scope'] == CUSTOMER_CREATED_SCOPE:
                    new_customers.append(update['data']['id'])
                customers.append(update['data']['id'])
            logger.info("process_webhook_customer_updates: updating customers: " + str(len(customers)))
            bc_customers.update_customers(store, customers)
            add_customer_welcome_points(store, new_customers)
            for customer_id in new_customers:
                admin_app_notification_util.generate_admin_app_notification(store_id, admin_app_notification_util.AdminAppNotificationUtil.CUSTOMER_CREATED, customer_id)
            _update_customer_groups(store, customers)
        if deletions and len(deletions) > 0:
            for customer_id in deletions:
                logger.error(f"Deleting customer ID: {customer_id}")
                customer_db.remove_customer_by_id(store=store, customer_id=customer_id)
                pg_customer_util.remove_customer_pgdb(store_id,customer_id)
    else:
        raise Exception(f"process_webhook_product_updates: Store with id {store_id} doesn't exist.")

def _update_customer_groups(store, customers):
    conn = db.get_connection(store['id'])
    try:
        logger.info(customers)
        for customer_id in customers:
            customer_id = int(customer_id)
            customer_res = customer_db.fetch_customer_by_id(store, customer_id)
            if customer_res:
                customer_group_name = customer_db.fetch_customer_group_name(store, customer_res['customer_group_id'])
                query = text("""
                    UPDATE customers
                    SET 
                        customer_group_id = :customer_group_id,
                        customer_group_name = :customer_group_name
                    WHERE customer_id = :customer_id
                """)
                conn.execute(query, {'customer_group_id': customer_res['customer_group_id'], 'customer_group_name': customer_group_name, 'customer_id': customer_id})
                conn.commit()
    except Exception as e:
        logger.error(traceback.format_exc())
    finally:
        conn.close()

def update_customer_points(store, order_amount, customer_id, order_id):
    total_amount = float(order_amount)
    total_amount = round(total_amount)
    points = total_amount * 2
    # customer_res = customer_db.fetch_customer_by_id(store, customer_id)
    customer_res = customer_db.fetch_customer_coupon_by_id(store, customer_id)
    if customer_res:
        old_points = int(customer_res['loyalty_points']) if customer_res.__contains__("loyalty_points") else 0
        add_loyalty_reward_history(store, old_points, points, customer_id, 1, order_id)
        customer_res['loyalty_points'] = (
            int(customer_res['loyalty_points']) if customer_res.__contains__("loyalty_points") else 0) + int(points)
        customer_db.update_customer_points_by_id(store, customer_res) 
        del customer_res['_id']
        pg_customer_util.update_pg_customer_points(store['id'],customer_res) 
    else:
        data = {}
        data['customer_id'] = int(customer_id)
        data['coupons'] = []
        data['loyalty_points'] = points
        data['created_at'] = int(datetime.now(timezone.utc).timestamp())
        data['updated_at'] = int(datetime.now(timezone.utc).timestamp())
        add_loyalty_reward_history(store, 0, points, customer_id, 1, order_id)
        customer_db.create_customer_with_coupon(store, data)

def add_customer_welcome_points(store, customers=[]):
    n = len(customers)
    if n == 0:
        return 
    
    store_db = mongo_db.get_admin_db_client_for_store_id(store['id'])
    store_info = store_db["store_info"].find_one({"type": "welcome_loyalty_points"}, {"_id": 0, "welcome_points": 1})
    welcome_points = store_info.get("welcome_points", 0) if store_info else 0
    for idx, id in enumerate(customers):
        data = {}
        data['customer_id'] = int(id)
        data['coupons'] = []
        data['loyalty_points'] = welcome_points
        data['created_at'] = int(datetime.now(timezone.utc).timestamp())
        data['updated_at'] = int(datetime.now(timezone.utc).timestamp())
        add_loyalty_reward_history(store, 0, welcome_points, int(id), 0, 0)
        customer_db.create_customer_with_coupon(store, data)

def add_loyalty_reward_history(store, old_points, new_points, customer_id, flag, order_id):   
    data = {}
    message = ''
    if flag == 0:
        message = 'Earned ' + str(new_points) + ' Points through welcome bonus.'
    elif flag == 1:
        message = 'Earned ' + str(new_points) + ' Points with order #' + str(order_id) + '.'

    data['customer_id'] = int(customer_id)
    data['balance'] = int(old_points) + int(new_points)
    data['earned/used'] = int(new_points)
    data['created_at'] = int(datetime.now(timezone.utc).timestamp())
    data['description'] = message
    data['coupon_code'] = ''
    data['coupon_id'] = 0
    data['order_id'] = order_id
    customer_db.add_customer_loyalty_history(store, data)

def update_all_customers(store_id):
    store = store_util.get_store_by_id(store_id)
    if store:
        customer_db.clear_customer_groups_update_field(store)
        bc_customers.fetch_all_customers(store)

def update_all_customer_groups(store_id):
    store = store_util.get_store_by_id(store_id)
    if store:
        customer_db.clear_customer_groups_update_field(store)
        bc_customers.fetch_all_customer_groups(store)

def update_price_list_assignments(store_id):
    store = store_util.get_store_by_id(store_id)
    if store:
        #pricing_db.clear_price_list_assignment_update_field(store)
        bc_price_list.fetch_price_list_assignments(store)

def update_price_list(store_id, price_list_id):
    store = store_util.get_store_by_id(store_id)
    if store:
        #pricing_db.clear_price_list_update_field(store, price_list_id)
        bc_price_list.fetch_price_list_records(store, price_list_id)

def loyalty_point_visibility_check_for_customer(store_id, customer_id):
    conn = db.get_connection(store_id)
    try:
        query = text("SELECT customer_id, customer_group_id, customer_group_name, is_loyalty_point_enable FROM customers WHERE customer_id = :customer_id")
        result = conn.execute(query, {"customer_id": customer_id})
        customer_data = result.fetchone()
        if customer_data:
            customer_group_id = customer_data[1]
            customer_loyalty_point_visible = customer_data[3]
            group_loyalty_point_visible = True

            query = text("SELECT is_loyalty_point_enable FROM customer_group_print_invoice_visibility WHERE customer_group_id = :customer_group_id")
            result = conn.execute(query, {"customer_group_id": customer_group_id})
            customer_group_data = result.fetchone()
            if customer_group_data:
                group_loyalty_point_visible = customer_group_data[0]

            if customer_loyalty_point_visible == True:
                return True
            elif customer_loyalty_point_visible == False:
                return False
            elif customer_loyalty_point_visible == None:
                if group_loyalty_point_visible == True:
                    return True
                else:
                    return False
        else:
            return False
        
    except Exception as e:
        logger.error(traceback.format_exc())
        return False
    finally:
        conn.close()
        