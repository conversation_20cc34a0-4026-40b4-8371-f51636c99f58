from mongo_db import get_admin_db_client_for_store_id, get_store_by_id, get_store_db_client_for_store_id, store_db
import mongo_db
from utils import  email_util, common_util, price_list_util, store_util
from plugin import bc_price_list

import logging
import traceback
logger = logging.getLogger()
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import timezone, datetime
import pg_db
from sqlalchemy import text
import io
import csv
import zipfile
import pytz
from pg_db import analytics_db
from templates import html_template
import threading
import pandas as pd
from email.mime.base import MIMEBase
from email import encoders
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

logger = logging.getLogger()


def multi_store_price_list_csv_export(store_ids, query_params, username):
    """
    Export price list data for multiple stores and send via email
    
    Args:
        store_ids (list): List of store IDs to process
        query_params (dict): Query parameters for filtering products
        username (str): Username for email recipient
    """
    try:
        # Set a reasonable max_workers based on the number of stores
        max_workers = min(len(store_ids), 10)  # Cap at 10 concurrent threads
        
        def process_store(storeID, query_params):
            """Helper function to process a single store"""
            try:
                store_data = process_single_store_price_list_export(storeID, query_params)
                return {"store_id": storeID, "status": "success", "data": store_data}
            except Exception as e:
                logger.error(f"Error processing store {storeID}: {e}")
                return {"store_id": storeID, "status": "error", "error": str(e)}
        
        # Use ThreadPoolExecutor for concurrent execution
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_store = {
                executor.submit(process_store, storeID, query_params): storeID 
                for storeID in store_ids
            }
            
            # Collect results as they complete
            results = []
            for future in as_completed(future_to_store):
                storeID = future_to_store[future]
                try:
                    result = future.result()
                    results.append(result)
                    logger.info(f"Completed processing store {storeID}: {result['status']}")
                except Exception as e:
                    logger.error(f"Exception occurred for store {storeID}: {e}")
                    results.append({"store_id": storeID, "status": "error", "error": str(e)})
        
        # Log summary
        successful_stores = [r for r in results if r['status'] == 'success']
        failed_stores = [r for r in results if r['status'] == 'error']
        
        logger.info(f"Multi-store processing completed. Success: {len(successful_stores)}, Failed: {len(failed_stores)}")
        
        if failed_stores:
            logger.error(f"Failed stores: {[r['store_id'] for r in failed_stores]}")
        
        # Combine all data and errors
        all_csv_data = []
        all_errors = []
        
        # Collect data from successful stores
        for result in successful_stores:
            if result.get('data'):
                all_csv_data.extend(result['data'])
        
        print(all_csv_data, "all_csv_data")
        # Collect errors from failed stores
        for result in failed_stores:
            error_msg = result.get('error', 'Unknown error')
            all_errors.append(f"Store {result['store_id']}: {error_msg}")
        
        # Combine all errors into a single text
        errors_text = "\n".join(all_errors) if all_errors else ""
        
        # Generate CSV and send email
        if all_csv_data:
            generate_and_send_multi_store_csv(all_csv_data, store_ids, username, errors_text)
        else:
            logger.warning("No data to export for any store")
            
        return results
        
    except Exception as e:
        logger.error(traceback.format_exc())
        raise e


def process_single_store_price_list_export(store_id, query_params):
    """
    Process price list export for a single store
    Returns structured data for CSV generation
    """
    store = mongo_db.get_store_by_id(store_id)
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    storefront_db = mongo_db.get_store_db_client_for_store_id(store_id)
    conn = pg_db.get_connection(store_id)
    
    try:
        # Extract query parameters
        tag_filter = query_params.get("tag_filter")
        classification_filter = query_params.get("classification_filter")
        classified_as_filter = query_params.get("classified_as_filter")
        products_filter = query_params.get("products_filter")
        user_filter = query_params.get("user_filter")
        supplier_filter = query_params.get("supplier_filter")

        # Fetch price lists from BigCommerce
        static_price_lists = price_list_util.fetch_static_price_lists(store_id)
        price_lists, _ = bc_price_list.fetch_price_lists(store)

        # Merge static price lists
        price_lists['data'].extend([{
            'id': pl['id'], 'name': pl['name'], 'date_created': None, 'date_modified': None, 'active': pl['active']
        } for pl in static_price_lists])

        # Move price_list_id 7 to the top if active
        sorted_price_lists = sorted(price_lists['data'], key=lambda x: (x['id'] != 7, not x['active']))
        
        # Generate price list mappings
        price_list_meta = {
            f"price_{i+1}": {'name': pl['name'], 'active': pl['active']}
            for i, pl in enumerate(sorted_price_lists, start=0) if pl['active']
        }
        price_list_map = {pl['id']: f"price_{i+1}" for i, pl in enumerate(sorted_price_lists, start=0) if pl['active']}
        reversed_price_list_map = {v: k for k, v in price_list_map.items()}

        variant_sku_list = []
        filter_query = []

        def fetch_skus(query, param_name, values):
            result = conn.execute(text(query), {param_name: tuple(values)}).fetchall()
            return [row[0] for row in result]

        # Apply filters
        if tag_filter:
            tag_skus = fetch_skus("SELECT sku FROM product_tags WHERE tag_id IN :tag_filter_list", 'tag_filter_list', tag_filter.split(','))
            tag_query = {"parent_product_sku": {"$in": tag_skus}}
            filter_query.append(tag_query)
        
        if tag_filter:
            tag_skus = fetch_skus("SELECT variant_sku FROM product_tags WHERE tag_id IN :tag_filter_list", 'tag_filter_list', tag_filter.split(','))
            variant_sku_list = [sku for sku in tag_skus if sku is not None]

        if classified_as_filter:
            classified_skus = fetch_skus("SELECT parent_sku FROM replenishment_dashboard WHERE classified_as_id IN :classified_as_filter_list", 'classified_as_filter_list', classified_as_filter.split(','))
            classified_as_query = {"parent_product_sku": {"$in": classified_skus}}
            filter_query.append(classified_as_query)

        if classification_filter:
            classification_skus = fetch_skus("SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE classification IN :classification_filter_list AND parent_sku IS NOT NULL AND parent_sku != ''", 'classification_filter_list', classification_filter.split(','))
            classification_query = {"parent_product_sku": {"$in": classification_skus}}
            filter_query.append(classification_query)

        if user_filter:
            query = """
                SELECT ARRAY_AGG(DISTINCT suppliers) 
                FROM user_supplier_mapping 
                WHERE user_name = :user_name
            """
            res = conn.execute(text(query), {'user_name': user_filter})
            suppliers_list = res.fetchone()[0] or []

            query = "SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE primary_supplier = ANY(:suppliers_list) AND parent_sku IS NOT NULL AND parent_sku != ''"
            result = conn.execute(text(query), {'suppliers_list': suppliers_list}).fetchall()
            user_skus = [row[0] for row in result]
            user_query = {"parent_product_sku": {"$in": user_skus}}
            filter_query.append(user_query)

        if supplier_filter:
            supplier_filter_list = [supplier.strip() for supplier in supplier_filter.split(';')]
            query = "SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE primary_supplier ILIKE ANY(:supplier_filter_list) AND parent_sku IS NOT NULL AND parent_sku != ''"
            result = conn.execute(text(query), {'supplier_filter_list': supplier_filter_list}).fetchall()
            supplier_skus = [row[0] for row in result]
            supplier_query = {"parent_product_sku": {"$in": supplier_skus}}
            filter_query.append(supplier_query)

        if products_filter:
            filter_query.append({"parent_product_sku": {"$in": list(map(str, products_filter.split(',')))}})

        additional_query = {"$and": [{"syncing": False}] + filter_query} if filter_query else {"syncing": False}

        # Fetch products
        product_price_list_fields = {
            "parent_product_id": 1,
            "parent_product_name": 1,
            "parent_product_sku": 1,
            "created_at": 1,
            "updated_at": 1,
            "updated_by": 1,
            "default_price": 1,
            "variants": 1,
            "price_update_status": 1
        }
        products, total_data_length = common_util.get_records_price_list(
            db, "product_price_lists", {}, product_price_list_fields, additional_query
        )
        
        # Extract SKUs and IDs
        all_variant_skus = [variant['variant_sku'] for product in products for variant in product['variants']]
        all_parent_skus = [product['parent_product_sku'] for product in products]
        all_parent_product_ids = list({int(product['parent_product_id']) for product in products if 'parent_product_id' in product})

        # Fetch additional data
        month_names, day_difference = common_util.get_month_array_for_meta(6)
        
        # Product level data
        product_inventory_map = {}
        product_monthly_data_map = {}
        if all_parent_product_ids:
            # Inventory data
            product_inventory_docs = list(storefront_db["products"].find(
                {"id": {"$in": all_parent_product_ids}},
                {"id": 1, "inventory_level": 1}
            ))
            product_inventory_map = {doc['id']: doc.get('inventory_level', 0) for doc in product_inventory_docs}

            # Monthly data
            query = text(f"""
                SELECT product_id, month_1, month_2, month_3, month_4, month_5, month_6, month_7
                FROM {analytics_db.AnalyticsDB.get_replenishment_products_table()}
                WHERE product_id = ANY(:product_ids)
            """)
            result = conn.execute(query, {"product_ids": all_parent_product_ids}).fetchall()
            product_monthly_data_map = {
                row[0]: {
                    f"month_{i}": int(row[i]) if row[i] is not None else None
                    for i in range(1, 8)
                } for row in result
            }

        # Variant level data
        sku_inventory_map = {}
        sku_cost_map = {}
        variant_monthly_data_map = {}
        if all_variant_skus:
            # Cost data
            query = text("SELECT sku, cost FROM skuvault_catalog WHERE sku IN :variant_skus")
            result = conn.execute(query, {'variant_skus': tuple(all_variant_skus)})
            sku_cost_map = {row[0]: row[1] for row in result.fetchall()}

            # Inventory data
            cursor = storefront_db["products"].find(
                {"variants.sku": {"$in": all_variant_skus}},
                {"variants.sku": 1, "variants.inventory_level": 1}
            )
            for product_doc in cursor:
                for variant in product_doc.get("variants", []):
                    sku = variant.get("sku")
                    if sku in all_variant_skus:
                        sku_inventory_map[sku] = variant.get("inventory_level")

            # Monthly data
            query = text(f"""
                SELECT sku, month_1, month_2, month_3, month_4, month_5, month_6, month_7
                FROM {analytics_db.AnalyticsDB.get_replenishment_variants_table()}
                WHERE sku = ANY(:skus)
            """)
            result = conn.execute(query, {"skus": all_variant_skus}).fetchall()
            variant_monthly_data_map = {
                row[0]: {
                    f"month_{i}": int(row[i]) if row[i] is not None else None
                    for i in range(1, 8)
                } for row in result
            }

        # Classification data
        classification_map = {}
        if all_parent_skus:
            classification_query = text("SELECT DISTINCT classification, parent_sku FROM skuvault_catalog WHERE parent_sku IN :parent_product_skus")
            classification_results = conn.execute(classification_query, {'parent_product_skus': tuple(all_parent_skus)}).fetchall()
            
            classification_temp = {}
            for row in classification_results:
                if row[1] in classification_temp:
                    classification_temp[row[1]].append(row[0])
                else:
                    classification_temp[row[1]] = [row[0]]
            
            classification_map = {sku: ", ".join(classifications) for sku, classifications in classification_temp.items()}

        # User data
        updated_by_users = {product.get('updated_by') for product in products if product.get('updated_by')}
        user_data_map = store_db.fetch_users_by_usernames(store_id, updated_by_users) if updated_by_users else {}

        # Price list update data
        parent_product_ids = [product['parent_product_id'] for product in products if product.get('parent_product_id')]
        price_list_update_map = {}
        if parent_product_ids:
            query = text("""
                SELECT product_id, updated_at, updated_by 
                FROM pricelist_change_logs 
                WHERE product_id IN :parent_product_ids AND is_active = TRUE
            """)
            result = conn.execute(query, {'parent_product_ids': tuple(parent_product_ids)}).fetchall()
            price_list_update_map = {
                row[0]: {'updated_at': row[1], 'updated_by': row[2]} for row in result
            }

        # Process products and prepare data
        csv_data = []
        
        for product in products:
            parent_product_id = product.get('parent_product_id')
            parent_product_sku = product.get('parent_product_sku', None)
            product_updated_at = product.get('updated_at')
            
            # Add inventory and monthly data
            product['inventory_level'] = product_inventory_map.get(parent_product_id, None)
            monthly_data = product_monthly_data_map.get(parent_product_id, {})
            for i in range(1, 8):
                product[f'month_{i}'] = monthly_data.get(f'month_{i}', None)

            # Calculate turn rate and weeks on hand
            total_sum = sum([int(monthly_data.get(f'month_{i}', 0) or 0) for i in range(2, 8)])
            inventory_level = max(int(product.get("inventory_level") or 0), 1)
            turn_rate = ((int(total_sum) / inventory_level) * 365) / day_difference
            weeks_on_hand = 52 / turn_rate if turn_rate != 0 else 1

            product['turn_rate'] = round(turn_rate, 2)
            product['weeks_on_hand'] = round(weeks_on_hand, 2)

            # Apply price list updates
            if parent_product_id in price_list_update_map:
                update_data = price_list_update_map[parent_product_id]
                price_list_updated_at_str = update_data['updated_at']
                
                # if product_updated_at and price_list_updated_at_str:
                #     product_updated_at_dt = datetime.strptime(product_updated_at, "%Y-%m-%dT%H:%M:%SZ")
                #     price_list_updated_at_dt = datetime.strptime(price_list_updated_at_str, "%Y-%m-%dT%H:%M:%SZ")
                
                #     if product_updated_at_dt <= price_list_updated_at_dt or abs((product_updated_at_dt - price_list_updated_at_dt).total_seconds()) <= 3:
                #         product['updated_at'] = price_list_updated_at_dt.strftime("%Y-%m-%dT%H:%M:%SZ")
                #         product['updated_by'] = update_data['updated_by']
            
            product['classification'] = classification_map.get(parent_product_sku, "")
            updated_by = product.get('updated_by')
            product['updated_by'] = user_data_map.get(updated_by, {}).get("name", updated_by)
            
            # Filter variants
            product['variants'] = [v for v in product['variants'] if v.get('variant_sku') in variant_sku_list] or product['variants']
            
            # Process variants
            price_list_data = {key: "" for key in price_list_meta}
            variant_prices = []
            variant_costs = []

            for variant in product['variants']:
                variant_sku = variant.get('variant_sku')
                variant['variant_price'] = product['default_price'] if variant.get('variant_price') is None else variant.get('variant_price')
                variant['cost'] = sku_cost_map.get(variant_sku, None)
                variant['inventory_level'] = sku_inventory_map.get(variant_sku, None)
                
                # Add monthly data for variants
                variant_monthly_data = variant_monthly_data_map.get(variant_sku, {})
                for i in range(1, 8):
                    variant[f'month_{i}'] = variant_monthly_data.get(f'month_{i}', None)

                # Calculate variant turn rate and weeks on hand
                variant_total_sum = sum([int(variant_monthly_data.get(f'month_{i}', 0) or 0) for i in range(2, 8)])
                variant_inventory_level = max(int(variant.get("inventory_level") or 0), 1)
                variant_turn_rate = ((int(variant_total_sum) / variant_inventory_level) * 365) / day_difference
                variant_weeks_on_hand = 52 / variant_turn_rate if variant_turn_rate != 0 else 1

                variant['variant_turn_rate'] = round(variant_turn_rate, 2)
                variant['variant_weeks_on_hand'] = round(variant_weeks_on_hand, 2)
                
                # Collect prices and costs
                if variant['variant_price'] is not None:
                    variant_prices.append(variant['variant_price'])
                if variant['cost'] is not None:
                    variant_costs.append(variant['cost'])

                # Process price lists
                for price in variant['price_list']:
                    price_list_key = price_list_map.get(price['price_list_id'])
                    if price_list_key:
                        price_value = float(price['price'])
                        if price_list_data[price_list_key]:
                            min_price, max_price = map(float, price_list_data[price_list_key].split('-')) if '-' in price_list_data[price_list_key] else (float(price_list_data[price_list_key]), float(price_list_data[price_list_key]))
                            price_list_data[price_list_key] = f"{min(min_price, price_value)}-{max(max_price, price_value)}" if min_price != max_price else str(min_price)
                        else:
                            price_list_data[price_list_key] = str(price_value)

            # Set product level prices and costs
            if variant_prices:
                product['default_price'] = f"{min(variant_prices)}-{max(variant_prices)}" if len(set(variant_prices)) > 1 else str(min(variant_prices))
            
            if variant_costs:
                product['cost_range'] = f"{min(variant_costs)}-{max(variant_costs)}" if len(set(variant_costs)) > 1 else str(min(variant_costs))
            else:
                product['cost_range'] = None

            # Format price lists
            product['price_list'] = [{
                price_list_meta[key]['name']: price_list_data[key],
                "price_list_id": i+1
            } for i, key in enumerate(price_list_meta)]
            
            # Process variant price lists
            for variant in product['variants']:
                variant['parent_product_id'] = product['parent_product_id']
                variant['parent_product_name'] = product['parent_product_name']

                updated_price_list = []
                for price_list_key, meta in price_list_meta.items():
                    price_list_id = reversed_price_list_map.get(price_list_key)
                    price_list_name = meta['name']
                    price_entry = {'price_list_id': price_list_id, price_list_name: ""}

                    for price in variant['price_list']:
                        if price['price_list_id'] == price_list_id:
                            price_entry[price_list_name] = str(price.pop('price'))
                            break

                    updated_price_list.append(price_entry)

                variant['price_list'] = updated_price_list

            # Add store information
            product['store_id'] = store_id
            product['store_name'] = store.get('name', f'Store {store_id}')

            csv_data.append(product)

        return csv_data

    except Exception as e:
        logger.error(f"Error in process_single_store_price_list_export for store {store_id}: {e}")
        raise e
    finally:
        if conn:
            conn.close()


def generate_and_send_multi_store_csv(all_csv_data, store_ids, username, errors_text):
    """
    Generate merged multi-store CSV with store-specific columns and send email.
    Creates columns like: Product Name | SKU | Variant Name | Classification |
    Current Stock Midwest | Current Stock CBD | Cost Midwest | Cost CBD | etc.
    """
    try:
        # Step 1: Get store names for column headers
        store_names = {}
        for store_id in store_ids:
            try:
                store = mongo_db.get_store_by_id(store_id)
                store_names[store_id] = store.get('name', f'Store {store_id}')
            except Exception as e:
                logger.warning(f"Could not get store name for {store_id}: {e}")
                store_names[store_id] = f'Store {store_id}'

        # Step 2: Build mapping of SKU/variant to data per store
        sku_variant_map = {}  # {(sku, variant_name): {store_id: data_dict}}

        # Step 3: Process all CSV data and group by SKU/variant
        for product in all_csv_data:
            store_id = product.get('store_id', '')
            store_name = store_names.get(store_id, f"Store {store_id}")

            # Parent product row (no variant)
            key = (product['parent_product_sku'], "")
            if key not in sku_variant_map:
                sku_variant_map[key] = {}

            # Extract price list data for parent product
            price_list_data = {}
            for price_entry in product.get('price_list', []):
                for key_name, value in price_entry.items():
                    if key_name != 'price_list_id':
                        price_list_data[key_name] = value

            sku_variant_map[key][store_id] = {
                'Product Name': product['parent_product_name'],
                'SKU': product['parent_product_sku'],
                'Variant Name': "",
                'Classification': product.get('classification', ""),
                'Current Stock': product.get('inventory_level', 0),
                'Cost': product.get('cost_range', ''),
                'Wholesale': product.get('default_price', ''),
                'Price Lists': price_list_data,
            }

            # Process variants
            for variant in product.get('variants', []):
                variant_key = (variant['variant_sku'], variant['variant_name'])
                if variant_key not in sku_variant_map:
                    sku_variant_map[variant_key] = {}

                # Extract variant price list data
                variant_price_list_data = {}
                for price_entry in variant.get('price_list', []):
                    for key_name, value in price_entry.items():
                        if key_name != 'price_list_id':
                            variant_price_list_data[key_name] = value

                sku_variant_map[variant_key][store_id] = {
                    'Product Name': product['parent_product_name'],
                    'SKU': variant['variant_sku'],
                    'Variant Name': variant['variant_name'],
                    'Classification': product.get('classification', ""),
                    'Current Stock': variant.get('inventory_level', 0),
                    'Cost': variant.get('cost', ''),
                    'Wholesale': variant.get('variant_price', ''),
                    'Price Lists': variant_price_list_data,
                }

        # Step 4: Build headers with store-specific columns
        ordered_store_names = [store_names.get(sid, f"Store {sid}") for sid in store_ids]

        # Get all price list names from any product to ensure consistent columns
        price_list_names = set()
        for sku_data in sku_variant_map.values():
            for store_data in sku_data.values():
                if 'Price Lists' in store_data:
                    price_list_names.update(store_data['Price Lists'].keys())
        price_list_names = sorted(list(price_list_names))  # Sort for consistent ordering

        # Base headers (common to all)
        base_headers = ['Product Name', 'SKU', 'Variant Name', 'Classification']

        # Build store-specific columns as requested by user
        store_columns = []
        for store_name in ordered_store_names:
            store_columns.extend([
                f"Current Stock {store_name}",
                f"Cost {store_name}",
                f"Wholesale {store_name}",
            ])
            # Add price list columns for this store
            for pl_name in price_list_names:
                store_columns.append(f"{pl_name} {store_name}")

        headers = base_headers + store_columns

        # Step 5: Write CSV with store-specific columns
        csv_content = io.StringIO()
        writer = csv.DictWriter(csv_content, fieldnames=headers)
        writer.writeheader()

        for (sku, variant_name), store_data_map in sku_variant_map.items():
            # Get classification from any store that has data (should be same across stores)
            classification = ''
            for store_id in store_ids:
                store_data = store_data_map.get(store_id, {})
                if store_data and store_data.get('Classification'):
                    classification = store_data['Classification']
                    break

            row = {
                'Product Name': next((d['Product Name'] for d in store_data_map.values() if d.get('Product Name')), ''),
                'SKU': sku,
                'Variant Name': variant_name,
                'Classification': classification,
            }

            # For each store, fill in store-specific columns
            for store_name in ordered_store_names:
                # Find the store_id for this store_name
                store_id = next((sid for sid, sname in store_names.items() if sname == store_name), None)
                store_data = store_data_map.get(store_id, {}) if store_id else {}

                if store_data:
                    row[f"Current Stock {store_name}"] = store_data.get('Current Stock', '')
                    row[f"Cost {store_name}"] = store_data.get('Cost', '')
                    row[f"Wholesale {store_name}"] = store_data.get('Wholesale', '')

                    # Price lists for this store
                    price_lists = store_data.get('Price Lists', {})
                    for pl_name in price_list_names:
                        row[f"{pl_name} {store_name}"] = price_lists.get(pl_name, '')
                else:
                    # Fill with empty values if store has no data for this SKU/variant
                    row[f"Current Stock {store_name}"] = ''
                    row[f"Cost {store_name}"] = ''
                    row[f"Wholesale {store_name}"] = ''
                    for pl_name in price_list_names:
                        row[f"{pl_name} {store_name}"] = ''

            writer.writerow(row)

        # Generate filename and compress
        utc_now = datetime.now(timezone.utc)
        cst_tz = pytz.timezone('America/Chicago')
        cst_now = utc_now.astimezone(cst_tz)
        created_date = cst_now.strftime("%m-%d-%Y_%H:%M")
        zip_filename = f"MultiStore_PriceList_{created_date}.zip"

        # Compress CSV into ZIP
        zip_buffer = io.BytesIO()
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            zip_file.writestr(zip_filename.replace('.zip', '.csv'), csv_content.getvalue())
        zip_buffer.seek(0)

        # Send email with the CSV
        email_util.send_price_list_csv_data_email(
            store_id=store_ids[0] if store_ids else None,
            file_content=zip_buffer,
            recipient_email=username,
            filename=zip_filename,
            user_name=username,
            created_date=created_date
        )

    except Exception as e:
        logger.error(f"Error in generate_and_send_multi_store_csv: {e}")
        raise e 
    

def _get_store_ids_from_store_name(store_ids, store_name):
    if store_name == 'both':
        if "63da3e98b702e324567f76f9" in store_ids:
            store_ids.append("661239751b9ce4bd7f85237c")
        elif "661239751b9ce4bd7f85237c" in store_ids:
            store_ids.append("63da3e98b702e324567f76f9")
    elif store_name == 'midwest':
        if "63da3e98b702e324567f76f9" not in store_ids:
            store_ids.append("63da3e98b702e324567f76f9")
        if "661239751b9ce4bd7f85237c" in store_ids:
            store_ids.remove("661239751b9ce4bd7f85237c")
    elif store_name == 'cbd':
        if "661239751b9ce4bd7f85237c" not in store_ids:
            store_ids.append("661239751b9ce4bd7f85237c")
        if "63da3e98b702e324567f76f9" in store_ids:
            store_ids.remove("63da3e98b702e324567f76f9")
    return store_ids

def _setup_store_connections(store_ids):
    """
    Setup database connections for multiple stores.
    
    Returns:
        Dict with store_id as key and connection objects as values
    """
    connections = {}
    
    for store_id in store_ids:
        try:
            store = get_store_by_id(store_id)
            connections[store_id] = {
                'admin_db': get_admin_db_client_for_store_id(store_id),
                'store_db': get_store_db_client_for_store_id(store_id),
                'pg_conn': pg_db.get_connection(store_id),
                'store_name': 'Midwest' if store_id == "63da3e98b702e324567f76f9" else 'CBD'
                
            }
        except Exception as e:
            logger.error(f"Failed to setup connections for store {store_id}: {e}")
            # Close any connections that were successfully opened
            _close_store_connections(connections)
            raise e
    
    return connections

def _close_store_connections(connections):
    """
    Close all database connections.
    """
    for store_id, conn_dict in connections.items():
        try:
            if 'pg_conn' in conn_dict and conn_dict['pg_conn']:
                conn_dict['pg_conn'].close()
        except Exception as e:
            logger.error(f"Error closing connections for store {store_id}: {e}")

def _get_store_object_safely(store_id):
    """
    Safely get store object by ID with error handling.
    """
    try:
        store_obj = store_util.get_store_by_id(store_id)
        return store_obj

    except Exception as e:
        logger.error(f"Error getting store object for {store_id}: {e}")
        return None

def _get_price_lists_safely(store_obj, store_id):
    """
    Safely get price lists from BigCommerce with error handling.
    """
    try:
        if store_obj:
            price_lists, _ = bc_price_list.fetch_price_lists(store_obj)
            return price_lists
        else:
            logger.warning(f"No store object provided for store {store_id}")
            return {'data': []}
    except Exception as e:
        logger.error(f"Error fetching BigCommerce price lists for store {store_id}: {e}")
        logger.error(f"Store object keys: {list(store_obj.keys()) if store_obj else 'None'}")
        return {'data': []}

def _build_price_list_mappings(store_id, price_lists, static_price_lists, role_id):
    """
    Build price list mappings using a static reference price list for sorting by name and store_id.
    """
    refrence_price_lists = {
        "Value Tier Price List": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 7,
                "661239751b9ce4bd7f85237c": 4
            }
        },
        "Tier 1 Price List": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 1,
                "661239751b9ce4bd7f85237c": 1
            }
        },
        "Tier 2 Price List": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 4,
                "661239751b9ce4bd7f85237c": 2
            }
        },
        "Tier Pro Price List": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 8,
                "661239751b9ce4bd7f85237c": 3
            }
        },
        "Distributor": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 13,
                "661239751b9ce4bd7f85237c": 5
            }
        },
        "MVD Warehouse": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 15,
                "661239751b9ce4bd7f85237c": 7
            }
        },
        "TCD Warehouse": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 14,
                "661239751b9ce4bd7f85237c": 6
            }
        },
        "VIP": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 52,
                "661239751b9ce4bd7f85237c": 52
            }
        },
    }

    # Merge static price lists
    price_lists['data'].extend([{
        'id': pl['id'], 'name': pl['name'], 'date_created': None, 'date_modified': None, 'active': pl['active']
    } for pl in static_price_lists])

    # Role-specific filtering
    if role_id == '67f5f9c43c97938e59357472':
        price_lists['data'] = [pl for pl in price_lists['data'] if pl['id'] not in (14, 15)]
    elif role_id == '67fd12676af694b36923ce09':
        price_lists['data'] = [pl for pl in price_lists['data'] if pl['id'] in (13, 15, 52)]

    # Build a lookup for all price lists by id
    id_to_price_list = {pl['id']: pl for pl in price_lists['data'] if pl['active']}

    # Now, build the sorted meta and map using the reference order and store_id mapping
    price_list_meta = {}
    price_list_map = {}
    idx = 1
    for name, ref_entry in refrence_price_lists.items():
        price_list_id = ref_entry['store_mappings'].get(store_id)
        if price_list_id is None:
            continue
        pl = id_to_price_list.get(price_list_id)
        if not pl:
            continue
        price_list_key = f"price_{idx}"
        price_list_meta[price_list_key] = {
            'id': pl['id'],
            'name': pl['name'],
            'active': pl['active']
        }
        price_list_map[pl['id']] = price_list_key
        idx += 1
    return price_list_meta, price_list_map

def _process_single_store_products_with_sales_percentage(store_id, products, store_conn, price_list_meta, price_list_map, role_id, price_list_to_customer_group):
    # Get all parent SKUs for classification lookup
    all_parent_skus = [product['parent_product_sku'] for product in products]

    # Get classification data
    classification_map = {}
    if all_parent_skus:
        classification_query = text("""
            SELECT DISTINCT
                sc.parent_sku,
                sc.classification,
                sc.primary_supplier,
                usm.user_name
            FROM skuvault_catalog sc
            LEFT JOIN user_supplier_mapping usm
                ON sc.primary_supplier = usm.suppliers
            WHERE sc.parent_sku IN :parent_product_skus
        """)

        classification_results = store_conn['pg_conn'].execute(classification_query, {'parent_product_skus': tuple(all_parent_skus)}).fetchall()

        for parent_sku, classification, primary_supplier, user_name in classification_results:
            if parent_sku in classification_map:
                classification_map[parent_sku]['classifications'].add(classification)
            else:
                classification_map[parent_sku] = {
                    'classifications': {classification},
                    'primary_supplier': primary_supplier,
                    'user_name': user_name
                }

        # Convert classifications set to comma-separated string
        for sku in classification_map:
            classification_map[sku]['classifications'] = ", ".join(classification_map[sku]['classifications'])

    # Get user data for updated_by field
    updated_by_users = {product.get('updated_by') for product in products if product.get('updated_by')}
    user_data_map = store_db.fetch_users_by_usernames(store_id, updated_by_users) if updated_by_users else {}

    # Process each product
    for product in products:
        parent_product_sku = product.get('parent_product_sku', None)

        # Convert timestamps
        # product['updated_at'] = datetime.strptime(product.get('updated_at'), "%Y-%m-%dT%H:%M:%SZ") if product.get('updated_at') else ''
        # product['created_at'] = datetime.strptime(product.get('created_at'), "%Y-%m-%dT%H:%M:%SZ") if product.get('created_at') else ''
        product['inventory_level'] = product.get('inventory_level', 0)

        # Add classification data
        product['classification'] = classification_map.get(parent_product_sku, {}).get('classifications', "")
        product['primary_supplier'] = classification_map.get(parent_product_sku, {}).get('primary_supplier', "")
        product['purchaser'] = classification_map.get(parent_product_sku, {}).get('user_name', "")

        # Assign user-friendly name for updated_by
        updated_by = product.get('updated_by')
        product['updated_by'] = user_data_map.get(updated_by, {}).get("name", updated_by)

        # Add monthly data if role allows
        if role_id != "67fd12676af694b36923ce09":
            for i in range(1, 8):
                product[f'month_{i}'] = product.get(f'month_{i}', None)

        product['turn_rate'] = product.get('turn_rate', 0)
        product['weeks_on_hand'] = product.get('weeks_on_hand', 0)

        # Process pricing data with sales percentage
        # _process_product_pricing_with_sales_percentage(product, price_list_meta, price_list_map, product_data_map_for_margin_and_sales, price_list_to_customer_group)

        # Remove variants from final output
        if 'variants' in product:
            del product['variants']

    return products

def _apply_filters_single_store(pg_conn, tag_filter=None, classification_filter=None, classified_as_filter=None, products_filter=None, user_filter=None, supplier_filter=None, top_products_filter=None, cost_margin_filter=None, is_multi_store=False, filter=''):
    all_skus = set()  # To collect all unique SKUs
    cost_margin_query = None

    def fetch_skus(query, param_name, values):
        result = pg_conn.execute(text(query), {param_name: tuple(values)}).fetchall()
        return [row[0] for row in result]

    # For multi-store, only allow certain filters that use skuvault_catalog
    if is_multi_store:
        if classification_filter:
            classification_skus = fetch_skus("SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE classification IN :classification_filter_list AND parent_sku IS NOT NULL AND parent_sku != ''", 'classification_filter_list', classification_filter.split(','))
            all_skus.update(classification_skus)

        if user_filter:
            query = """
                SELECT ARRAY_AGG(DISTINCT suppliers)
                FROM user_supplier_mapping
                WHERE user_name = :user_name
            """
            res = pg_conn.execute(text(query), {'user_name': user_filter})
            suppliers_list = res.fetchone()[0] or []

            if suppliers_list:
                query = "SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE primary_supplier = ANY(:suppliers_list) AND parent_sku IS NOT NULL AND parent_sku != ''"
                result = pg_conn.execute(text(query), {'suppliers_list': suppliers_list}).fetchall()
                user_skus = [row[0] for row in result]
                all_skus.update(user_skus)

        if supplier_filter:
            supplier_filter_list = [supplier.strip() for supplier in supplier_filter.split(';')]
            query = "SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE primary_supplier ILIKE ANY(:supplier_filter_list) AND parent_sku IS NOT NULL AND parent_sku != ''"
            result = pg_conn.execute(text(query), {'supplier_filter_list': supplier_filter_list}).fetchall()
            supplier_skus = [row[0] for row in result]
            all_skus.update(supplier_skus)

        # Handle cost_margin filter for multi-store
        if cost_margin_filter:
            cost_margin_filter_val = int(cost_margin_filter)
            if cost_margin_filter_val > 0:
                cost_margin_query = {"cost_margin": {"$gte": 0, "$lte": cost_margin_filter_val}}
            else:
                cost_margin_query = {"cost_margin": {"$gte": cost_margin_filter_val, "$lte": 0}}
        
        if products_filter: 
            sku_array = list(map(str, products_filter.split(',')))
            all_skus.update(sku_array)

        # If no filters applied for multi-store, get all SKUs from skuvault_catalog
        if not all_skus and filter == '':
            # Get all SKUs (no pagination here, will be handled in MongoDB)
            query = """
                SELECT DISTINCT parent_sku
                FROM skuvault_catalog
                WHERE parent_sku IS NOT NULL AND parent_sku != ''
            """
            result = pg_conn.execute(text(query)).fetchall()
            all_skus_list = [row[0] for row in result]
            all_skus.update(all_skus_list)

            # Return for multi-store with no filters
            return list(all_skus), cost_margin_query, len(all_skus), list(all_skus)

        # Return for multi-store with filters
        return list(all_skus), cost_margin_query, len(all_skus), list(all_skus)

    else:
        # Single store - all filters enabled (original logic)
        filter_query = []

        if tag_filter:
            tag_skus = fetch_skus("SELECT sku FROM product_tags WHERE tag_id IN :tag_filter_list", 'tag_filter_list', tag_filter.split(','))
            tag_query = {"parent_product_sku": {"$in": tag_skus}}
            filter_query.append(tag_query)
            all_skus.update(tag_skus)

        if classified_as_filter:
            classified_skus = fetch_skus("SELECT parent_sku FROM replenishment_dashboard WHERE classified_as_id IN :classified_as_filter_list", 'classified_as_filter_list', classified_as_filter.split(','))
            classified_as_query = {"parent_product_sku": {"$in": classified_skus}}
            filter_query.append(classified_as_query)
            all_skus.update(classified_skus)

        if classification_filter:
            classification_skus = fetch_skus("SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE classification IN :classification_filter_list AND parent_sku IS NOT NULL AND parent_sku != ''", 'classification_filter_list', classification_filter.split(','))
            classification_query = {"parent_product_sku": {"$in": classification_skus}}
            filter_query.append(classification_query)
            all_skus.update(classification_skus)

        if user_filter:
            query = """
                SELECT ARRAY_AGG(DISTINCT suppliers)
                FROM user_supplier_mapping
                WHERE user_name = :user_name
            """
            res = pg_conn.execute(text(query), {'user_name': user_filter})
            suppliers_list = res.fetchone()[0] or []

            if suppliers_list:
                query = "SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE primary_supplier = ANY(:suppliers_list) AND parent_sku IS NOT NULL AND parent_sku != ''"
                result = pg_conn.execute(text(query), {'suppliers_list': suppliers_list}).fetchall()
                user_skus = [row[0] for row in result]
                user_query = {"parent_product_sku": {"$in": user_skus}}
                filter_query.append(user_query)
                all_skus.update(user_skus)

        if supplier_filter:
            supplier_filter_list = [supplier.strip() for supplier in supplier_filter.split(';')]
            query = "SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE primary_supplier ILIKE ANY(:supplier_filter_list) AND parent_sku IS NOT NULL AND parent_sku != ''"
            result = pg_conn.execute(text(query), {'supplier_filter_list': supplier_filter_list}).fetchall()
            supplier_skus = [row[0] for row in result]
            supplier_query = {"parent_product_sku": {"$in": supplier_skus}}
            filter_query.append(supplier_query)
            all_skus.update(supplier_skus)

        if products_filter:
            query = """SELECT DISTINCT sku FROM products WHERE product_id IN :products_filter_list"""
            result = pg_conn.execute(text(query), {'products_filter_list': list(map(int, products_filter.split(',')))})
            products_skus = [row[0] for row in result]
            products_query = {"parent_product_sku": {"$in": products_skus}}
            filter_query.append(products_query)
            all_skus.update(products_skus)

        if top_products_filter:
            top_n = int(top_products_filter)
            query = text(f"""
                SELECT parent_sku
                FROM {analytics_db.AnalyticsDB.profitability_products_revenue_table}
                WHERE order_date >= NOW() - INTERVAL '30 days'
                GROUP BY product_id
                ORDER BY SUM(revenue) DESC
                LIMIT :top_n
            """)

            result = pg_conn.execute(query, {'top_n': top_n}).fetchall()
            top_product_skus = [row[0] for row in result]

            if top_product_skus:
                top_products_query = {"parent_product_sku": {"$in": top_product_skus}}
                filter_query.append(top_products_query)
                all_skus.update(top_product_skus)

        if cost_margin_filter:
            cost_margin_filter_val = int(cost_margin_filter)
            if cost_margin_filter_val > 0:
                filter_query.append({
                    "cost_margin": {"$gte": 0, "$lte": cost_margin_filter_val}
                })
            else:
                filter_query.append({
                    "cost_margin": {"$gte": cost_margin_filter_val, "$lte": 0}
                })

        # Return for single store
        return filter_query, len(all_skus), list(all_skus)
    
def _get_store_products_data_with_filters(store_id, store_conn, unified_filter_query, field, sort_order, filter, role_id, apply_sorting=True):
    """
    Get products data for a specific store with pre-applied filters.

    Args:
        apply_sorting: If True, apply sorting and pagination. If False, get all data without sorting.
    """
    try:
        # Setup payload based on whether sorting should be applied
        if apply_sorting:
            payload = {
                "sort_by": field,
                "sort_order": sort_order,
                "filterBy": ["parent_product_name", "parent_product_sku", "variant_name", "variant_sku"],
                "filter": filter
            }
        else:
            # For secondary stores, get all data without pagination/sorting
            payload = {
                "sort_by": field,
                "sort_order": sort_order,
                "filterBy": ["parent_product_name", "parent_product_sku", "variant_name", "variant_sku"],
                "filter": filter
            }
        # Build additional query from unified_filter_query
        additional_query = {}
        if unified_filter_query:
            if len(unified_filter_query) == 1:
                additional_query = unified_filter_query[0]
            else:
                additional_query = {"$and": unified_filter_query}

        product_price_list_fields = {
            "parent_product_id": 1,
            "parent_product_name": 1,
            "parent_product_sku": 1,
            "created_at": 1,
            "updated_at": 1,
            "updated_by": 1,
            "default_price": 1,
            "variants": 1,
            "price_update_status": 1
        }

        # Get products
        # products, total_data_length, _, _ = get_paginated_records_price_list(
        #     store_conn['admin_db'], StoreAdminDBCollections.PRODUCT_PRICE_LISTS, payload, product_price_list_fields, additional_query
        # )
        products, total_data_length = common_util.get_records_price_list(
            store_conn['admin_db'], "product_price_lists", payload, product_price_list_fields, additional_query
        )
        # Get price lists
        static_price_lists = price_list_util.fetch_static_price_lists(store_id)
        store_obj = _get_store_object_safely(store_id)
        price_lists = _get_price_lists_safely(store_obj, store_id)

        # Build price list mappings
        price_list_meta, price_list_map = _build_price_list_mappings(store_id, price_lists, static_price_lists, role_id)

        # Get price list assignments for sales percentage calculation
        assignments = list(store_conn['store_db']['price_list_assignment'].find({
            "price_list_id": {"$exists": True},
            "customer_group_id": {"$exists": True}
        }))

        price_list_to_customer_group = {assignment['price_list_id']: assignment['customer_group_id'] for assignment in assignments}

        # Process products
        processed_products = _process_single_store_products_with_sales_percentage(store_id, products, store_conn, price_list_meta, price_list_map, role_id, price_list_to_customer_group)

        return {
            'products': processed_products,
            'total_count': total_data_length,
            'price_list_meta': price_list_meta,
            'price_list_map': price_list_map
        }

    except Exception as e:
        logger.error(f"Error getting store products data with filters for store {store_id}: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {
            'products': [],
            'total_count': 0,
            'price_list_meta': {},
            'price_list_map': {}
        }
    
def export_multi_store_product_price_lists_csv(store_id, query_params, username):
    """
    Optimized export of multi-store product price lists to CSV and send via email.
    Groups products by SKU and creates store-specific columns for comparison.
    """
    try:
        # Extract query parameters
        store_name = query_params.get("store_name", "both")
        store_ids = _get_store_ids_from_store_name([store_id], store_name)

        # Use the optimized multi-store processing function
        results = multi_store_price_list_csv_export(store_ids, query_params, username)

        print(results, "results")
        # Check if processing was successful
        successful_stores = [r for r in results if r['status'] == 'success']
        failed_stores = [r for r in results if r['status'] == 'error']

        if not successful_stores:
            logger.error("No stores processed successfully")
            return {"status": 500, "message": "Failed to process data from any store"}

        if failed_stores:
            logger.warning(f"Some stores failed to process: {[r['store_id'] for r in failed_stores]}")

        return {"status": 200, "message": f"CSV export sent to {username}"}

    except Exception as e:
        logger.error(f"Error in export_multi_store_product_price_lists_csv: {e}")
        logger.error(traceback.format_exc())
        return {"status": 500, "message": str(e)}

def send_email_with_attachment(smtp_server, smtp_port, username, password, subject, msg_body, from_email, to_email, attachment_bytes, attachment_filename, attachment_mimetype):
    """
    Send an email with a file attachment (e.g., CSV).
    attachment_bytes: bytes of the file
    attachment_filename: filename for the attachment
    attachment_mimetype: e.g., 'text/csv'
    """
    try:
        msg = MIMEMultipart()
        msg['Subject'] = subject
        msg['From'] = from_email
        msg['To'] = ", ".join(to_email)
        msg.attach(MIMEText(msg_body, 'plain'))

        # Attach the file
        part = MIMEBase(*attachment_mimetype.split('/'))
        part.set_payload(attachment_bytes)
        encoders.encode_base64(part)
        part.add_header('Content-Disposition', f'attachment; filename="{attachment_filename}"')
        msg.attach(part)

        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(username, password)
        server.sendmail(from_email, to_email, msg.as_string())
        server.close()
        return True
    except Exception as ex:
        logger.error(f"Error sending email with attachment: {traceback.format_exc()}")
        return False, str(ex)