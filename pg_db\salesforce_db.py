from sqlalchemy import Column, DateTime, String, Integer, Float, Boolean, text
from sqlalchemy import func
import pg_db as db


class SalesforceCustomerRep(db.Base):
    __tablename__ = db.salesforce_customer_rep_table

    customer_id = Column(Integer, primary_key=True)
    customer_name = Column(String)
    type = Column(String)
    sf_id = Column(String)
    rep_name = Column(String)
    rep_email = Column(String)
    rep_phone = Column(String)
    rep_mobile = Column(String)
    is_credit_card_payment_block = Column(Boolean)
    payment_term = Column(String)
    credit_limit = Column(Float)
    rep_type = Column(String)
    price_list = Column(String)

    @classmethod
    def get_customer_rep_by_customer_id(cls, store_id, customer_id, session=None):
        local_session = None
        if not session:
            session = db.get_session(store_id)
            local_session = session
        try:
            # SQL query to get data from the orders and customer tables
            query = text(f"""
                SELECT customer_id, customer_name, type, sf_id, rep_name, rep_email, rep_phone, rep_mobile, is_credit_card_payment_block
                FROM {cls.__tablename__} where customer_id = :customer_id
            """)

            # Execute the query
            result = session.execute(query, {"customer_id": customer_id})
            return result.fetchone()
        finally:
            if local_session:
                local_session.close()

class SalesforceOrderDetails(db.Base):
    __tablename__ = db.salesforce_order_details_table

    order_id = Column(Integer, primary_key=True)
    customer_id = Column(Integer)
    amount_due = Column(Float, default=0)
    amount_paid = Column(Float, default=0)
    order_total_inc_tax = Column(Float, default=0)
    xero_order_total = Column(Float, default=0)
