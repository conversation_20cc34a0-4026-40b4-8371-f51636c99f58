from mongo_db import store_db
import mongo_db
import logging
import traceback
logger = logging.getLogger()
import logging
import pg_db
from sqlalchemy import text
from utils import store_util, email_util, bc_util
import pg_db
import re
from utils import project_notification_util
from plugin import bc_order
from datetime import datetime
import pytz
import requests
import pg_db
import time

def _fetch_customer_details(store_id, customer_ids):
    """
    Fetch customer details for multiple customers in a single query
    
    Args:
        store_id (str): Store ID
        customer_ids (list): List of customer IDs to fetch details for
        
    Returns:
        dict: Dictionary mapping customer_id to customer details
    """
    if not customer_ids:
        return {}
        
    conn = pg_db.get_connection(store_id)
    try:
        query = text("""
            SELECT 
                customer_id,
                email,
                company
            FROM 
                customers
            WHERE 
                customer_id = ANY(:customer_ids)
        """)
        result = conn.execute(query, {'customer_ids': customer_ids})
        # Convert result to dictionary with customer_id as key
        customer_details = {}
        for row in result.fetchall():
            customer_details[row[0]] = {
                'email': row[1],
                'company_name': row[2]
            }
        return customer_details
    except Exception as ex:
        error_message = "Exception: " + str(traceback.format_exc())
        logger.error(error_message)
        return {}
    finally:
        conn.close()

def _fetch_pos(store_id, bop_id, variant_ids, customer_ids):
    """
    Fetch PO details for multiple customers and variants in a single query
    
    Args:
        store_id (str): Store ID
        bop_id (int): Bulk order product ID
        variant_ids (list): List of variant IDs
        customer_ids (list): List of customer IDs
        
    Returns:
        dict: Dictionary mapping (customer_id, variant_id) to list of PO details
    """
    if not variant_ids or not customer_ids:
        return {}
        
    conn = pg_db.get_connection(store_id)
    try:
        query = text("""
            SELECT DISTINCT 
                po.po_id,
                po.customer_id,
                pol.bc_variant_id
            FROM 
                bo_purchase_orders AS po 
                JOIN bo_purchase_order_lineitems AS pol ON po.po_id = pol.po_id 
            WHERE 
                pol.bop_id = :bop_id 
                AND pol.bc_variant_id = ANY(:variant_ids)
                AND po.customer_id = ANY(:customer_ids)
                AND po.status IN ('pending', 'partially fulfilled')
        """)
        
        result = conn.execute(query, {
            'bop_id': bop_id,
            'variant_ids': variant_ids,
            'customer_ids': customer_ids
        })
        
        # Group POs by customer_id and variant_id
        po_details = {}
        for row in result.fetchall():
            po_id = row[0]
            customer_id = row[1]
            variant_id = row[2]
            key = (customer_id, variant_id)
            
            if key not in po_details:
                po_details[key] = []
                
            po_details[key].append({
                'number': f'PO-{po_id}',
                'link': f'https://adminapp.midwestgoods.com/orders/bo/po-request/pending/{po_id}/pending'
            })
            
        return po_details
        
    except Exception as ex:
        error_message = "Exception: " + str(traceback.format_exc())
        logger.error(error_message)
        return {}
    finally:
        conn.close()

def _fetch_variant_details(store_id, variant_ids):
    """
    Fetch variant details for multiple variant IDs in a single query
    
    Args:
        store_id (str): Store ID
        variant_ids (list or set): List or set of variant IDs
        
    Returns:
        dict: Dictionary mapping variant_id to variant details
    """
    if not variant_ids:
        return {}
    
    # Convert variant_ids to list if it's a set
    variant_ids_list = list(variant_ids) if isinstance(variant_ids, set) else variant_ids
        
    variant_details = mongo_db.fetchall_documents_from_storefront_collection(store_id, 'products', {'variants.id': {'$in': variant_ids_list}}, {'variants.id': 1, 'variants.sku': 1, '_id': 0})
    variant_details_map = {}
    for product in variant_details:
        # Iterate through all variants in the product
        for variant in product.get('variants', []):
            variant_id = variant.get('id')
            sku = variant.get('sku')
            if variant_id and sku:
                variant_details_map[variant_id] = sku
    return variant_details_map
    


def send_bulk_order_notification(store_id, payload):
    """
    Send bulk order notifications to sales reps for their customers
    
    Args:
        store_id (str): Store ID
        payload (dict): Bulk order payload containing product and customer data
        
    Returns:
        tuple: (success, message)
    """
    if not store_id or not payload:
        logger.warning("Missing store_id or payload for bulk order notification")
        return False, "Missing required parameters"

    try:
        # Extract product data
        bop_id = payload.get('bop_id')
        product_data = {
            'bop_id': bop_id,
            'bc_product_id': payload.get('bc_product_id'),
            'bc_name': payload.get('bc_name'),
            'bc_sku': payload.get('bc_sku')
        }

        # Group customers by sales rep and collect variant IDs
        sales_rep_customers = {}
        all_customer_ids = set()  # Track all customer IDs for batch fetch
        all_variant_ids = set()   # Track all variant IDs for batch fetch
        customer_variant_map = {} # Map customer_id to their variant IDs

        # First pass: collect all variant IDs and filter customers with updated: true
        for variant in payload.get('variants', []):
            variant_id = variant.get('bc_variant_id')
            if variant_id:
                all_variant_ids.add(variant_id)

        variant_details_map = _fetch_variant_details(store_id, all_variant_ids)

        # Second pass: process only customers with updated: true flag
        for variant in payload.get('variants', []):
            variant_id = variant.get('bc_variant_id')
                
            for customer in variant.get('customer_array', []):
                # Only process customers with updated: true flag
                if not customer.get('updated', False):
                    continue

                # Skip if requested quantity is None and allocated quantity is 0
                requested_qty = customer.get('requested_qty')
                locked_qty = customer.get('locked_qty', 0)
                
                # Skip customers with invalid or zero quantities
                if (requested_qty is None or requested_qty == 0 or 
                    locked_qty is None or locked_qty == 0):
                    continue

                sales_rep_email = customer.get('customer_rep_email')
                if not sales_rep_email:
                    continue

                customer_id = customer.get('customer_id')
                if customer_id:
                    all_customer_ids.add(customer_id)
                    if customer_id not in customer_variant_map:
                        customer_variant_map[customer_id] = set()
                    if variant_id:
                        customer_variant_map[customer_id].add(variant_id)

                if sales_rep_email not in sales_rep_customers:
                    sales_rep_customers[sales_rep_email] = {
                        'name': customer.get('customer_rep_name', 'Sales Rep'),
                        'customers': {}
                    }

                if customer_id not in sales_rep_customers[sales_rep_email]['customers']:
                    sales_rep_customers[sales_rep_email]['customers'][customer_id] = {
                        'customer_id': customer_id,
                        'customer_name': customer.get('customer_name'),
                        'customer_email': None,  # Will be fetched from DB
                        'company_name': None,    # Will be fetched from DB
                        'po_numbers': [],        # Will store PO numbers and links
                        'variants': []
                    }

                # Add variant data for this customer
                variant_data = {
                    'product_name': product_data.get('bc_name', 'N/A'),
                    'product_sku': product_data.get('bc_sku', 'N/A'),
                    'variant_option': variant.get('option', 'N/A'),
                    'variant_sku': variant_details_map.get(variant.get('bc_variant_id'), 'N/A'),
                    'option': variant.get('option', 'N/A'),
                    'bc_variant_id': variant.get('bc_variant_id'),
                    'requested_qty': customer.get('requested_qty'),
                    'locked_qty': customer.get('locked_qty', 0)
                }

                # Only add variant if it has valid data
                if (variant_data['product_name'] != 'N/A' and 
                    variant_data['product_sku'] != 'N/A' and 
                    variant_data['variant_option'] != 'N/A'):
                    sales_rep_customers[sales_rep_email]['customers'][customer_id]['variants'].append(variant_data)

        # Filter out customers with no variants
        for sales_rep_email in list(sales_rep_customers.keys()):
            for customer_id in list(sales_rep_customers[sales_rep_email]['customers'].keys()):
                if not sales_rep_customers[sales_rep_email]['customers'][customer_id]['variants']:
                    del sales_rep_customers[sales_rep_email]['customers'][customer_id]
            
            # Remove sales rep if they have no customers
            if not sales_rep_customers[sales_rep_email]['customers']:
                del sales_rep_customers[sales_rep_email]

        # Early return if no customers to process
        if not sales_rep_customers:
            logger.info("No customers with updated: true flag found for notification")
            return True, "No customers to notify"

        # Fetch all customer details in a single query
        customer_details = _fetch_customer_details(store_id, list(all_customer_ids))
        
        # Fetch all PO details in a single query
        po_details = _fetch_pos(store_id, bop_id, list(all_variant_ids), list(all_customer_ids))

        # Update customer details and PO numbers for each sales rep's customers
        for sales_rep_email, data in sales_rep_customers.items():
            for customer_id, customer in data['customers'].items():
                # Update customer details
                if customer_id in customer_details:
                    details = customer_details[customer_id]
                    customer['customer_email'] = details.get('email')
                    customer['company_name'] = details.get('company_name')
                
                # Update PO numbers for each variant
                all_pos = []
                for variant in customer['variants']:
                    variant_id = variant.get('bc_variant_id')
                    if variant_id:
                        key = (customer_id, variant_id)
                        if key in po_details:
                            all_pos.extend(po_details[key])
                
                # Remove duplicate POs while preserving order
                seen = set()
                customer['po_numbers'] = [po for po in all_pos 
                                        if not (po['number'] in seen or seen.add(po['number']))]

        # Send notifications to each sales rep
        for sales_rep_email, data in sales_rep_customers.items():
            logger.info(f"Sending notification to {sales_rep_email}")
            is_notification_enabled = project_notification_util.check_if_notification_enabled(store_id, "bulk_order_allocation", "Bulk Order", "Sales Rep Notification", sales_rep_email)
            if not is_notification_enabled:
                continue
            success, message = email_util.send_bulk_order_allocation_notification(
                store_id=store_id,
                sales_rep_email=sales_rep_email,
                sales_rep_name=data['name'],
                customer_data=list(data['customers'].values()),
                product_data=product_data
            )
            
            if not success:
                logger.error(f"Failed to send notification to {sales_rep_email}: {message}")

        return True, "Bulk order notifications sent successfully"

    except Exception as ex:
        error_message = "Exception: " + str(traceback.format_exc())
        logger.error(error_message)
        return False, error_message


def send_bulk_order_reminder_email_to_reps(store_id):
    """
    Send reminder emails to sales reps for their pending orders with locked quantities
    
    Args:
        store_id (str): Store ID
        
    Returns:
        tuple: (success, message)
    """
    if not store_id:
        logger.warning("Missing store_id for reminder email notification")
        return False, "Missing required parameters"

    try:
        conn = pg_db.get_connection(store_id)
        
        # Query to fetch pending orders with locked quantities and sum quantities
        query = text("""
            SELECT 
                po.customer_id,
                po.customer_name,
                po.customer_rep_name,
                po.customer_rep_email,
                li.bop_id,
                li.bc_variant_id,
                li.variant_id,
                li.bc_sku,
                li.option,
                COALESCE(SUM(dli.locked_qty), 0) AS total_locked_qty,
                bop.bc_product_id,
                bop.bc_name,
                bop.bc_sku as product_sku,
                c.email as customer_email,
                c.company as customer_company,
                bop.case_qty,
                bop.display_qty,
                bop.name as product_name,
                bop.min_market_price,
                bop.is_po_locked,
                COALESCE(SUM(dli.requested_qty), 0) AS total_requested_qty,
                string_agg(DISTINCT po.po_id::text, ',') as po_ids
            FROM 
                bo_purchase_orders po
            LEFT JOIN 
                bo_purchase_order_lineitems li ON po.po_id = li.po_id
            LEFT JOIN
                bo_bulk_order_products bop ON li.bop_id = bop.bop_id
            LEFT JOIN 
                bo_distribution_lineitems dli ON po.customer_id = dli.customer_id 
                AND li.bc_variant_id = dli.bc_variant_id
                AND bop.bc_product_id = dli.bc_product_id
            LEFT JOIN
                bo_distributions bd ON dli.distribution_id = bd.id
            LEFT JOIN
                customers c ON po.customer_id = c.customer_id
            WHERE 
                (po.status IS NULL OR po.status IN ('pending', 'partially fulfilled'))
                AND dli.locked_qty > 0 AND dli.locked_qty IS NOT NULL
                AND bd.end_date_time IS NULL AND bd.is_active = true
            GROUP BY 
                po.customer_id, po.customer_name, po.customer_rep_name, po.customer_rep_email,
                li.bop_id, li.bc_variant_id, li.variant_id, li.bc_sku, li.option,
                bop.bc_product_id, bop.bc_name, bop.bc_sku, c.email, c.company,
                bop.case_qty, bop.display_qty, bop.name, bop.min_market_price, bop.is_po_locked
        """)
        
        result = conn.execute(query)
        rows = result.fetchall()
        
        # Group data by sales rep email
        sales_rep_customers = {}
        product_details = {}  # Store product details by bop_id
        
        for row in rows:
            sales_rep_email = row[3]  # customer_rep_email
            if not sales_rep_email:
                continue
                
            # Store product details if not already stored
            bop_id = row[4]  # bop_id
            if bop_id not in product_details:
                product_details[bop_id] = {
                    'bc_product_id': row[10],  # bc_product_id
                    'bc_name': row[11],        # bc_name
                    'bc_sku': row[12]          # product_sku
                }
                
            if sales_rep_email not in sales_rep_customers:
                sales_rep_customers[sales_rep_email] = {
                    'name': row[2],  # customer_rep_name
                    'customers': {},
                    'product_data': product_details[bop_id]  # Store product data for this sales rep
                }
            
            customer_id = str(row[0])  # customer_id
            if customer_id not in sales_rep_customers[sales_rep_email]['customers']:
                # Initialize customer data with empty po_numbers list
                sales_rep_customers[sales_rep_email]['customers'][customer_id] = {
                    'customer_id': row[0],
                    'customer_name': row[1],
                    'customer_email': row[13],  # customer_email
                    'company_name': row[14],    # customer_company
                    'variants_by_sku': {},      # Use dictionary to group variants by SKU
                    'po_numbers': []            # Will store all POs for this customer
                }
            
            # Process PO IDs from the query
            po_ids_str = row[21]  # po_ids from query
            if po_ids_str:
                # Convert comma-separated string to list of integers
                po_ids = [int(po_id) for po_id in po_ids_str.split(',')]
                # Create PO links for each PO ID
                po_links = [
                    {
                        'number': po_id,
                        'link': f'https://adminapp.midwestgoods.com/orders/bo/po-request/pending/{po_id}/pending'
                    }
                    for po_id in sorted(po_ids)
                ]
                # Add new POs to customer's po_numbers list
                customer = sales_rep_customers[sales_rep_email]['customers'][customer_id]
                customer['po_numbers'].extend(po_links)
            
            # Group variants by SKU
            variant_sku = row[7]  # li.bc_sku
            if variant_sku not in sales_rep_customers[sales_rep_email]['customers'][customer_id]['variants_by_sku']:
                sales_rep_customers[sales_rep_email]['customers'][customer_id]['variants_by_sku'][variant_sku] = {
                    'bc_variant_id': row[5],   # bc_variant_id
                    'variant_sku': variant_sku,
                    'option': row[8],          # option
                    'requested_qty': row[20],  # total_requested_qty (summed in query)
                    'locked_qty': row[9],      # total_locked_qty (summed in query)
                }
        
        # Remove duplicate POs while preserving order for each customer
        for sales_rep_email, data in sales_rep_customers.items():
            for customer in data['customers'].values():
                seen = set()
                customer['po_numbers'] = [po for po in customer['po_numbers'] 
                                        if not (po['number'] in seen or seen.add(po['number']))]
        
        # Send notifications to each sales rep
        for sales_rep_email, data in sales_rep_customers.items():
            logging.info(f"Sending reminder notification to {sales_rep_email}")
            is_notification_enabled = project_notification_util.check_if_notification_enabled(store_id, "bulk_order_reminder", "Bulk Order", "Sales Rep Notification", sales_rep_email)
            if not is_notification_enabled:
                continue
            
            # Convert customers dict to list and variants_by_sku to list
            customer_data = []
            for customer in data['customers'].values():
                # Convert variants_by_sku to list of variants
                variants = list(customer['variants_by_sku'].values())
                
                customer_entry = {
                    'customer_id': customer['customer_id'],
                    'customer_name': customer['customer_name'],
                    'customer_email': customer['customer_email'],
                    'company_name': customer['company_name'],
                    'variants': variants,
                    'po_numbers': customer['po_numbers']  # Include deduplicated PO numbers
                }
                customer_data.append(customer_entry)
            
            # Use the product data stored from the query
            product_data = {
                'bop_id': None,  # Not needed for reminder emails
                'bc_product_id': data['product_data']['bc_product_id'],
                'bc_name': data['product_data']['bc_name'],
                'bc_sku': data['product_data']['bc_sku'],
                'variants': []  # Empty as variants are under customers
            }
            
            success, message = email_util.send_bulk_order_allocation_notification(
                store_id=store_id,
                sales_rep_email=sales_rep_email,
                sales_rep_name=data['name'],
                customer_data=customer_data,
                product_data=product_data,
                call_from_reminder=True
            )
            
            if not success:
                logger.error(f"Failed to send reminder notification to {sales_rep_email}: {message}")
        
        return True, "Reminder notifications sent successfully"
        
    except Exception as ex:
        error_message = "Exception: " + str(traceback.format_exc())
        logger.error(error_message)
        return False, error_message
    finally:
        if conn:
            conn.close()


def send_bulk_order_updated_notification(store_id, order_id):
    try:
        store = store_util.get_store_by_id(store_id)
        order_products_response = bc_order.get_order_products(store, order_id)
        if not order_products_response or order_products_response.status_code != 200:
            logger.error(f"Failed to fetch order products for order {order_id}, status: {getattr(order_products_response, 'status_code', 'No response')}")
            return False, "Failed to fetch order products"
            
        try:
            order_products = order_products_response.json()
        except Exception as e:
            logger.error(f"Failed to parse order products JSON for order {order_id}: {str(e)}")
            return False, "Failed to parse order products data"
        
        if not order_products:
            logger.info(f"No products found for order {order_id}")
            return True, "No products to process"

        # Optimized single query to fetch all required data
        conn = pg_db.get_connection(store_id)

        # Single query to get PO, customer, and sales rep details
        combined_query = text("""
            SELECT 
                pbo.po_id,
                pbo.order_total,
                po.customer_id,
                po.customer_name,
                c.email as customer_email,
                c.company as company_name,
                scr.rep_name as sales_rep_name,
                scr.rep_email as sales_rep_email
            FROM 
                bo_purchase_order_bc_order_mapping pbo
            JOIN 
                bo_purchase_orders po ON pbo.po_id = po.po_id
            LEFT JOIN 
                customers c ON po.customer_id = c.customer_id
            LEFT JOIN 
                salesforce_customer_rep scr ON po.customer_id = scr.customer_id
            WHERE 
                pbo.bc_order_id = :order_id
        """)
        
        result = conn.execute(combined_query, {'order_id': str(order_id)})
        data = result.fetchone()
        
        if not data:
            logger.info(f"No PO mapping found for order {order_id}")
            return True, "No PO mapping found"
            
        # Extract data from the single query result with safe defaults
        po_id = data[0] if data[0] is not None else 'N/A'
        customer_name = data[3] if data[3] is not None else 'N/A'
        customer_email = data[4] if data[4] is not None else 'N/A'
        company_name = data[5] if data[5] is not None else 'N/A'
        sales_rep_name = data[6] if data[6] is not None else 'N/A'
        sales_rep_email = data[7] if data[7] is not None else 'N/A'
        
        # Process order products and extract required data
        order_product_details = []
        total_order_value = 0.0  # Initialize as float to handle decimal values
        
        for product in order_products:
            try:
                # Skip products with zero quantity
                quantity = product.get('quantity', 0)
                if not quantity or quantity == 0:
                    continue
                
                # Safely convert quantity to integer
                try:
                    quantity = int(quantity)
                except (ValueError, TypeError):
                    logger.warning(f"Invalid quantity value for product: {product.get('name', 'Unknown')}, quantity: {quantity}")
                    quantity = 0
                    continue
                
                # Get variant name from product options
                options = product.get('product_options', [])
                if options:
                    try:
                        variant_name = ' - '.join(str(option.get('display_value', '')) for option in options if option.get('display_value'))
                    except Exception as e:
                        logger.warning(f"Error processing variant name for product: {product.get('name', 'Unknown')}, error: {str(e)}")
                        variant_name = 'N/A'
                else:
                    variant_name = 'N/A'

                # Get product name and combine with variant
                product_name = product.get('name', 'N/A')
                if variant_name and variant_name != 'N/A':
                    product_name = f"{product_name}<br /><strong style=\"font-size: 12px; display: block;\">{variant_name}</strong>"
                
                # Get SKU
                sku = product.get('sku', 'N/A')
                
                # Safely convert unit price to float
                unit_price = product.get('price_ex_tax', 0) or product.get('price_inc_tax', 0)
                try:
                    unit_price = float(unit_price) if unit_price else 0.0
                except (ValueError, TypeError):
                    logger.warning(f"Invalid unit price for product: {product_name}, price: {unit_price}")
                    unit_price = 0.0
                
                # Calculate total price
                total_price = unit_price * quantity
                
                # Add to total order value
                total_order_value += total_price
                
                # Add product details to list
                order_product_details.append({
                    'product_name': product_name,
                    'sku': sku,
                    'quantity': quantity,
                    'unit_price': unit_price,
                    'total_price': total_price
                })
                
            except Exception as e:
                logger.error(f"Error processing product in order {order_id}: {str(e)}")
                logger.error(f"Product data: {product}")
                continue

        # Get order date from first product (assuming all products have same date)
        order_details = bc_order.fetch_order(store, order_id)
        order_details = order_details.json()
        
        # Helper function to format dates
        def format_date(date_str):
            if not date_str or date_str == 'N/A':
                return 'N/A'
            try:
                parsed_date = datetime.strptime(date_str, '%a, %d %b %Y %H:%M:%S %z')
                return parsed_date.strftime('%B %d, %Y %I:%M %p')
            except:
                return date_str
        
        order_date = format_date(order_details.get('date_created', 'N/A') if order_details else 'N/A')
        current_date = format_date(order_details.get('date_modified', 'N/A') if order_details else 'N/A')
        
        # Prepare data for email template
        email_data = {
            'order_id': str(order_id),
            'customer_name': str(customer_name) if customer_name else 'N/A',
            'customer_email': str(customer_email) if customer_email else 'N/A',
            'company_name': str(company_name) if company_name else 'N/A',
            'sales_rep_name': str(sales_rep_name) if sales_rep_name else 'N/A',
            'sales_rep_email': str(sales_rep_email) if sales_rep_email else 'N/A',
            'po_id': str(po_id) if po_id else 'N/A',
            'order_date': str(order_date) if order_date else 'N/A',
            'order_update_date': str(current_date),
            'order_product_details': order_product_details,
            'total_order_value': total_order_value
        }

        # Send email notification using the email utility
        success, message = email_util.send_bulk_order_updated_notification(
            store_id=store_id,
            email_data=email_data
        )
        
        if not success:
            logger.error(f"Failed to send bulk order updated notification: {message}")
            return False, message

        return True, "Bulk order updated notification sent successfully"

    except Exception as ex:
        error_message = "Exception: " + str(traceback.format_exc())
        logger.error(error_message)
        return False, error_message
    finally:
        conn.close()
    

def update_order_notes(store_id, order_id, username, customer_id, normal_order=False):
    """
    Update order notes by calling the external serial_history API
    
    Args:
        store: Store configuration
        order_id: BigCommerce order ID
        order_notes: New note content to add
    
    Returns:
        API response from the serial_history endpoint
    """
    time.sleep(60)
    # API endpoint
    url = "https://midwestgoods.mobi/serial_history"
    
    # Headers
    headers = {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'tok': 'saddasdgf1PFKxsfsfazzEKnRcxsgYdsfxzzzI2'
    }
    user = store_db.fetch_user_by_username(store_id, username)
    rep = _fetch_customer_rep(store_id, customer_id)
    rep_name = rep[0] if rep else None
    rep_email = rep[1] if rep else None

    # Get current time in CST/CDT (automatically handles daylight saving time)
    central_tz = pytz.timezone('US/Central')
    current_time = datetime.now(central_tz).strftime('%Y-%m-%d %H:%M:%S %Z')

    # Determine order source
    order_source = "Express" if normal_order else "Bulk order feature"
    
    # Build user part of the message
    user_part = f"{user['name']}: {username}" if user else username
    
    # Build the complete order notes
    order_notes = f"Order was placed by {user_part} from {order_source} at {current_time}. Order managed by {rep_name}: {rep_email}"
    
    # Request payload
    payload = {
        "Clicker": username,
        "BC_OrderId__c": str(order_id),
        "CBD_Order_Id__c": "",
        "new_note": order_notes
    }
    
    try:
        # Make the API call
        response = requests.post(
            url=url,
            headers=headers,
            json=payload,
            timeout=30  # 30 second timeout
        )
        
        # Return the response
        return response
        
    except requests.exceptions.RequestException as e:
        # Log the error and return None or raise exception based on your error handling strategy
        logger.error(traceback.format_exc())
        return None

def _fetch_customer_rep(store_id, customer_id):
    conn = pg_db.get_connection(store_id)
    try:
        query = """SELECT rep_name, rep_email FROM salesforce_customer_rep WHERE customer_id = :customer_id"""
        result = conn.execute(text(query), {'customer_id': int(customer_id)})
        return result.fetchone()
    except Exception as e:
        logger.error(traceback.format_exc())
        return None
    finally:
        conn.close()
