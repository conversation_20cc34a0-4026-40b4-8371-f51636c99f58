from sqlalchemy.dialects.postgresql import insert
import pg_db as db
from sqlalchemy import Boolean, Column, DateTime, String, Integer, ForeignKey, Float, text
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import insert
import pg_db as db
from sqlalchemy import Boolean, Column, DateTime, String, Integer, ForeignKey
from sqlalchemy.sql import func

class BulkProductBrands(db.Base):
    __tablename__ = "bo_bulk_products_brands"    
    id = Column(Integer, primary_key=True, unique=True, nullable=False)    
    brand_name = Column(String(200), nullable=False, unique=True)
    products_count = Column(Integer, default=0)
    status = Column(String, default='active')  # options - draft, active, inactive
    created_by = Column(String(100), nullable=False)
    updated_by = Column(String(100), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())

class BulkOrderProducts(db.Base):
    __tablename__ = "bo_bulk_order_products"    
    bop_id = Column(Integer, primary_key=True, unique=True, nullable=False)    
    bc_sku = Column(String, unique=True, nullable=True)
    bc_product_id = Column(Integer, nullable=False)
    bc_name = Column(String(200), nullable=True)  
    name = Column(String(200), nullable=False)    
    product_image = Column(String, nullable=True)    
    display_qty = Column(Float, nullable=False)    
    case_qty = Column(Float, nullable=False)
    status = Column(String, default='draft')  # options - draft, active, inactive, archived
    type = Column(String, default='bulkorder')  # options - bulkorder, preorder 
    is_qty_locked = Column(Boolean, default=False) # options - true, false
    orders = Column(Integer, default=0)             
    created_by = Column(String(100), nullable=False)
    updated_by = Column(String(100), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())
    is_po_locked = Column(Boolean, default=False)
    brand_id = Column(Integer, ForeignKey(BulkProductBrands.__tablename__ + '.id'), nullable=False, default=0)
    min_market_price = Column(Float, default=0)
    is_marketing_product = Column(Boolean, default=False)

class BulkOrderProductVariants(db.Base):
    __tablename__ = "bo_product_variants"
    id = Column(Integer, primary_key=True, autoincrement=True)
    bop_id = Column(Integer, ForeignKey(BulkOrderProducts.__tablename__ + '.bop_id'), nullable=False)
    bc_sku = Column(String, nullable=True)
    bc_upc = Column(String, nullable=True)
    bc_variant_id = Column(Integer, nullable=True)
    bo_upc = Column(String, nullable=True)
    po_option = Column(String, nullable=True)
    option = Column(String, nullable=True)  
    current_stock = Column(Float, default=0)    

class PurchaseOrders(db.Base):
    __tablename__ = "bo_purchase_orders"
    
    po_id = Column(Integer, primary_key=True, unique=True, nullable=False)
    customer_id = Column(Integer, nullable=False)
    customer_name = Column(String, nullable=False)
    customer_rep_id = Column(Integer, nullable=True)
    customer_rep_name = Column(String, nullable=True)
    customer_rep_email = Column(String, nullable=True)
    status = Column(String, default='pending')  # options - pending, completed  
    type = Column(String, default='bulkorder')  # options - bulkorder, preorder    
    created_by = Column(String(100), nullable=False)
    updated_by = Column(String(100), nullable=True)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())   
    reason = Column(String, nullable=True)
class PurchaseOrderLineItems(db.Base):
    __tablename__ = "bo_purchase_order_lineitems"
    id = Column(Integer, primary_key=True, autoincrement=True)
    po_id = Column(Integer, ForeignKey(PurchaseOrders.__tablename__ + '.po_id'), nullable=False)
    bop_id = Column(Integer)
    variant_id = Column(Integer, ForeignKey(BulkOrderProductVariants.__tablename__ + '.id'), nullable=False)
    bc_variant_id = Column(Integer, nullable=True)
    bo_upc = Column(String, nullable=True)
    bc_upc = Column(String, nullable=True) 
    bc_sku = Column(String, nullable=True)       
    price = Column(Float)
    option = Column(String, nullable=True) 
    po_option = Column(String, nullable=True)   
    requested_qty = Column(Float, default=0)
    fullfilled_qty = Column(Float, default=0)
    remaining_qty = Column(Float, default=0)
    approved_qty = Column(Float, default=0)
    status = Column(String)     # options - backorder, completed, cancelled 
   
class POBulkOrders(db.Base):
    __tablename__ = "bo_purchase_order_bc_order_mapping"
    id = Column(Integer, primary_key=True, autoincrement=True)
    po_id = Column(Integer, ForeignKey(PurchaseOrders.__tablename__ + '.po_id'), nullable=False)
    bc_order_id = Column(String)    
    order_total = Column(Float)
    cart_id = Column(String)
    created_by = Column(String(100), nullable=False)
    updated_by = Column(String(100), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())

    @classmethod
    def get_bulk_order(cls, store_id, order_id, session=None):
        local_session = None
        if not session:
            session = db.get_session(store_id)
            local_session = session
        try:
            # SQL query to get data from the orders and customer tables
            query = text(f"""
                SELECT id, po_id, bc_order_id, order_total, cart_id, created_by, updated_by, created_at, updated_at 
                FROM bo_purchase_order_bc_order_mapping where bc_order_id = '{str(order_id)}'
            """)

            # Execute the query
            result = session.execute(query)
            return result.fetchone()
        finally:
            if local_session:
                local_session.close()

    @classmethod
    def update_bulk_order(cls, store_id, order_total, order_id, session=None):
        local_session = None
        if not session:
            session = db.get_session(store_id)
            local_session = session
        try:
            # SQL query to get data from the orders and customer tables
            update_sql = text(f"""
                UPDATE bo_purchase_order_bc_order_mapping SET order_total = {order_total} WHERE bc_order_id = '{str(order_id)}'
            """)
            # Execute the query
            session.execute(update_sql)
        finally:
            if local_session:
                local_session.commit()
                local_session.close()
            

class distributions(db.Base):
    __tablename__ = "bo_distributions"
    id = Column(Integer, primary_key=True, autoincrement=True)
    bop_id = Column(Integer, ForeignKey(BulkOrderProducts.__tablename__ + '.bop_id'), nullable=False)
    start_date_time = Column(DateTime, default=func.now())
    end_date_time = Column(DateTime, default=None)
    is_qty_locked = Column(Boolean, default=False) # options - true, false
    is_active = Column(Boolean, default=False)     # options - true, false
    is_published = Column(Boolean, default=False)  # options - true, false
    created_at = Column(DateTime, server_default=func.now())  
    created_by = Column(String(100), nullable=False)
    updated_at = Column(DateTime, onupdate=func.now())  
    updated_by = Column(String(100), nullable=False)   
    orders = Column(Integer, default=0)
    salse_rep_count = Column(Integer, default=0)
    is_po_locked = Column(Boolean, default=False)
class distributions_lineitems(db.Base):
    __tablename__ = "bo_distribution_lineitems"
    id = Column(Integer, primary_key=True, autoincrement=True)
    distribution_id = Column(Integer, ForeignKey(distributions.__tablename__ + '.id'), nullable=False)
    bc_product_id = Column(Integer)    
    bc_sku = Column(String)
    variant_id = Column(Integer, ForeignKey(BulkOrderProductVariants.__tablename__ + '.id'), nullable=False)
    bc_variant_id = Column(Integer)
    bo_upc = Column(String)
    bc_upc = Column(String)
    option = Column(String, nullable=True)
    po_option = Column(String, nullable=True) 
    available_qty = Column(Float, default=0)
    requested_qty = Column(Float, default=0)
    locked_qty = Column(Float, default=0)
    distributed_qty = Column(Float, default=0)
    customer_id = Column(Integer, nullable=False)
    customer_name = Column(String(100), nullable=False) 
    customer_rep_id = Column(Integer, nullable=True)
    customer_rep_name = Column(String(100), nullable=True) 
    created_at = Column(DateTime, server_default=func.now()) 
    created_by = Column(String(100), nullable=False)
    previously_distributed_qty = Column(Float, default=0)
    customer_rep_email = Column(String, nullable=True)

class bo_published_distribution_logs(db.Base):
    __tablename__ = "bo_published_distribution_logs"
    id = Column(Integer, primary_key=True, autoincrement=True)
    distribution_id = Column(Integer, ForeignKey(distributions.__tablename__ + '.id'), nullable=False)
    bc_product_id = Column(Integer)    
    bc_sku = Column(String)
    variant_id = Column(Integer, ForeignKey(BulkOrderProductVariants.__tablename__ + '.id'), nullable=False)
    bc_variant_id = Column(Integer)
    bo_upc = Column(String)
    bc_upc = Column(String)
    option = Column(String, nullable=True)
    po_option = Column(String, nullable=True) 
    available_qty = Column(Float, default=0)
    requested_qty = Column(Float, default=0)
    locked_qty = Column(Float, default=0)
    customer_id = Column(Integer, nullable=False)
    customer_name = Column(String(100), nullable=False) 
    customer_rep_id = Column(Integer, nullable=True)
    customer_rep_name = Column(String(100), nullable=True) 
    created_at = Column(DateTime, server_default=func.now()) 
    created_by = Column(String(100), nullable=False)