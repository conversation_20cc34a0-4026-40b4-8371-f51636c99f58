from enum import Enum
from mongo_db import store_db
import mongo_db
from utils import cache_util

class AppName(Enum):
    save_your_cart = 'save_your_cart'
    instock_notify = 'instock_notify'
    search_spring = 'search_spring'
    skuvault = 'skuvault'
    salesforce = 'salesforce'
    twillio = 'twillio'
    smpt = "smpt"
    sms_api = "sms_api"
    zoho = "zoho"

class Template(Enum):
    reset_password = 'reset_password'
    customer_signup = 'customer_signup'
    hide_product_alert = 'hide_product_alert'
    loyalty_points_alert = 'loyalty_points_alert'
    system_health_check = 'system_health_check'
    no_order_alert = 'no_order_alert'
    job_failed_alert = "job_failed_alert"
    replenishment_csv_data = "Replenishment_csv"
    replenishment_dashboard_csv_data = "Replenishment_dashboard_csv"
    safety_stock_alert = "safety_stock_alert"
    customer_tracking_csv = "customer_tracking_csv"
    bulk_product_global_report_csv = "bulk_product_global_report_csv"
    liquidated_products_csv = "liquidated_products_csv"
    order_notification_to_compliance = "order_notification_to_compliance"
    price_list_import = "price_list_import"
    price_list_export_csv = "price_list_export_csv"
    customer_profitability_csv = "customer_profitability_csv"
    product_wise_profitability_csv = "product_wise_profitability_csv"
    suppliers_profitability_csv = "suppliers_profitability_csv"
    classifications_profitability_csv = "classifications_profitability_csv"
    orders_profitability_csv = "orders_profitability_csv"
    brands_profitability_csv = "brands_profitability_csv"
    replenishment_daily_sold_product_csv = "replenishment_daily_sold_product_csv"
    daily_sold_discontinued_products_csv = "replenishment_discontinued_daily_sold_product_csv"
    no_sold_product_csv = "no_sold_product_csv"
    ticket_assigned_or_created_notification = "ticket_assigned_or_created_notification"
    ticket_comment_added_notification = "comment_added"
    bulk_order_allocation_notification = "bulk_order_allocation_notification"
    bulk_order_reminder_notification = "bulk_order_reminder_notification"
    bulk_order_updated_notification = "bulk_order_updated_notification"

def get_active_stores():
    return store_db.fetch_active_stores()

def get_store_by_storehash(store_hash):
    store = cache_util.get_store(store_hash)
    if not store:
        store = store_db.fetch_store_by_storehash(store_hash)
        if store:
            store = mongo_db.process_data(store)
    return store

def get_store_by_id(store_id):
    store = cache_util.get_store(store_id)
    if not store:
        store = store_db.fetch_store_by_id(store_id)
        if store:
            store = mongo_db.process_data(store)
    return store

def get_email_template(store_id, template_id):
    template = store_db.fetch_email_template(store_id, template_id)
    return template

def get_bc_api_creds(store):
    api_creds = {}
    if store:
        bc_info = store.get("bc_config", None)
        api = None
        if bc_info:
            api = bc_info.get("api", None)  
            api_creds["store_hash"] = bc_info.get("store_hash", None)
            api_creds["channel_id"] = bc_info.get("channel_id", 1)
            api_creds["store_url"] = bc_info.get("store_url", None)
            if api:
                api_creds["client_id"] = api.get("client_id", None)
                api_creds["access_token"] = api.get("access_token", None)
                api_creds["secret"] = api.get("secret", None)
        else:
            bc_info = store.get("api", None)
            if bc_info:
                api = bc_info.get("bcV3Api", None) 
                api_creds["store_hash"] = bc_info.get("storeHash", None)
                api_creds["channel_id"] = bc_info.get("channelId", 1)
                api_creds["store_url"] = bc_info.get("bcStoreUrl", None)
                if api:
                    api_creds["client_id"] = api.get("clientId", None)
                    api_creds["access_token"] = api.get("accessToken", None)
                    api_creds["secret"] = api.get("secret", None)

    return api_creds

def get_salesforce_api_data(store_id):
    return store_db.fetch_app_api_data(store_id, AppName.salesforce.value)

def get_bc_db(store):
    return store["db"]

def get_store_db(store):
    return store["db"]

def get_cms_db(store):
    return store["db"]

def get_skuvault_api_info(store_id):
    return store_db.fetch_app_api_data(store_id, AppName.skuvault.value)

def get_twillio_api_info(store_id):
    return store_db.fetch_app_api_data(store_id, AppName.twillio.value)

def get_smpt_info(store_id):
    return store_db.fetch_app_api_data(store_id, AppName.smpt.value)

def get_sms_api_info(store_id):
    return store_db.fetch_app_api_data(store_id, AppName.sms_api.value)

def get_zoho_api_info(store_id):
    return store_db.fetch_app_api_data(store_id, AppName.zoho.value)

def get_cdn_base_url(store):
    base_url = None
    cdn = store.get("cdn", None)
    if cdn:
        base_url = cdn.get("base_url", None)
    
    if not base_url:
        base_url = store.get("image_cdn_baseurl", None)

    return base_url