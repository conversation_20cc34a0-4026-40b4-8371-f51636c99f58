import datetime
import pg_db_utils
import pg_db as db
from pg_db import replenishment_db
from pg_db_utils import pg_order_util
from utils import store_util
import logging
import traceback

logger = logging.getLogger()

def fetch_orders(store, query_params={}):
    line_items = []
    page = 1
    while True:
        logger.info("Fetching Orders page: " + str(page))
        query_params["limit"] = 250
        query_params["page"] = page

        req_body = {
            "query_params": query_params,
            "method": "GET",
            "url": "v2/orders"
        }

        res = pg_db_utils.process_api(req_body, store)

        if res and res['status_code'] == 200 and res['data']:
            if len(res['data']) > 0:

                for row in res['data']:
                    logger.info("Processing order " + str(row["id"]) + ", modified at: " + row["date_modified"])

                    _line_items, _discounts = pg_order_util.fetch_order_line_items(store, row["id"], row['date_modified'])
                    if _line_items and len(_line_items) > 0:
                        line_items.extend(_line_items)                    
                
                if len(res['data']) < 250:
                    break
            else:
                break
        else:
            break
        page = page + 1
    process_data(store['id'], line_items)

def refresh_replenishment_reserved_table(store_id):
    store = store_util.get_store_by_id(store_id)
    min_date_created_iso = (datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(days=365)).isoformat()
    
    query_params = {
        "customer_id": 28777,
        "status_id": 7,
        "min_date_created": min_date_created_iso
    }
    
    fetch_orders(store, query_params)


def build_repenishment_reserved_variants_model(line_item):
    return replenishment_db.ReplenishmentReservedVariants(
        order_id=int(line_item['order_id']),
        order_line_item_id = int(line_item['id']),
        product_id = int(line_item['product_id']),
        sku = line_item['sku'],
        variant_id = int(line_item['variant_id']),
        quantity = int(line_item['quantity']),
        last_updated_datetime = line_item['last_updated_datetime']
    )

def save_data(store_id, dto_list, dto_to_model_convertor):
    session = db.get_session(store_id)
    try:
        # clean up the table before inserting new data
        replenishment_db.ReplenishmentReservedVariants.clear_table(store_id, session)
        for dto in dto_list:
            model = dto_to_model_convertor(dto)
            session.add(model)
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        session.commit()
        session.close()

def process_data(store_id, line_items):
    if len(line_items) > 0:
        save_data(store_id, line_items, build_repenishment_reserved_variants_model)