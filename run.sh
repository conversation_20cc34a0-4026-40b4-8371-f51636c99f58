#!/bin/bash

#docker stop task-worker
#docker run --name task-worker --network=ad-net -v /app/logs/worker:/app/logs --rm -d task-worker:latest

echo "Stopping running instances..."
docker ps --filter name=task-worker* -aq | xargs docker stop

INSTANCES=1

if [ ! -z "$1" ]
then
        INSTANCES=$1
fi

for (( c=1; c<=$INSTANCES; c++ ))
do
    echo "Starting instance task-worker-$c"
    docker run --name task-worker-$c --network=ad-net -v /app/images:/app/images -v /app/logs/worker/$c:/app/logs -v /app/reports:/app/reports --rm -d task-worker:latest
done