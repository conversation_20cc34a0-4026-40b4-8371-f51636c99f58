FROM python:3.12

RUN pip install flask-cors sqlalchemy psycopg2-binary pandas ipinfo jinja2 flask flask-restful flask-cors requests celery pymongo redis twilio gspread
RUN pip install --upgrade google-api-python-client oauth2client google-auth google-auth-oauthlib google-auth-httplib2

RUN mkdir -p /app
COPY ./ /app/
RUN mkdir -p /app/logs
RUN mkdir -p /app/images
RUN chmod +x /app/entrypoint.sh

WORKDIR /app
ENV APP_ENV=Production

ENTRYPOINT ["/bin/sh", "entrypoint.sh"]
