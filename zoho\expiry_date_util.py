import time
import logging
import traceback
import zoho
import pg_db as db
import traceback
import calendar
from datetime import datetime
from collections import defaultdict
import psycopg2.extras
from sqlalchemy import text

logger = logging.getLogger()

def get_expiry_dates(store_id):
    query_params = {
        "max_records": 1000,
        # "fields": "Sku,Expiry_Date,Modified_Time,Added_Time",
        "criteria": "Added_Time>='2023-01-01'"
    }

    records = zoho.fetch_records(
        store_id=store_id,
        application_name="inventory-aisle-stocking",
        report_name="All_Overstock_Logs",
        query_params=query_params
    )
    logger.info(f"Total records fetched from Zoho: {len(records)}")

    if not records:
        logger.info(f"No records found from Zoho for store_id {store_id}")
        return

    conn = db.get_connection(store_id)
    cursor = conn.connection.cursor()  # ✅ Access raw psycopg2 connection under SQLAlchemy

    try:
        # Step 1: Process records in memory
        sku_to_expiries = defaultdict(set)

        for record in records:
            sku = record.get("Sku", "").strip()
            expiry = record.get("Expiry_Date", "").strip()
            if not sku:
                continue
            
            parsed = parse_expiry_date(expiry)
            if parsed:
                sku_to_expiries[sku].add(parsed)
            
        if not sku_to_expiries:
            logger.info(f"No valid expiry dates found in Zoho records for store_id {store_id}")
            return

        skus = list(sku_to_expiries.keys())

        parent_map = {}
        for chunk in chunked(skus, 1000):
            parent_map_query = "SELECT variants_sku, parent_sku FROM variants WHERE variants_sku = ANY(:skus)"
            reuslt_parent_map = conn.execute(text(parent_map_query), {"skus": chunk})
            parent_map.update({row[0]: row[1] for row in reuslt_parent_map.fetchall()})


        existing_records = {}
        for chunk in chunked(skus, 1000):
            existing_records_query = "SELECT sku, expiry_dates FROM zoho_data WHERE sku = ANY(:skus)"
            reuslt_existing_records = conn.execute(text(existing_records_query), {"skus": chunk})
            existing_records.update({row[0]: set(row[1]) for row in reuslt_existing_records.fetchall()})

        inserts = []
        updates = []

        for sku, new_expiry_dates in sku_to_expiries.items():
            merged_expiry_dates = new_expiry_dates
            if sku in existing_records:
                merged_expiry_dates |= existing_records[sku]  # union
                action = "update"
            else:
                action = "insert"

            unique_dates = sorted(merged_expiry_dates)
            oldest = min(unique_dates) if unique_dates else None
            parent_sku = parent_map.get(sku)

            if action == "insert":
                inserts.append((sku, list(unique_dates), parent_sku, oldest))
            else:
                updates.append((list(unique_dates), oldest, sku))

        # Step 4: Bulk insert new records
        if inserts:
            psycopg2.extras.execute_values(
                cursor,
                """
                INSERT INTO zoho_data (sku, expiry_dates, parent_sku, oldest_date)
                VALUES %s
                ON CONFLICT (sku) DO NOTHING
                """,
                inserts
            )
            conn.connection.commit()
            # logger.info(f"Inserted {len(inserts)} records into zoho_data")

        # Step 5: Bulk update existing records
        for expiry_dates, oldest_date, sku in updates:
            cursor.execute(
                """
                UPDATE zoho_data
                SET expiry_dates = %s, oldest_date = %s
                WHERE sku = %s
                """,
                (expiry_dates, oldest_date, sku)
            )
            conn.connection.commit()
            # logger.info(f"Updated {len(updates)} records in zoho_data")

        logger.info(f"Processed {len(records)} records and updated zoho_data")

    except Exception as e:
        conn.rollback()
        logger.error(f"Error processing expiry dates for store_id {store_id}: {e}")
        logger.error(traceback.format_exc())
    finally:
        cursor.close()
        conn.close()


def chunked(iterable, size):
    for i in range(0, len(iterable), size):
        yield iterable[i:i + size]

def parse_expiry_date(raw_expiry):
    try:
        expiry = raw_expiry.strip().strip("()")  # Remove parentheses

        # Try format like '01-SEP-2026'
        try:
            dt = datetime.strptime(expiry, "%d-%b-%Y")
            return dt.strftime("%Y-%m")
        except Exception as e:
            pass

        # Try format like 'SEP-2026'
        try:
            dt = datetime.strptime(expiry, "%b-%Y")
            return dt.strftime("%Y-%m")
        except Exception as e:
            pass

        # Try format like 'March-2025'
        try:
            dt = datetime.strptime(expiry, "%B-%Y")
            return dt.strftime("%Y-%m")
        except Exception as e:
            pass

        raise Exception(f"Unsupported format: {raw_expiry}")

    except Exception as e:
        return None

def update_zoho_returns_data(store_id):
    get_rtv_data(store_id)
    logger.info("get_rtv_data completed")

    get_customer_returns_data(store_id)
    logger.info("get_customer_returns_data completed")
    
def get_rtv_data(store_id):
    query_params = {
        "max_records": 1000,
        "criteria": "Added_Time>='2025-01-01'"
    }

    records = zoho.fetch_records(
        store_id=store_id,
        application_name="purchasing-department",
        report_name="All_Rtv_Products",
        query_params=query_params
    )
    logger.info(f"Total RTV records fetched from Zoho: {len(records)}")

    if not records:
        logger.info(f"No RTV records found from Zoho for store_id {store_id}")
        return

    conn = db.get_connection(store_id)
    cursor = conn.connection.cursor()

    try:
        # Create table if it doesn't exist
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS zoho_rtv_data (
                id SERIAL PRIMARY KEY,
                variant_sku TEXT NOT NULL,
                parent_sku TEXT,
                rtv_date TIMESTAMP,
                rtv_number TEXT,
                rtv_status TEXT,
                quantity INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                supplier_company_name TEXT
            )
        """)
        conn.connection.commit()

        # Get parent SKUs mapping
        variant_skus = extract_skus(records)
        parent_map = {}
        
        for chunk in chunked(variant_skus, 1000):
            parent_map_query = "SELECT variants_sku, parent_sku FROM variants WHERE variants_sku = ANY(:skus)"
            result_parent_map = conn.execute(text(parent_map_query), {"skus": chunk})
            parent_map.update({row[0]: row[1] for row in result_parent_map.fetchall()})

        # Get existing RTV records to avoid duplicates
        existing_records = set()
        cursor.execute("SELECT variant_sku, rtv_number FROM zoho_rtv_data")
        for row in cursor.fetchall():
            existing_records.add((row[0], row[1]))

        # Process and insert records
        inserts = []
        updates = []
        skipped = 0

        for record in records:
            # Extract SKU from nested structure: SKU.SKU_ID
            sku_data = record.get("SKU", {})
            if isinstance(sku_data, dict):
                variant_sku = sku_data.get("SKU_ID", "").strip()
            else:
                variant_sku = ""

            if not variant_sku:
                continue

            # Extract RTV_Date from nested structure: RTV_Order.Added_Time
            rtv_order_data = record.get("RTV_Order", {})
            company_details = record.get("RTV_Order.Company_Name", {})
            
            # Extract RTV_Number from nested structure: RTV_Order.Order_Number
            if isinstance(rtv_order_data, dict):
                rtv_number = rtv_order_data.get("Order_Number", "").strip()
            else:
                rtv_number = ""

            if isinstance(company_details, dict):
                supplier_company_name = company_details.get("Supplier_Company_Name", "").strip()
            else:
                supplier_company_name = ""

            # Extract RTV_Status from nested structure: RTV_Order.RTV_Status
            rtv_status = record.get("RTV_Order.RTV_Status", "").strip()
            rtv_date_raw = record.get("RTV_Order.Added_Time", "").strip()

            # Extract Quantity: Qty
            quantity_raw = record.get("Qty", "0")

            # Skip if missing critical data
            if not rtv_number:
                skipped += 1
                continue

            # Parse date
            rtv_date = None
            if rtv_date_raw:
                rtv_date = parse_rtv_date(rtv_date_raw)

            # Parse quantity
            try:
                quantity = int(quantity_raw)
            except (ValueError, TypeError):
                quantity = 0

            # Get parent SKU
            parent_sku = parent_map.get(variant_sku)

            # Check if this is a new record or update
            if (variant_sku, rtv_number) in existing_records:
                updates.append((rtv_date, rtv_status, quantity, variant_sku, rtv_number, supplier_company_name))
            else:
                inserts.append((variant_sku, parent_sku, rtv_date, rtv_number, rtv_status, quantity, supplier_company_name))

        # Bulk insert new records
        if inserts:
            psycopg2.extras.execute_values(
                cursor,
                """
                INSERT INTO zoho_rtv_data 
                (variant_sku, parent_sku, rtv_date, rtv_number, rtv_status, quantity, supplier_company_name)
                VALUES %s
                """,
                inserts
            )
            conn.connection.commit()
            logger.info(f"Inserted {len(inserts)} new RTV records")

        # Update existing records
        for update_data in updates:
            cursor.execute(
                """
                UPDATE zoho_rtv_data
                SET rtv_date = %s, rtv_status = %s, quantity = %s, supplier_company_name = %s
                WHERE variant_sku = %s AND rtv_number = %s
                """,
                update_data
            )
        
        if updates:
            conn.connection.commit()
            logger.info(f"Updated {len(updates)} existing RTV records")
            
        if skipped:
            logger.info(f"Skipped {skipped} records due to missing critical data")

        logger.info(f"Successfully processed RTV data for store_id {store_id}")

    except Exception as e:
        conn.rollback()
        logger.error(f"Error processing RTV data for store_id {store_id}: {e}")
        logger.error(traceback.format_exc())
    finally:
        cursor.close()
        conn.close()

def get_customer_returns_data(store_id):
    query_params = {
        "max_records": 1000,
        "criteria": "Added_Time>='2025-01-01'"
    }

    records = zoho.fetch_records(
        store_id=store_id,
        application_name="returns-department",
        report_name="Return_Product_Details_Report",
        query_params=query_params
    )
    logger.info(f"Total Customer Returns records fetched from Zoho: {len(records)}")

    if not records:
        logger.info(f"No Customer Returns records found from Zoho for store_id {store_id}")
        return

    conn = db.get_connection(store_id)
    cursor = conn.connection.cursor()

    try:
        # Create table if it doesn't exist
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS zoho_customer_returns_data (
                id SERIAL PRIMARY KEY,
                customer_name TEXT,
                variant_sku TEXT NOT NULL,
                parent_sku TEXT,
                return_id TEXT,
                return_date TIMESTAMP,
                order_quantity INTEGER,
                customer_email TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        conn.connection.commit()

        # Get parent SKUs mapping
        variant_skus = extract_skus(records)
        parent_map = {}
        
        for chunk in chunked(variant_skus, 1000):
            parent_map_query = "SELECT variants_sku, parent_sku FROM variants WHERE variants_sku = ANY(:skus)"
            result_parent_map = conn.execute(text(parent_map_query), {"skus": chunk})
            parent_map.update({row[0]: row[1] for row in result_parent_map.fetchall()})

        # Get existing RTV records to avoid duplicates
        existing_records = set()
        cursor.execute("SELECT variant_sku, return_id FROM zoho_customer_returns_data")
        for row in cursor.fetchall():
            existing_records.add((row[0], row[1]))

        # Process and insert records
        inserts = []
        updates = []
        skipped = 0

        for record in records:
            # Extract SKU from nested structure: SKU.SKU_ID
            sku_data = record.get("SKU", {})
            if isinstance(sku_data, dict):
                variant_sku = sku_data.get("SKU_ID", "").strip()
            else:
                variant_sku = ""

            if not variant_sku:
                continue

            # Extract RTV_Date from nested structure: RTV_Order.Added_Time
            return_date_data = record.get("Date_Time", {})
            customer_name = record.get("Date_Time.Customer_Name", "")
            customer_email = record.get("Date_Time.Customer_Email", "")
            
            # Extract RTV_Number from nested structure: RTV_Order.Order_Number
            if isinstance(return_date_data, dict):
                return_date_raw = return_date_data.get("Date_Time", "").strip()
            else:
                return_date_raw = ""
                
            # Extract RTV_Status from nested structure: RTV_Order.RTV_Status
            # rtv_status = record.get("RTV_Order.RTV_Status", "").strip()
            # rtv_date_raw = record.get("RTV_Order.Added_Time", "").strip()

            # Extract Quantity: Qty
            # quantity_raw = record.get("Qty", "0")
            order_quantity_raw = record.get("Order_Qty", "0")
            return_id = record.get("ID", "")

            # Skip if missing critical data
            if not return_id:
                skipped += 1
                continue

            # Parse date
            rtv_date = None
            if return_date_raw:
                return_date = parse_rtv_date(return_date_raw)

            # Parse quantity
            try:
                order_quantity = int(order_quantity_raw)
            except (ValueError, TypeError):
                order_quantity = 0

            # Get parent SKU
            parent_sku = parent_map.get(variant_sku)

            # Check if this is a new record or update
            if (variant_sku, return_id) in existing_records:
                updates.append((return_date, order_quantity, variant_sku, return_id, customer_name, customer_email))
            else:
                inserts.append((customer_name, variant_sku, parent_sku, return_date, order_quantity, return_id, customer_email))

        # Bulk insert new records
        if inserts:
            psycopg2.extras.execute_values(
                cursor,
                """
                INSERT INTO zoho_customer_returns_data 
                (customer_name, variant_sku, parent_sku, return_date, order_quantity, return_id, customer_email)
                VALUES %s
                """,
                inserts
            )
            conn.connection.commit()
            logger.info(f"Inserted {len(inserts)} new Customer Returns records")

        # Update existing records
        for update_data in updates:
            cursor.execute(
                """
                UPDATE zoho_customer_returns_data
                SET return_date = %s, order_quantity = %s, customer_name = %s, customer_email = %s
                WHERE variant_sku = %s AND return_id = %s
                """,
                update_data
            )
        
        if updates:
            conn.connection.commit()
            logger.info(f"Updated {len(updates)} existing Customer Returns records")
            
        if skipped:
            logger.info(f"Skipped {skipped} records due to missing critical data")

        logger.info(f"Successfully processed Customer Returns data for store_id {store_id}")

    except Exception as e:
        conn.rollback()
        logger.error(f"Error processing Customer Returns data for store_id {store_id}: {e}")
        logger.error(traceback.format_exc())
    finally:
        cursor.close()
        conn.close()

def parse_rtv_date(raw_date):
    """Parse RTV date from various formats to ISO format"""
    try:
        date_str = raw_date.strip()

        # Try common formats including the new format with time
        formats = [
            "%d-%b-%Y %I:%M:%S %p",  # "30-May-2025 02:20:54 PM"
            "%d-%b-%Y",              # "30-May-2025"
            "%Y-%m-%d",
            "%m/%d/%Y",
            "%b %d, %Y"
        ]

        for fmt in formats:
            try:
                dt = datetime.strptime(date_str, fmt)
                return dt  # Return as datetime object for database, including time if available.
            except ValueError:
                continue

        # If all formats fail
        logger.warning(f"Unsupported date format: {raw_date}")
        return None

    except Exception as e:
        logger.error(f"Error parsing date {raw_date}: {e}")
        return None

def extract_skus(records):
    skus = []
    for record in records:
        # Try direct field first
        sku = ""
        if not sku:
            # Try nested field if direct is missing or empty
            sku_data = record.get("SKU", {})
            if isinstance(sku_data, dict):
                sku = sku_data.get("SKU_ID", "").strip()
        if sku:
            skus.append(sku)
    return skus
