def generate_price_table(title: str, price_map: dict) -> str:
    rows = ""
    for name, price_desc in price_map.items():
        rows += f"""
        <tr>
          <th
            class="textEditor_tableCell textEditor_tableCellHeader"
            style="width: 75px; background-color: rgb(242, 243, 245); border: 1px solid black; vertical-align: top; text-align: start;"
          >
            <p class="textEditor_paragraph" dir="ltr">
              <span style="white-space: pre-wrap;">{name}</span>
            </p>
          </th>
          <td
            class="textEditor_tableCell"
            style="width: 75px; border: 1px solid black; vertical-align: top; text-align: start;"
          >
            <p class="textEditor_paragraph" style="text-align: start;">
              <span style="white-space: pre-wrap;">{price_desc}</span>
            </p>
          </td>
        </tr>
        """

    return f"""
    <div class="textEditor_layoutItem">
      <table class="textEditor_table">
        <colgroup>
          <col style="width: 218px;" />
          <col style="width: 172px;" />
        </colgroup>
        <tbody>
          <tr>
            <th
              colspan="2"
              class="textEditor_tableCell textEditor_tableCellHeader"
              style="width: 75px; background-color: rgb(242, 243, 245); border: 1px solid black; vertical-align: top; text-align: start;"
            >
              <p class="textEditor_paragraph" dir="ltr">
                <span style="white-space: pre-wrap;">{title}</span>
              </p>
            </th>
          </tr>
          {rows}
        </tbody>
      </table>
    </div>
    """
