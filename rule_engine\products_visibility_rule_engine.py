import pg_db
from pg_db import products_db
import datetime
import logging
import traceback
from plugin import bc_products
from utils import order_util , email_util
from pg_db_utils import pg_skuvault_util

logger = logging.getLogger()


def execute_rules(store, products):
    db_conn = pg_db.get_connection(store['id'])
    today_date = datetime.date.today()
    try:
        activate_product_rules = []
        deactivate_product_rules = []
        out_of_stock_products = {}
        out_of_stock_products_rules = {}
        filtered_out_of_stock_products_rules = {}
        
        deactivate_promotion_rules = {}   
        filtered_promotion_rules = {}             

        rules = products_db.ProductsVisibilityRules.fetch_rules(','.join(map(str, list(products.keys()))), db_conn)
        
        for rule in rules:                          
            product_id = rule[1] 
            disable_promotion = rule[3]
            threshold_quantity = rule[4]
            promotion_id = rule[5]
            disable_promotion_triggered = rule[6]
            hide_product = rule[7]                     
            hide_out_of_stock_days = rule[8]          
            is_hide_product_triggered = rule[13]
            promotion_disable_date = rule[15]
                      
            product = products.get(product_id, None)            
            if product:                
                if product['inventory_level'] > 0 and is_hide_product_triggered:
                    deactivate_product_rules.append(product['product_id'])
                elif hide_product and product['inventory_level'] == 0 and not is_hide_product_triggered:                                        
                    out_of_stock_products[product['product_id']] = hide_out_of_stock_days 
                    out_of_stock_products_rules[product_id] = rule

                #fetch promotion_rules                                  
                if disable_promotion:
                    if promotion_disable_date and not disable_promotion_triggered:                        
                        if today_date == promotion_disable_date:
                            deactivate_promotion_rules[product['product_id']] = promotion_id
                            filtered_promotion_rules[product['product_id']] = rule
                    elif product['inventory_level'] <= threshold_quantity and not disable_promotion_triggered:
                        deactivate_promotion_rules[product['product_id']] = promotion_id
                        filtered_promotion_rules[product['product_id']] = rule
        
        if len(out_of_stock_products) > 0:
            db_products = products_db.Products.get_out_of_stock_date(','.join(map(str, list(out_of_stock_products.keys()))), db_conn)
            for p in db_products:
                out_of_stock_date = p[1]
                if out_of_stock_date:
                    product_id = p[0]
                    out_of_stock_days = out_of_stock_products[product_id]
                    osd_days = (today_date - out_of_stock_date.date()).days
                    if osd_days > out_of_stock_days:
                        activate_product_rules.append(product_id)                        
                        filtered_out_of_stock_products_rules[product_id] = out_of_stock_products_rules[product_id]
            
        if len(deactivate_product_rules) > 0:
            products_db.ProductsVisibilityRules.update_in_stock_products(','.join(map(str, deactivate_product_rules)), db_conn)
        if len(activate_product_rules) > 0:
            actions_to_do_on_hide_products(store, filtered_out_of_stock_products_rules)
            products_db.ProductsVisibilityRules.update_out_of_stock_products(','.join(map(str, activate_product_rules)), db_conn, False)
               
        if len(deactivate_promotion_rules) > 0:
            actions_to_do_on_disable_promotion(store, deactivate_promotion_rules, filtered_promotion_rules)
            products_db.ProductsVisibilityRules.update_out_of_stock_products(','.join(map(str, list(deactivate_promotion_rules.keys()))), db_conn, True)

    except Exception as ex:
        logger.error(str(traceback.format_exc()))
    finally:
        db_conn.commit()
        db_conn.close()

def actions_to_do_on_hide_products(store, filtered_products):   
    for rule in filtered_products.values():           
        updated_name = rule[9] + ' ' +  rule[2]
        category_id = rule[10]           
        category_id = [int(num) for num in category_id.split(";")]        
        # delete images
        # delete_images(store, rule[0])

        #send mail
        send_out_of_stock_mail(store, rule[12])                
                
        # add name prefix and change category
        bc_products.update_product_visibility(store, rule[1], category_id)        
        
        bc_products.unhide_product(store, rule[1], False)        
        # set classifictions
        change_classifications(store, rule[11], rule[1], updated_name)                       
    
    

def actions_to_do_on_disable_promotion(store, promotions_data, filtered_promotion_rules):
    for promotion_id in promotions_data.values():
        bc_products.disable_promotion(store, promotion_id, "DISABLED")
    
    for rule in filtered_promotion_rules.values():                               
        # delete images
        delete_images(store, rule[1])                       

        bc_products.delete_product_cutomFields(store, rule[1])        
        

        
def delete_images(store, product_id):
    try:    
        productImages=bc_products.get_all_product_images(store,product_id)
        imageData=productImages.json()
        target_name = "[Promo]"         
        for image in imageData['data']:
            description = ''
            if 'description' in image:
                description = image["description"]
            if target_name.lower() in description.lower():
                bc_products.delete_the_image(store,product_id,image['id'])
    except Exception as ex:
        logger.error(str(traceback.format_exc()))

def send_out_of_stock_mail(store, to_email):
    try:    
        email_util.send_hide_product_email(str(store["id"]), to_email)
    except Exception as ex:
        logger.error(str(traceback.format_exc()))

def change_classifications(store, classification, product_id, updated_name):  
    db_conn = pg_db.get_connection(store['id'])
    try:  
        items = []
        variants = products_db.Variants.get_product_variants(product_id, db_conn)
        variants = variants.fetchall()         
        if len(variants) > 0:
            for variant in variants:
                s_vault = {}
                s_vault['Sku'] = variant[1]
                s_vault['Classification'] = classification
                s_vault['Description'] = updated_name
                items.append(s_vault)
        
            pg_skuvault_util.update_sku_vault_classifications(str(store["id"]), items) 
    except Exception as ex:
        logger.error(str(traceback.format_exc()))
    finally:
        db_conn.commit()
        db_conn.close()

