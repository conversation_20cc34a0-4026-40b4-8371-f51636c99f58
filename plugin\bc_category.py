from mongo_db import catalog_db
import mongo_db
import plugin
import logging
from pymongo import TEXT

logger = logging.getLogger()

def process_category(category):
    category['id'] = category['entityId']
    del category['entityId']
    if "children" in category:
        children = category['children']
        for index, childCategory in enumerate(children):
            children[index] = process_category(childCategory)
    return category

def process_category_tree(root):
    category_tree = root['data']['site']['categoryTree']
    categories = []
    for root_category in category_tree:
        root_category = process_category(root_category)
        root_category['_id'] = root_category['id']
        categories.append(root_category)
    
    return categories

def fetch_categories_rest_api(store):
    query_params = {
        "is_visible": "true",
        "include_fields": "name,parent_id,description,views,sort_order,page_title,meta_keywords,meta_description,image_url,url,custom_url,default_product_sort,is_visible",
        "limit": 0,
        "page": 250
    }
    api = "v3/catalog/categories"
    return plugin.fetch_all_by_rest_api(store, api, limit_per_req=250, query_params=query_params, 
            db_collection=catalog_db.CATEGORY_COLLECTION, db_process_threshold=250)

def fetch_all_categories(store):
    categories = fetch_categories_rest_api(store)
    
    db = mongo_db.get_store_db_client(store)
    if len(categories):
        res = db[catalog_db.CATEGORY_COLLECTION].create_index([('name', TEXT)], default_language='english')

    return categories