paginateBrands = """query paginateBrands(
    $pageSize: Int = __pageSize
    $cursor: String = __cursor
){
   site {
        brands (first: $pageSize, after: $cursor) {
            pageInfo {
                startCursor
                endCursor
            }
            edges {
                node {
                    entityId
                    name
                    path
                    defaultImage {
                        ...ImageFields
                    }
                }
            }
        }
    }
 }
 
 fragment ImageFields on Image {
   url320wide: url(width: 320)
   url640wide: url(width: 640)
   url960wide: url(width: 960)
   url1280wide: url(width: 1280)
 }"""

def get_paginated_query(page_size=1000, cursor=""):
    x = paginateBrands.replace("__pageSize", str(page_size))
    return x.replace("__cursor", '"' + cursor + '"')