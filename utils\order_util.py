import logging
from plugin import bc_order
from mongo_db import cart_db, customer_db
import task
from utils import redis_util, store_util, customer_util, bc_util
from pg_db_utils import pg_order_util
from pg_db import bulk_orders_db, notification_db, blocked_orders_db, salesforce_db
import pg_db as db
import traceback
from graphql import products_variant_query
from datetime import datetime, timezone, timedelta
from utils import admin_app_notification_util
from sqlalchemy import text
import mongo_db
import time
import project_manager
import os

logger = logging.getLogger()

def update_customer_cart(store, customer_id, order_id, cart_id):
    cart = cart_db.fetch_cart_by_bc_cart_id_and_customer_id(store, customer_id, cart_id)
    if cart:
        cart_db.delete_cart(store, cart['id'])
    pg_order_util.add_express_order(store['id'], order_id, customer_id, cart_id)
    bc_order.update_channel_id(store, order_id, 1)

def update_express_order_table(store_id, express_orders=[]):
    store = store_util.get_store_by_id(store_id)
    if store and express_orders and len(express_orders) > 0:
        for order in express_orders:
            update_customer_cart(store, order['customer_id'], order['id'], order['cart_id'])

def update_loyalti_points(store, order):
    customer_id = order['customer_id']
    order_id = order['id']
    if 'coupons' in order:
        if 'resource' in order['coupons']:
            url_value = order['coupons']['resource']
            url_value = 'v2' + url_value                
            coupons_res = bc_order.fetch_order_coupon(store, url_value)                
            if coupons_res.status_code == 200:
                coupons_array = coupons_res.json()                    
                for coupon in coupons_array:
                    if 'coupon_id' in coupon:
                        coupon_id_value = coupon['coupon_id']
                        c_data = {}
                        c_data['coupon_id'] = coupon_id_value
                        c_data['customer_id'] = customer_id                               

                        query = {"customer_id": int(customer_id), "coupon_id": int(coupon_id_value), "coupon_code": str(coupon['code'])}                                

                        old_history = customer_db.get_customer_loyalty_history(store, query) 
                        description = ''                        
                        if len(old_history):                                                               
                            if old_history[0]['description'].endswith("."):
                                description = old_history[0]['description'][:-1]
                                description += " for order #" + str(order_id) + "."                                    
                                update = { 
                                    "$set": {'description': description, 'order_id': int(coupon['order_id'])}
                                }  

                                customer_db.update_customer_loyalty_history(store, query, update)   
                                            
                        customer_db.update_customer_coupon_status(store, c_data)

def update_notification(store_id, order):
    # create notification
    notification_data = {}

    notification_data['customer_id'] = order['customer_id']
    notification_data['notification_type'] = 'order'
    notification_data['notification_type_id'] = order['id']
    notification_data['is_read'] = False
    notification_data['notification_date_created'] = order.get('date_created', None)

    session = db.get_session(store_id)
    
    try:
        with session.begin():
            model = build_notification_model(notification_data)
            session.add(model)
    
    except Exception as ex:
        logger.error(traceback.format_exc())
    
    finally:
        if session:
            session.commit()
            session.close()

def build_notification_model(notification_data):
    return notification_db.Notification(**notification_data)

def process_order_created_webhook(store_id, payload):
    store = store_util.get_store_by_id(store_id)
    if store:
        order_id = payload['data']['id']        
        express_channel_id = store_util.get_bc_api_creds(store)["channel_id"]
        res = bc_order.fetch_order(store, order_id)        
        # generate_admin_app_notification(store_id, AdminAppNotificationUtil.ORDER_CREATED, order_id)
        if res.status_code < 299:
            order = res.json()   
            cart_id = order['cart_id']         
            customer_id = order['customer_id']
            channel_id = order.get("channel_id", 1)    
            if cart_id and customer_id and channel_id == express_channel_id:
                update_customer_cart(store, customer_id, order_id, cart_id)
            update_loyalti_points(store, order)
            #update_notification(store_id, order)
            
            #create ticket for order in project mapped with orders module
            create_order_ticket(store_id, order)
        pg_order_util.fetch_and_update_orders(store, {"max_id":order_id, "min_id":order_id})
        admin_app_notification_util.generate_admin_app_notification(store_id, admin_app_notification_util.AdminAppNotificationUtil.ORDER_CREATED, order_id)
    else:
        logger.error(f"process_order_created_webhook: Invalid store id: {store_id}")
        raise Exception(f"process_order_created_webhook: Invalid store id: {store_id}")
                                                                        
                
def process_order_updated_webhook(store_id, payload):
    store = store_util.get_store_by_id(store_id)    
    if not store:
        logger.error(f"process_order_created_webhook: Invalid store id: {store_id}")
        raise Exception(f"process_order_created_webhook: Invalid store id: {store_id}")
    else:
        order_id = payload['data']['id']        
        res = bc_order.fetch_order(store, order_id)
        #generate_admin_app_notification(store_id, AdminAppNotificationUtil.ORDER_UPDATED, order_id)   
        if res.status_code < 299:            
            order = res.json()
            customer_id = order['customer_id']
            order_total = order['total_inc_tax'] if 'total_inc_tax' in order and order['total_inc_tax'] else 0
            if order['status'] == 'Completed':   
                loyalty_point_enable = customer_util.loyalty_point_visibility_check_for_customer(store_id, customer_id)               
                if loyalty_point_enable:
                    query = {"customer_id": int(customer_id), "order_id": int(order_id)}                       
                    history_data = customer_db.get_customer_loyalty_history(store, query)            
                    allowEntry = True
                    
                    if len(history_data):                    
                        for data in history_data:
                            if 'coupon_code' in data and data['coupon_code'] == '':                        
                                    allowEntry = False

                    if allowEntry:     
                        customer_util.update_customer_points(store, order['total_ex_tax'], customer_id, order_id)
            if float(order_total) > 0:
                update_bulk_order_data(store_id, order_id, order_total)
            
            update_ticket_status_using_order_id(order, '', store_id)
            time.sleep(10)
            task.submit_task('update_order_audit_report', (store_id, order_id, customer_id, float(order_total)))
            time.sleep(2)
        else:
            raise Exception(f"process_order_created_webhook: Failed to fetch order from bigcommerce. store id: {store_id}, order_id: {order_id}, status_code: {res.status_code}")
        pg_order_util.fetch_and_update_orders(store, {"max_id":order_id, "min_id":order_id})


def update_order_audit_report(store_id, order_id, customer_id, order_total):
    response = {'status': 200, 'message': 'No need to audit this order'}
    conn = db.get_connection(store_id)
    try:
        order_details_query = text("""
                SELECT order_status_id, order_status, customer_id, order_created_date_time, total_including_tax FROM orders WHERE order_id = :order_id
            """)
        order_details = conn.execute(order_details_query, {'order_id': order_id}).fetchone()
        logger.info(f"order_details_audit_report: {order_details[4]}")
        logger.info(f"order_total_audit_report: {order_total}")

        if order_total == order_details[4]:
            return response

        order_id = str(order_id)
        store = store_util.get_store_by_id(store_id)
        res = bc_util.get_order_products_for_audit_report(store, order_id, customer_id)
        accepted_prices, order_products = res

        # Quick checks
        if not order_products or not accepted_prices:
            response['status'] = 404
            response['message'] = 'No need to audit this order'

        # Flag to determine if any variant price is invalid
        invalid_price_found = False

        for product in order_products:
            variant_id = product['variant_id']
            base_price = float(product.get('base_price', 0))

            accepted = accepted_prices.get(variant_id, {}).get('accepted_prices', [])

            if not accepted:
                continue

            # Convert accepted prices to floats for safe comparison
            accepted = list(map(float, accepted))

            if base_price not in accepted:
                # Found an invalid price, no need to check further
                invalid_price_found = True
                break

        if invalid_price_found:
            # Insert or Update into order_audit_report table
            sql_check = text("""
                SELECT id, updated_at FROM order_audit_report WHERE order_id = :order_id
            """)
            result = conn.execute(sql_check, {'order_id': order_id}).fetchone()

            if result:
                # updated_at = result[1]
                # if updated_at.tzinfo is None:
                #     updated_at = updated_at.replace(tzinfo=timezone.utc)
                # now = datetime.now(timezone.utc)
                # time_diff = now - updated_at

                # if time_diff.total_seconds() <= 15:
                #     response['status'] = 200
                #     response['message'] = 'Order recently updated — skipping update'
                #     return response
                
                # Update existing record
                sql_update = text("""
                    UPDATE order_audit_report
                    SET order_status_id = :order_status_id,
                        order_status = :order_status,
                        updated_by = :updated_by,
                        updated_at = :updated_at,
                        order_total = :order_total
                    WHERE order_id = :order_id
                """)
                conn.execute(sql_update, {
                    'order_status_id': order_details[0],
                    'order_status': order_details[1],
                    'updated_by': "BigCommerce",
                    'updated_at': datetime.now(timezone.utc),
                    'order_id': order_id,
                    'order_total': order_details[4]
                })
                conn.commit()
                response['status'] = 200
                response['message'] = 'Order price audit updated successfully'
            else:
                # Insert new record
                sql_insert = text("""
                    INSERT INTO order_audit_report 
                    (order_date, order_id, customer_id, order_status_id, order_status, updated_by, updated_at, order_total)
                    VALUES 
                    (:order_date, :order_id, :customer_id, :order_status_id, :order_status, :updated_by, :updated_at, :order_total)
                """)
                conn.execute(sql_insert, {
                    'order_date': order_details[3],
                    'order_id': order_id,
                    'customer_id': order_details[2],
                    'order_status_id': order_details[0],
                    'order_status': order_details[1],
                    'updated_by': "BigCommerce",
                    'updated_at': datetime.now(timezone.utc),
                    'order_total': order_details[4]
                })
                conn.commit()
                response['status'] = 200
                response['message'] = 'Order price audit updated successfully'

        else:
            # All prices valid — if exists in order_audit_report, delete it
            sql_check = text("""
                SELECT id FROM order_audit_report WHERE order_id = :order_id
            """)
            result = conn.execute(sql_check, {'order_id': order_id}).fetchone()

            if result:
                sql_delete = text("""
                    DELETE FROM order_audit_report WHERE order_id = :order_id
                """)
                conn.execute(sql_delete, {'order_id': order_id})
                conn.commit()
                response['status'] = 200
                response['message'] = 'Order price audit deleted successfully'

    except Exception as e:
        logger.error(traceback.format_exc())
        raise e
    finally:
        conn.close()
    return response

def refresh_all_order_audits(store_id, batch_size=100):
    """
    Refresh all orders in order_audit_report table for a given store.
    Processes orders in batches to optimize database calls.
    
    Args:
        store_id: Store identifier
        batch_size: Number of orders to process in each batch (default: 100)
    
    Returns:
        dict: Summary of processed orders
    """
    response = {
        'status': 200,
        'message': 'Order audit refresh completed',
        'processed': 0,
        'updated': 0,
        'deleted': 0,
        'errors': 0
    }
    
    conn = db.get_connection(store_id)
    try:
        # Get store info once
        store = store_util.get_store_by_id(store_id)
        
        offset = 0
        while True:
            # Fetch batch of orders from order_audit_report with offset
            audit_query = text("""
                SELECT oar.order_id, oar.customer_id, oar.order_total
                FROM order_audit_report oar
                ORDER BY oar.order_id
                LIMIT :batch_size
                OFFSET :offset
            """)
            audit_orders = conn.execute(audit_query, {
                'batch_size': batch_size,
                'offset': offset
            }).fetchall()
            
            if not audit_orders:
                break
                
            # Extract order IDs for batch processing
            order_ids = [str(order[0]) for order in audit_orders]
            
            # Fetch all relevant order details in one query
            orders_query = text("""
                SELECT o.order_id, o.order_status_id, o.order_status, 
                       o.customer_id, o.order_created_date_time, o.total_including_tax
                FROM orders o
                WHERE o.order_id::text = ANY(:order_ids)
            """)
            orders_data = conn.execute(orders_query, {'order_ids': order_ids}).fetchall()
            
            # Create lookup dictionaries for quick access
            orders_map = {
                str(order[0]): {
                    'status_id': order[1],
                    'status': order[2],
                    'customer_id': order[3],
                    'created_date': order[4],
                    'total': order[5]
                }
                for order in orders_data
            }
            
            # Process each order in the batch
            to_update = []
            to_delete = []
            counter = 0
            batch_start_time = time.time()
            for audit_order in audit_orders:
                counter += 1
                logger.info(f"Processing order {counter} of {len(audit_orders)} (Batch offset: {offset})")
                order_id = str(audit_order[0])
                order_info = orders_map.get(order_id)
                
                if not order_info:
                    # Order no longer exists in orders table
                    to_delete.append(order_id)
                    continue
                
                order_start_time = time.time()
                # Get order products and check prices
                res = bc_util.get_order_products_for_audit_report(store, order_id, order_info['customer_id'])
                order_end_time = time.time()
                logger.info(f"Time taken for get_order_products_for_audit_report: {order_end_time - order_start_time} seconds")
                if not res:
                    to_delete.append(order_id)
                    continue
                    
                accepted_prices, order_products = res
                
                # Check if any product has invalid price
                invalid_price = any(
                    float(product.get('base_price', 0)) not in 
                    list(map(float, accepted_prices.get(product['variant_id'], {}).get('accepted_prices', [])))
                    for product in order_products
                    if accepted_prices.get(product['variant_id'], {}).get('accepted_prices')
                )
                
                if invalid_price:
                    to_update.append({
                        'order_id': order_id,
                        'order_date': order_info['created_date'],
                        'customer_id': order_info['customer_id'],
                        'order_status_id': order_info['status_id'],
                        'order_status': order_info['status'],
                        'order_total': order_info['total']
                    })
                else:
                    to_delete.append(order_id)
            
            # Batch update records
            if to_update:
                update_query = text("""
                    UPDATE order_audit_report
                    SET order_status_id = :order_status_id,
                        order_status = :order_status,
                        updated_by = :updated_by,
                        updated_at = :updated_at,
                        order_total = :order_total
                    WHERE order_id = :order_id
                """)
                
                for order_data in to_update:
                    conn.execute(update_query, {
                        **order_data,
                        'updated_by': "BigCommerce",
                        'updated_at': datetime.now(timezone.utc)
                    })
                response['updated'] += len(to_update)
            
            # Batch delete records
            if to_delete:
                delete_query = text("""
                    DELETE FROM order_audit_report 
                    WHERE order_id = ANY(:order_ids)
                """)
                conn.execute(delete_query, {'order_ids': to_delete})
                response['deleted'] += len(to_delete)
            
            # Commit the changes for this batch
            if to_update or to_delete:
                conn.commit()
            
            response['processed'] += len(audit_orders)
            batch_end_time = time.time()
            logger.info(f"Time taken for batch {offset//batch_size + 1}: {batch_end_time - batch_start_time} seconds")
            
            # Increment offset for next batch
            offset += batch_size
            
    except Exception as e:
        logger.error(f"Error in refresh_all_order_audits: {str(e)}")
        logger.error(traceback.format_exc())
        response['status'] = 500
        response['message'] = f'Error processing orders: {str(e)}'
        response['errors'] += 1
        conn.rollback()
    finally:
        conn.close()
    
    return response

def update_bulk_order_data(store_id, order_id, order_total):
    session = db.get_session(store_id)
    try:
        bulk_order = bulk_orders_db.POBulkOrders.get_bulk_order(store_id, order_id, session)
        if bulk_order:
            bc_order_id = bulk_order[2]
            if int(bc_order_id) == int(order_id):
                bulk_orders_db.POBulkOrders.update_bulk_order(store_id, order_total, order_id, session)
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        session.commit()
        session.close()

def process_blocked_order_lineitems(store_id):
    session = db.get_session(store_id)
    try:
        store = store_util.get_store_by_id(store_id)
        if store:
            api = store_util.get_bc_api_creds(store)
            token = redis_util.get_graphql_token(store['id'])
            orders = blocked_orders_db.BlockedOrdersSchema.get_all_blocked_orders(store['id'], session)
            if orders:
                for order in orders:
                    order_id = order[0]
                    order_products = bc_order.get_order_products(store, order_id)
                    order_products = order_products.json()
                    restock_thresold = order[7] if order[7] else 0
                    restock_qty = order[8] if order[8] else 0
                    if restock_thresold > 0 and restock_qty > 0:
                        order_line_items, discounts = pg_order_util.fetch_order_line_items(store, order_id, '')
                        if order_line_items:
                            product_list = []
                            variant_list = []
                            order_qty_mapping = {}
                            result = []
                            for product in order_line_items:  
                                o_qty = product.get("quantity", None)
                                if o_qty:
                                    v_id = product.get("variant_id", None)
                                    order_qty_mapping.update({
                                        v_id: o_qty
                                    })  
                                product_id = product.get("product_id", None) 
                                if len(product_list) < 50:
                                    if product_id and not int(product_id) in product_list:
                                        product_list.append(product_id) 

                                    variant_id = product.get("variant_id", None)
                                    if variant_id:
                                        variant_list.append(variant_id)  

                                if len(product_list) == 50 or len(variant_list) == 250:                                                                    
                                    query = products_variant_query.get_query(product_list, variant_list)
                                    status, res_body = bc_util.process_bc_graphql_request(api["store_url"], token['token'], query)                           
                                    if status == 200:       
                                        for item in res_body['data']['site']['products']['edges']:
                                            result.append(item['node'])                                        
                                    variant_list = []
                                    product_list = []
                            
                            if len(variant_list) > 0:
                                query = products_variant_query.get_query(product_list, variant_list)
                                status, res_body = bc_util.process_bc_graphql_request(api["store_url"], token['token'], query)
                                if status == 200:  
                                    for item in res_body['data']['site']['products']['edges']:
                                        result.append(item['node'])  
                            
                            restock_qty_list = {}
                            logs_array = []
                            if result:
                                for item in result:
                                    v_array = item.get("variants")
                                    if v_array:
                                        edges = v_array.get('edges', [])
                                        for edge in edges:
                                            v_item = edge.get('node', {})  # Get the 'node' dictionary from each edge
                                            v_id = v_item.get("entityId")
                                            available_qty = v_item['inventory']['aggregated'].get('availableToSell')

                                            blocked_qty = order_qty_mapping.get(v_id, None)                        
                                            if available_qty <= restock_thresold and blocked_qty is not None:
                                                r_qty = (blocked_qty - restock_qty) if blocked_qty > restock_qty else 0
                                                restock_qty_list.update({
                                                    v_id: r_qty
                                                })  
                                                log = {
                                                    "order_id": order_id,
                                                    "date_time": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ"),
                                                    "sku": v_item['sku'],
                                                    "triggered_by": "System",
                                                    "restock_qty": restock_qty if restock_qty < blocked_qty else blocked_qty,
                                                    "available_qty": available_qty
                                                }  
                                                logs_array.append(log)                                       
                            if restock_qty_list:
                                products = []
                                for product in order_line_items:
                                    v_id = product.get("variant_id", None)
                                    if v_id in restock_qty_list and product['quantity'] > 0:
                                        p_array = {
                                            "id": product.get("id", None),
                                            "product_id": product.get("product_id", None),
                                            "variant_id": product.get("variant_id", None),
                                            "quantity": restock_qty_list[v_id]
                                        }
                                        products.append(p_array)                                    
                                
                                if products:
                                    req_body = {
                                        "products": products
                                    }
                                    payload_for_min_max_rule = transform_order_products_data(order_products)
                                    affected_product_ids = check_min_max_rule_for_lineitems(store, payload_for_min_max_rule)
                                    time.sleep(7)
                                    url = "v2/orders/" + str(order_id)
                                    res = bc_util.call_api(api, "PUT", url,req_body=req_body) 
                                    if res.status_code < 299:            
                                        for log in logs_array:
                                            blocked_orders_db.add_logs(store['id'], log, session)
                                    if affected_product_ids:
                                        set_min_max_rule_for_lineitems_in_bc(store, affected_product_ids)
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        session.commit()
        session.close()

def update_ticket_status_using_order_id(order, updated_by, store_id):
    conn = db.get_connection(store_id)
    try:
        order_id = order['id']
        
        date_format = '%a, %d %b %Y %H:%M:%S %z'
        created_date = datetime.strptime(order['date_created'], date_format)
        due_date = None
        if order['status'] in ['Awaiting Payment', 'Awaiting Pickup']:
            due_date = created_date + timedelta(days=7)

        query = text(f"""SELECT id, project_id, title, pipeline_record_id, table_name, updated_by from agile_project_cards where pipeline_record_id = :id and table_name = 'orders';""")
        query = query.params(id=str(order_id))
        res = conn.execute(query).fetchone()
        if res:
            data = {
                'ticket_id': res[0],
                'title': res[2],
                'status': order['status'],
                'project_id': res[1],
                'resource_id': res[3],
                'table_name': res[4],
                'updated_by': res[5]
            }
            if order['status'] in ['Awaiting Payment', 'Awaiting Pickup']:
                data['due_date'] = due_date
            # update_ticket(store_id, data)
            task.submit_task('update_ticket', (store_id, data))
        else:
        #    create ticket for order in project mapped with orders module
           create_order_ticket(store_id, order)

    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        if conn:
            conn.close()

def create_order_ticket(store_id, order):
    conn = db.get_connection(store_id)
    try: 
        order_id = order['id']
        customer_id = order['customer_id']

        customer_rep = None
        if customer_id:
            customer_rep = salesforce_db.SalesforceCustomerRep.get_customer_rep_by_customer_id(store_id, customer_id)
        
        date_format = '%a, %d %b %Y %H:%M:%S %z'
        created_date = datetime.strptime(order['date_created'], date_format)
        due_date = None
        if order['status'] in ['Awaiting Payment', 'Awaiting Pickup']:
            due_date = created_date + timedelta(days=7)
            
        if order['status'] in ['Awaiting Payment', 'Awaiting Fulfillment', 'Awaiting Shipment', 'Manual Verification Required', 'Awaiting Pickup', 'Shipped', 'Completed', 'Cancelled']:

            query = text("""SELECT id, project_id, pipeline_db_tables_id, ticket_name, default_assignee, db_table_column from pipeline_project_table_mapping where pipeline_db_tables_id = (SELECT id from pipeline_db_tables where table_name = :table_name)""")
            query = query.params(table_name='orders')
            mapping_data = conn.execute(query).fetchall()
            if not mapping_data:
                logger.info(f"No pipeline project table mapping found for table_name: orders")
                return
            
            for row in mapping_data:
                status_column_id = get_order_status_for_mapping(conn, order['status'], row[1])
                mapping_details = {
                    "id": row[0],
                    "project_id": row[1],
                    "pipeline_db_tables_id": row[2],
                    "ticket_name": row[3],
                    "default_assignee": row[4],
                    "db_table_column": row[5]
                }

                data = {}
                data['table_name'] = 'orders'
                data['resource_id'] = str(order_id)
                data['title'] = str(order_id)
                data['description'] = order['customer_message']
                data['assigned_to'] = customer_rep[5] if customer_rep else ''
                data['start_date'] = created_date
                data['due_date'] = due_date
                data['mapping_details'] = mapping_details
                data['column_id'] = status_column_id

                try:
                    # task.submit_task('create_ticket', (store_id, data))
                    project_manager.create_ticket(store_id, data, is_from_order=True)  
                except Exception as ex:
                    logger.error(str(traceback.format_exc()))
    except Exception as ex:
        logger.error(str(traceback.format_exc()))
    finally:
        if conn:        
            conn.close()

def get_order_status_for_mapping(db_conn, order_status, project_id):
    status_column_id = None
    query = text (f"""SELECT pptm.id, pptm.pipeline_db_tables_id, pcm.project_column_id, pcm.project_id
                            FROM pipeline_project_table_mapping pptm
                            JOIN pipeline_column_mapping pcm ON pcm.project_table_mapping_id = pptm.id
                            WHERE pcm.table_column_value = :status AND pptm.project_id = :project_id""")

    query = query.params(status=order_status, project_id=project_id)
    status_column = db_conn.execute(query).fetchone()
    if status_column:
        status_column_id = status_column[2]
    
    return status_column_id

def clear_orders_ticket_for_project_mapping(store_id):
    conn = None
    try:
        conn = db.get_connection(store_id)

        # Fetch project mappings for "orders" table
        query = text("""
            SELECT id, project_id, pipeline_db_tables_id, ticket_name, default_assignee, db_table_column 
            FROM pipeline_project_table_mapping 
            WHERE pipeline_db_tables_id = (SELECT id FROM pipeline_db_tables WHERE table_name = :table_name)
        """)
        mapping_data = conn.execute(query, {"table_name": "orders"}).fetchall()

        if not mapping_data:
            logger.info("No pipeline project table mapping found for table_name: 'orders'")
            return {"status": 404, "message": "No pipeline project mappings found."}

        for mapping in mapping_data:
            project_id = mapping[1]
            # Fetch column ID for "Completed" column in project
            query = text("""
                SELECT project_column_id FROM pipeline_column_mapping 
                WHERE project_id = :project_id AND table_column_value = ANY(:column_names)
            """)
            column_id_rows = conn.execute(query, {"project_id": project_id, "column_names": ["Completed", "Cancelled", "Shipped"]}).fetchall()

            if not column_id_rows:
                logger.info(f"No column found for project_id: {project_id} and column_names: 'Completed', 'Cancelled', 'Shipped'")
                continue  # Skip to next mapping

            column_ids = [row[0] for row in column_id_rows]
            # Fetch all card IDs that meet deletion criteria
            card_query = text("""
                SELECT id FROM agile_project_cards 
                WHERE project_id = :project_id AND current_column_id = ANY(:column_ids)
                AND (
                    (description = '' AND id NOT IN (
                        SELECT id FROM agile_project_cards 
                        WHERE project_id = :project_id 
                        AND current_column_id = ANY(:column_ids) 
                        AND description = '' 
                        ORDER BY start_date DESC LIMIT 5000
                    ))
                    OR 
                    (description != '' AND id NOT IN (
                        SELECT id FROM agile_project_cards 
                        WHERE project_id = :project_id 
                        AND current_column_id = ANY(:column_ids) 
                        AND description != '' 
                        ORDER BY start_date DESC LIMIT 1000
                    ))
                )
            """)
            card_ids = conn.execute(card_query, {"project_id": project_id, "column_ids": column_ids}).fetchall()
            card_ids = [row[0] for row in card_ids]

            if not card_ids:
                logger.info(f"No matching cards to delete for project_id: {project_id}")
                continue  # Skip if no cards meet the criteria

            # Convert card_ids list to a tuple format for SQL
            card_ids_tuple = tuple(card_ids)
            # Delete related records in correct order
            try:
                # Step 1: Delete related records from agile_card_comments
                delete_comments_query = text("DELETE FROM agile_card_comments WHERE card_id IN :card_ids")
                conn.execute(delete_comments_query, {"card_ids": card_ids_tuple})

                # Step 2: Delete related records from agile_customfield_value
                delete_customfield_value_query = text("DELETE FROM agile_customfield_value WHERE card_id IN :card_ids")
                conn.execute(delete_customfield_value_query, {"card_ids": card_ids_tuple})

                # Step 3: Delete related records from agile_project_cards_logs
                delete_logs_query = text("DELETE FROM agile_project_cards_logs WHERE card_id IN :card_ids")
                conn.execute(delete_logs_query, {"card_ids": card_ids_tuple})

                # Step 4: Delete related records from agile_project_card_attachments
                get_attachments_query = text(f"""SELECT file_name, relative_path FROM agile_project_card_attachments WHERE project_id = :project_id AND card_id IN :card_ids;""")
                attachments = conn.execute(get_attachments_query.params(card_ids=card_ids_tuple, project_id=project_id)).fetchall()

                if attachments:
                    for attachment in attachments:
                        relative_path = attachment[1]                
                        file_path = os.path.join('/app/images/', str(relative_path))
                        if os.path.exists(file_path):
                            os.remove(file_path)


                delete_attachments_query = text(f"""DELETE FROM agile_project_card_attachments WHERE project_id = :project_id AND card_id IN :card_ids;""")
                conn.execute(delete_attachments_query.params(card_ids=card_ids_tuple, project_id=project_id))
                conn.commit()

                # Step 5: Finally, delete the project cards
                delete_cards_query = text("DELETE FROM agile_project_cards WHERE id IN :card_ids")
                conn.execute(delete_cards_query, {"card_ids": card_ids_tuple})

                conn.commit()  # Commit all deletions if successful
                logger.info(f"Deleted {len(card_ids)} cards and their related records for project_id: {project_id}")

            except Exception as delete_error:
                conn.rollback()  # Rollback changes if any deletion fails
                logger.error(f"Error while deleting related records for project_id {project_id}: {traceback.format_exc()}")
                continue  # Skip this project and continue with others

        return {"status": 200, "message": "Old project tickets and related records cleared successfully."}

    except Exception as ex:
        logger.error(f"Error in clear_orders_ticket_for_project_mapping: {traceback.format_exc()}")
        return {"status": 500, "message": "An error occurred while clearing project tickets."}

    finally:
        if conn:
            conn.close()  # Ensure connection is closed

def get_customer_price_list_and_group(store_id, customer_id):
    response = {'status': 400}
    conn = db.get_connection(store_id)
    try:
        query = text("""
            SELECT scr.price_list, c.customer_group_id, c.customer_group_name
            FROM salesforce_customer_rep scr
            LEFT JOIN customers c ON scr.customer_id = c.customer_id
            WHERE scr.customer_id = :customer_id
        """)
        result = conn.execute(query, {"customer_id": customer_id}).fetchone()
        
        if result:
            response['status'] = 200
            response['data'] = {
                'price_list': result[0],
                'customer_group_id': result[1],
                'customer_group_name': result[2]
            }
        else:
            response['status'] = 404
            response['data'] = {
                'price_list': None,
                'customer_group_id': None,
                'customer_group_name': None
            }
    except Exception as e:
        logger.error(traceback.format_exc())
        raise e
    finally:
        if conn:
            conn.close()
    return response

def get_accepted_prices(store_id, variant_ids, price_list_ids, customer_id):
    """
    Optimized version: Fetch product prices for multiple variant_ids based on prioritization:
    1. Check product_customer_price table in PostgreSQL using product_id.
    2. Check product_price_lists collection in MongoDB using variant_id and price_list_ids.
    """
    conn = db.get_connection(store_id)    
    try:
        result_dict = {}

        variant_ids = [int(vid) for vid in variant_ids]
        price_list_ids = [int(pid) for pid in price_list_ids]

        # Step 1: Fetch product_id for all variant_ids
        collection = "products"
        query = {"variants.id": {"$in": variant_ids}}
        projection = {"id": 1, "variants.id": 1}
        products = mongo_db.fetchall_documents_from_storefront_collection(store_id, collection, query, projection)

        variant_product_map = {}
        for product in products:
            product_id = product.get("id")
            for variant in product.get("variants", []):
                vid = variant.get("id")
                if vid in variant_ids:
                    variant_product_map[vid] = product_id

        # Step 2: Fetch all customer prices from PostgreSQL in one batch
        product_ids = list(set(variant_product_map.values()))
        customer_prices = {}

        if product_ids:
            try:
                sql = """
                    SELECT product_id, price
                    FROM product_customer_price
                    WHERE product_id = ANY(:product_ids) AND customer_id = :customer_id;
                """
                result = conn.execute(text(sql), {"product_ids": product_ids, "customer_id": customer_id}).fetchall()
                for row in result:
                    customer_prices[row[0]] = row[1]
            except Exception:
                pass  # continue silently if PostgreSQL error

        # Step 3: Fetch all needed price list docs in MongoDB at once
        price_list_query = {
            "variants.variant_id": {"$in": variant_ids},
            "variants.price_list.price_list_id": {"$in": price_list_ids}
        }
        price_list_projection = {
            "variants.variant_id": 1,
            "variants.price_list": 1
        }
        price_docs = mongo_db.fetchall_documents_from_admin_collection(
            store_id, "product_price_lists", price_list_query, price_list_projection
        )

        # Build a quick lookup for variant_id -> price info
        variant_price_map = {}

        for doc in price_docs:
            for variant in doc.get("variants", []):
                vid = variant.get("variant_id")
                if vid in variant_ids:
                    if vid not in variant_price_map:
                        variant_price_map[vid] = []
                    for price_info in variant.get("price_list", []):
                        if price_info.get("price_list_id") in price_list_ids:
                            price = price_info.get("price")
                            if price is not None:
                                variant_price_map[vid].append(price)

        # Step 4: Build the final result
        for variant_id in variant_ids:
            accepted_prices = []
            product_id = variant_product_map.get(variant_id)

            if not product_id:
                result_dict[variant_id] = {"accepted_prices": accepted_prices}
                continue

            # Priority 1: Add PostgreSQL customer price
            customer_price = customer_prices.get(product_id)
            if customer_price is not None:
                accepted_prices.append(customer_price)

            # Priority 2: Add MongoDB price list prices
            mongo_prices = variant_price_map.get(variant_id, [])
            accepted_prices.extend(mongo_prices)

            result_dict[variant_id] = {"accepted_prices": accepted_prices}

        return result_dict
    except Exception as e:
        logger.error(traceback.format_exc())
        return {}
    finally:
        conn.close()


def check_min_max_rule_for_lineitems(store, line_items):
    try:
        bc_api = store_util.get_bc_api_creds(store)
        token = redis_util.get_graphql_token(store['id'])
        affected_product_ids = []
        product_ids = [] 
        variant_ids = []
        products = []
        
        # Group line items by product_id and collect variant_ids
        product_variants = {}
        for item in line_items:
            product_id = item['product_id']
            variant_id = item['variant_id']
            if product_id not in product_variants:
                product_variants[product_id] = []
            if variant_id:
                product_variants[product_id].append(variant_id)

        # Process products in batches
        for product_id, v_ids in product_variants.items():
            if len(product_ids) < 50:
                product_ids.append(product_id)
                
                for variant_id in v_ids:
                    if variant_id:
                        variant_ids.append(variant_id)

            if len(product_ids) == 50 or len(variant_ids) == 250:
                graphql_query = products_variant_query.get_query(product_ids, variant_ids)
                gql_status, gql_res = bc_util.process_bc_graphql_request(bc_api["store_url"], token['token'], graphql_query)
                if gql_status == 200:
                    for item in gql_res['data']['site']['products']['edges']:
                        products.append(item['node'])    
                
                product_ids = []
                variant_ids = []
        
        if len(product_ids) > 0 and len(variant_ids) > 0:
            graphql_query = products_variant_query.get_query(product_ids, variant_ids)
            gql_status, gql_res = bc_util.process_bc_graphql_request(bc_api["store_url"], token['token'], graphql_query)
            if gql_status == 200:
                for item in gql_res['data']['site']['products']['edges']:
                    products.append(item['node'])   
        
        # process the product information if products found
        product_payload = []
        if products:
            for product in products:
                min_purchase_quantity = product['minPurchaseQuantity']
                max_purchase_quantity = product['maxPurchaseQuantity']
                if (max_purchase_quantity and int(max_purchase_quantity) > 0) or (min_purchase_quantity and int(min_purchase_quantity) > 0):
                    exists_product = mongo_db.fetch_one_document_from_admin_collection(store['id'], 'order_edit_logs', {'product_id': product['entityId']})
                    if exists_product:
                        mongo_db.delete_documents_from_admin_collection(store['id'], 'order_edit_logs', {'product_id': product['entityId']})
                    
                    product_logs = {
                        'product_id': product['entityId'],
                        'min_purchase_quantity': int(min_purchase_quantity) if min_purchase_quantity else 0,    
                        'max_purchase_quantity': int(max_purchase_quantity) if max_purchase_quantity else 0,
                        'sku': product['sku'],
                        'available_quantity': product['inventory']['aggregated']['availableToSell'],
                        'status': product['availabilityV2']['status'],
                        'variants': product['variants']['edges']
                    }
                    mongo_db.insert_document_in_admin_collection(store['id'], 'order_edit_logs', product_logs)
                    product_payload.append({"id": int(product['entityId']), "order_quantity_minimum": 0, "order_quantity_maximum": 0})
                    affected_product_ids.append(product['entityId'])
        
        # reset the product information in bc if products found
        if len(product_payload) > 0:
            # If the length of product_payload is greater than 10, break it into sets of 10 products
            if len(product_payload) > 10:
                # Split the product_payload into chunks of 10
                for i in range(0, len(product_payload), 10):
                    product_chunk = product_payload[i:i+10]
                    
                    req_data = {
                        "query_params": {},
                        "method": "PUT",
                        "url": "v3/catalog/products",
                        "body": product_chunk
                    }
                    res = bc_util.process_api(bc_api, req_data)
            else:
                # If the length of product_payload is 10 or less, process it as is
                req_data = {
                    "query_params": {},
                    "method": "PUT",
                    "url": "v3/catalog/products",
                    "body": product_payload
                }
                res = bc_util.process_api(bc_api, req_data)

        return affected_product_ids
    except Exception as e:
        logger.exception("Exception in check_min_max_rule_for_lineitems")

def set_min_max_rule_for_lineitems_in_bc(store, product_ids):
    try:
        # db = new_mongodb.get_admin_db_client(store)
        bc_api = store_util.get_bc_api_creds(store)
        req_data = {
                "query_params": {},
                "method": "PUT",
                "url": "v3/catalog/products",
                "body": []
            }
        payload = []
        for product_id in product_ids:
            exists_product = mongo_db.fetch_one_document_from_admin_collection(store['id'], 'order_edit_logs', {'product_id': product_id})
            if exists_product:
                min_purchase_quantity = exists_product['min_purchase_quantity']
                max_purchase_quantity = exists_product['max_purchase_quantity']
                # complex_rules = exists_product['complex_rules']
                if (max_purchase_quantity and int(max_purchase_quantity) > 0) or (min_purchase_quantity and int(min_purchase_quantity) > 0):
                   #update bulk products in bc
                    data = {
                        "id": int(product_id),
                        "order_quantity_minimum": int(min_purchase_quantity) if min_purchase_quantity else 0,
                        "order_quantity_maximum": int(max_purchase_quantity) if max_purchase_quantity else 0,
                    }
                    payload.append(data)
                    if len(payload) == 10:
                        req_data['body'] = payload
                        res = bc_util.process_api(bc_api, req_data)
                        if res and res['status_code'] == 200:
                            payload = []
                            req_data['body'] = []
                
                # if len(complex_rules) > 0:
                #     for rule in complex_rules:
                #         rule_id = rule.get("id")
                #         url =  f"v3/catalog/products/{product_id}/complex-rules/{rule_id}"
                #         req_body = {
                #             "prdocut_id": int(product_id),  
                #             "enabled": True
                #         }
                #         res = bc.call_api(bc_api, "PUT", url, {}, req_body)
        
        if len(payload) > 0:
            req_data['body'] = payload
            res = bc_util.process_api(bc_api, req_data)
        mongo_db.delete_documents_from_admin_collection(store['id'], 'order_edit_logs', {'product_id': {'$in': product_ids}})

        return True
    except Exception as e:
        logger.exception("Exception in check_min_max_rule_for_lineitems")
        return False


def transform_order_products_data(order_products):
    """
    Transform order products data into a simplified payload format.
    
    Args:
        order_products (list): List of order product objects from BigCommerce
        
    Returns:
        list: List of transformed product objects with required fields
        Example: [{'product_id': 26684, 'variant_id': 215107, 'quantity': 15, 'price_inc_tax': 47, 'price_ex_tax': 47}]
    """
    transformed_products = []
    
    for product in order_products:
        if product.get('quantity', 0) > 0:  # Only include products with quantity > 0
            transformed_product = {
                'product_id': product.get('product_id'),
                'variant_id': product.get('variant_id'),
                'quantity': product.get('quantity', 0),
                'price_inc_tax': float(product.get('price_inc_tax', 0)),
                'price_ex_tax': float(product.get('price_ex_tax', 0))
            }
            transformed_products.append(transformed_product)
    
    return transformed_products
