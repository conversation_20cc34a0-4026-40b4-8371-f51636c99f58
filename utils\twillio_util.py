from twilio.rest import Client
import logging
import traceback

logger = logging.getLogger()

def _get_client(api_creds):
    return Client(api_creds['account_sid'], api_creds['auth_token'])

def send_twillio_notification_sms(client, sender, receivers, content):
    sms_sid = None
    for contact in receivers:
        try:
            pass
            # message = client.messages.create(
            #     body= content,
            #     from_= sender,
            #     to=contact
            # )
            # sms_sid = message.sid
        except Exception as e:
            logger.error(str(traceback.format_exc()))

    return sms_sid

def send_twillio_notification_wp(client, sender, receivers, content):
    wp_sid = None
    for contact in receivers:
        try:
            toNumber = 'whatsapp:' + contact
            fromNumber = 'whatsapp:' + sender
            message = client.messages.create(
                body=content,
                from_=fromNumber,
                to=toNumber
            )
            wp_sid = message.sid
        except Exception as e:
            logger.error(str(traceback.format_exc()))
    
    return wp_sid

def send_twillio_notification_call(client, sender, receivers, content):
    call_sid = None
    for contact in receivers:
        try:
            call_message = '<Response><Say>' + content + '</Say></Response>'
            # call = client.calls.create(
            #     twiml = call_message,
            #     from_ = sender,
            #     to = contact
            # )
            # call_sid = call.sid
        except Exception as e:
            logger.error(str(traceback.format_exc()))
    
    return call_sid

def send_twillio_notification(twillio_api_creds, receivers, content):
    # if twillio_api_creds.get("active", True):
        # client = _get_client(twillio_api_creds)
        #sms_sid = send_twillio_notification_sms(client, twillio_api_creds["sms_from"], receivers, content)
        # wp_sid = send_twillio_notification_wp(client, twillio_api_creds["whatsapp_from"], receivers, content)
        #call_sid = send_twillio_notification_call(client, twillio_api_creds["call_from"], receivers, content)
    sms_sid = None
    wp_sid = None
    call_sid = None
    return sms_sid, wp_sid, call_sid
    
