from datetime import datetime
from sqlalchemy import Column,  DateTime,String, Integer,Float, text
from sqlalchemy.dialects.postgresql import insert
import pg_db as db
import logging

logger = logging.getLogger()

class BlockedOrdersSchema(db.Base):
    __tablename__ = db.blocked_orders

    id = Column(Integer, primary_key=True)
    order_id = Column(Integer)
    order_created_date_time = Column(DateTime)
    customer_name = Column(String)
    customer_representative = Column(String)
    blocked_by = Column(String)
    status = Column(String)
    total = Column(Float)
    restock_threshold = Column(Integer)
    restock_qty = Column(Integer)
    

    @classmethod
    def add_blocked_order(cls, store,  data, session=None):
        stmt = insert(BlockedOrdersSchema).on_conflict_do_nothing()
        
        db.execute_stmt(store, stmt, data, session)
        return data
    
    @classmethod
    def get_all_blocked_orders(cls, store_id, session=None):
        local_session = None
        if not session:
            session = db.get_session(store_id)
            local_session = session
        try:
            # SQL query to get data from the orders and customer tables
            query = text(f"""
                SELECT
                    bo.order_id,
                    bo.order_created_date_time,
                    bo.customer_name,
                    bo.customer_representative,
                    bo.blocked_by,
                    bo.status,
                    bo.total,
                    bo.restock_threshold,
                    bo.restock_qty
                FROM
                    blocked_orders AS bo
                ORDER BY
                    bo.order_created_date_time DESC
            """)

            # Execute the query
            result = session.execute(query)
            return result.fetchall()
        finally:
            if local_session:
                local_session.close()

class BlockedOrdersLogsSchema(db.Base):
    __tablename__ = db.blocked_orders_logs

    id = Column(Integer, primary_key=True)
    order_id = Column(Integer)
    date_time = Column(DateTime)
    sku = Column(String)
    triggered_by = Column(String)
    restock_qty = Column(Integer)
    available_qty = Column(Integer)


    @classmethod
    def add_blocked_order_logs(cls, store,  data, session=None):
        stmt = insert(BlockedOrdersLogsSchema).on_conflict_do_nothing()
        db.execute_stmt(store, stmt, data, session)
        
        return data
    
    @classmethod
    def get_logs(cls, store, order_id, session=None):
        response = {'status': 400}
        conn = db.get_connection(store['id'])

        try:
            # SQL query to get data from the orders and customer tables
            query = text(f"""
                SELECT
                    ol.order_id,
                    ol.date_time,
                    ol.sku,
                    ol.triggered_by,
                    ol.restock_qty,
                    ol.available_qty
                FROM
                    blocked_order_logs AS ol
                WHERE
                    ol.order_id = {order_id}
                ORDER BY
                    ol.date_time DESC
            """)

            # Execute the query
            result = conn.execute(query)
            res = result.fetchall()  # Fetch all records
            
            # If result exists, format the data into a dictionary
            if res:
                response['data'] = [{'order_id': row[0], 'date_time': row[1].isoformat(), 'sku': row[2], 'triggered_by': row[3], 'restock_qty': row[4], 'available_qty': row[5]} for row in res]
                response['status'] = 200
            else:
                response['status'] = 404
                response['message'] = 'No data found.'
        finally:
            if conn:
                conn.close()  # Make sure to close the connection

        return response
    
def get_order_details_by_order_id(store, order_id, current_user):
    response = {'status': 400}
    conn = db.get_connection(store['id'])

    try:
        # SQL query to get data from the orders and customer tables
        query = text(f"""
            SELECT 
                o.order_id,
                o.order_created_date_time,
                CONCAT(c.first_name, ' ', c.last_name) AS customer_name,  -- Combining first and last name
                o.order_status AS status,
                o.total_including_tax AS total
            FROM 
                orders AS o
            JOIN 
                customers AS c ON o.customer_id = c.customer_id
            WHERE 
                o.order_id = {order_id}
        """)

        # Execute the query
        result = conn.execute(query)
        res = result.fetchone()  # Fetch only one record since we expect one order

        # If result exists, format the data into a dictionary
        if res:
            row_data = {
                'order_id': res[0],
                'order_created_date_time': res[1].strftime("%Y-%m-%d %H:%M:%S") if (res[1] and isinstance(res[1], datetime)) else str(res[1]),
                'customer_name': res[2],
                'blocked_by': current_user,
                'status': res[3],
                'total': res[4],
                'restock_threshold': None,
                'restock_qty': None
            }

            response['data'] = row_data
            response['status'] = 200
        else:
            response['message'] = "Order not found"
            response['status'] = 404

    except Exception as e:
        response['status'] = 422
        response['message'] = str(e)

    finally:
        if conn:
            conn.close()  # Make sure to close the connection

    return response

def add_logs(store_id, data, session=None):
    local_session = None
    if not session:
        session = db.get_session(store_id)
        local_session = session

    try:
        stmt = insert(BlockedOrdersLogsSchema).values(data)
        res = session.execute(stmt)
        session.commit()  # Commit after executing the insert query
    except Exception as e:
        logger.error("Error while adding logs: " + str(e))
    finally:
        if local_session:
            local_session.close()

def clean_up_deleted_blocked_order(store, order_id):
    response = {'status': 400}
    conn = db.get_connection(store['id'])

    try:
        # Step 1: Delete from `blocked_orders`
        delete_blocked_orders = text("DELETE FROM blocked_orders WHERE order_id = :order_id")
        res_blocked_orders = conn.execute(delete_blocked_orders, {'order_id': order_id})
        conn.commit()  # Commit after executing the delete query
        print(f"Deleted from blocked_orders: {res_blocked_orders.rowcount} rows")

        if res_blocked_orders.rowcount > 0:
            response['data'] = {'deleted_count': res_blocked_orders.rowcount}
            response['status'] = 200

            # Step 2: Delete from `blocked_order_logs`
            delete_logs = text("DELETE FROM blocked_order_logs WHERE order_id = :order_id")
            res_logs = conn.execute(delete_logs, {'order_id': order_id})
            conn.commit()  # Commit after executing the delete query
            print(f"Deleted from blocked_orders_logs: {res_logs.rowcount} rows")

        else:
            response['message'] = "Failed to delete from blocked_orders"

    except Exception as e:
        conn.rollback()  # Rollback if there's an error
        response['status'] = 422
        response['message'] = str(e)

    finally:
        if conn:
            conn.close()

    return response

def get_all_logs(store, order_id):
    return BlockedOrdersLogsSchema.get_logs(store, order_id)