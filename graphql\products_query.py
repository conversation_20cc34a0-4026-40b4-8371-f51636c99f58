paginateProducts = """query paginateProducts(
    $pageSize: Int = __pageSize
    $cursor: String = __cursor
){
   site {
        products (first: $pageSize, after: $cursor) {
            pageInfo {
                startCursor
                endCursor
            }
            edges {
                node {
                    entityId
                    sku
                    name
                    description
                    minPurchaseQuantity
                    minPurchaseQuantity
                    type
                    weight {
                        value
                        unit
                    }
                    height {
                        value
                        unit
                    }
                    width {
                        value
                        unit
                    }
                    depth {
                        value
                        unit
                    }
                    inventory {
                        aggregated {
                            availableToSell
                            warningLevel
                        }
                        isInStock
                        hasVariantInventory
                    }
                    defaultImage {
                        ...ImageFields
                    }
                    categories {
                        edges {
                            node {
                                entityId
                                name
                            }
                        }
                    }
                    variants {
                        edges {
                            node {
                                entityId
                                sku
                                isPurchasable
                                inventory {
                                    aggregated {
                                        availableToSell
                                        warningLevel
                                    }
                                    isInStock
                                }
                                options {
                                    edges {
                                        node {
                                            entityId
                                            displayName
                                            isRequired
                                            values {
                                                edges {
                                                    node {
                                                        entityId
                                                        label
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                prices {
                                    price {
                                        ...MoneyFields
                                    }
                                    priceRange {
                                        min {
                                            ...MoneyFields
                                        }
                                        max {
                                            ...MoneyFields
                                        }
                                    }
                                    salePrice {
                                        ...MoneyFields
                                    }
                                    retailPrice {
                                        ...MoneyFields
                                    }
                                    saved {
                                        ...MoneyFields
                                    }
                                    bulkPricing {
                                        minimumQuantity
                                        maximumQuantity
                                        ... on BulkPricingFixedPriceDiscount {
                                            price
                                        }
                                        ... on BulkPricingPercentageDiscount {
                                            percentOff
                                        }
                                        ... on BulkPricingRelativePriceDiscount {
                                            priceAdjustment
                                        }
                                    }
                                }
                            }
                        }
                    }
                    images {
                        edges {
                            node {
                                ...ImageFields
                            }
                        }
                    }
                    reviewSummary {
                        summationOfRatings
                        numberOfReviews
                    }
                    prices {
                        price {
                            ...MoneyFields
                        }
                        priceRange {
                            min {
                                ...MoneyFields
                            }
                            max {
                                ...MoneyFields
                            }
                        }
                        salePrice {
                            ...MoneyFields
                        }
                        retailPrice {
                            ...MoneyFields
                        }
                        saved {
                            ...MoneyFields
                        }
                        bulkPricing {
                            minimumQuantity
                            maximumQuantity
                            ... on BulkPricingFixedPriceDiscount {
                                price
                            }
                            ... on BulkPricingPercentageDiscount {
                                percentOff
                            }
                            ... on BulkPricingRelativePriceDiscount {
                                priceAdjustment
                            }
                        }
                    }
                    brand {
                        entityId
                        name
                    }
                }
            }
        }
    }
 }
 
 fragment ImageFields on Image {
   url320wide: url(width: 320)
   url640wide: url(width: 640)
   url960wide: url(width: 960)
   url1280wide: url(width: 1280)
 }
 
 fragment MoneyFields on Money {
   value
   currencyCode
 }"""

def get_paginated_query(page_size=250, cursor=""):
    x = paginateProducts.replace("__pageSize", str(page_size))
    return x.replace("__cursor", '"' + cursor + '"')