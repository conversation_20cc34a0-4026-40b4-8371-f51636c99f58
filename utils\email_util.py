import smtplib
from email.message import EmailMessage
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import logging
import traceback
from utils import store_util, customer_util, auth_util, redis_util
from email.mime.base import MIMEBase
from email import encoders
from datetime import datetime
from jinja2 import Template
import base64
import traceback
import logging
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from google.oauth2 import service_account
from googleapiclient.discovery import build
from bson import ObjectId
import mongo_db
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from mongo_db import store_db


logger = logging.getLogger()

# Define constants
SCOPES = ['https://www.googleapis.com/auth/gmail.send']

new_account_expiry_duration = 60*60
forgot_password_expiry_duration = 60*60

def send_new_account_email(store_id, email):
    if store_id and email:
        store = store_util.get_store_by_id(store_id)        
        if store:
            mail_template = store_util.get_email_template(store_id, store_util.Template.customer_signup.value)
            if mail_template:
                token, exp = auth_util.generate_token(store_id, email, new_account_expiry_duration)
                redis_util.add_new_account_token(store_id, email, token, exp)

                subject = mail_template['subject']
                from_email = mail_template['email']
                password = mail_template['password']
                url = store['store_url'] + \
                    mail_template['url'] + "?token=" + token
                msg_body = mail_template['body'].format(url=url)
                to_email = [email]
                send_html_email(store_id, subject, msg_body, from_email, to_email, bcc_emails=[])


def send_reset_password_email(store_id, customer_id):
    if store_id and customer_id:
        store = store_util.get_store_by_id(store_id)
        if store:
            customer = customer_util.get_customer_by_id(store, customer_id)
            if customer:
                name = ""
                email = ""
                if "first_name" in customer:
                    name = customer['first_name']
                elif "name" in customer:
                    name = customer['name']

                if "email" in customer:
                    email = customer['email']

                mail_template = store_util.get_email_template(store_id, store_util.Template.reset_password.value)
                token, exp = auth_util.generate_token(store_id, email, forgot_password_expiry_duration)                
                redis_util.add_forgot_password_token(store_id, email, token, exp)

                subject = mail_template['subject']
                from_email = mail_template['email']
                password = mail_template['password']
                url = store['store_url'] + \
                    mail_template['url'] + "?token=" + token
                msg_body = mail_template['body'].format(
                    first_name=name, url=url)
                to_email = [email]
                send_html_email(store_id, subject, msg_body, from_email, to_email, bcc_emails=[])


def send_text_email(smtp_server, smtp_port, username, password, subject, msg_body, from_email, to_email):
    try:
        msg = EmailMessage()
        msg.set_content(msg_body)
        msg['Subject'] = subject
        msg['From'] = from_email
        msg['To'] = ", ".join(to_email)
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(username, password)
        server.send_message(msg)
        server.close()
        return True
    except Exception as ex:
        return_message = "Exception: " + str(traceback.format_exc())
        logger.error(return_message)
        return False, return_message


# def send_html_email(smtp_server, smtp_port, username, password, subject, msg_body, from_email, to_email, file=None, bcc_emails=[]):
#     try:
#         msg = MIMEMultipart('alternative')
#         msg['Subject'] = subject
#         msg['From'] = from_email
#         msg['To'] = ", ".join(to_email)
#         msg.attach(MIMEText(msg_body, 'html'))
#         if file:
#             msg.attach(file)  
#         all_recipients = to_email + (bcc_emails or [])
#         all_recipients = list(set(all_recipients))

#         server = smtplib.SMTP(smtp_server, smtp_port)
#         server.starttls()
#         server.login(username, password)
#         server.sendmail(from_email, all_recipients, msg.as_string())
#         server.close()
#         return True
#     except Exception as ex:
#         return_message = "Exception: " + str(traceback.format_exc())
#         logger.error(return_message)
#         return False, return_message



def authenticate(creds_data):
    """Authenticate using OAuth2 credentials from the database instead of a JSON file."""
    try:
        creds = Credentials.from_authorized_user_info(creds_data, SCOPES)

        # Refresh the token if expired
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())

        service = build('gmail', 'v1', credentials=creds)
        return service
    except Exception as e:
        logger.error(f"Authentication failed: {e}")
        return None


def send_html_email(store_id, subject, msg_body, from_email, to_email, bcc_emails=[], file=None):
    """Send an HTML email using OAuth2 credentials from the database."""
    try:
        # Get credentials from MongoDB
        db = mongo_db.get_admin_db_client_for_store_id(store_id)
        store_document = db["store"].find_one({"_id": ObjectId(store_id)})

        creds_data = store_document.get("apps", {}).get("smpt", {}).get("gmail_oauth", {})

        if not creds_data:
            return False, "OAuth2 credentials not found in the database."

        # Authenticate with the credentials from the database
        service = authenticate(creds_data)
        if service is None:
            return False, "Failed to authenticate Gmail service."

        # Create the email message
        msg = MIMEMultipart()
        msg["Subject"] = subject
        msg["From"] = from_email
        if not to_email:
            logger.error("No 'to_email' addresses provided. Skipping email.")
            return False, "Recipient address required"

        msg["To"] = ", ".join(to_email)

        msg["Bcc"] = ", ".join(bcc_emails)

        # Attach HTML content
        msg.attach(MIMEText(msg_body, "html"))

        # Attach file if provided
        if file:
            msg.attach(file)

        # Encode the message
        encoded_message = base64.urlsafe_b64encode(msg.as_bytes()).decode("utf-8")
        create_message = {"raw": encoded_message}

        # Send the email
        send_message = service.users().messages().send(userId="me", body=create_message).execute()
        
        return True, f"Email sent successfully! Message Id: {send_message['id']}"
    
    except Exception as ex:
        error_message = "Exception: " + str(traceback.format_exc())
        logger.error(error_message)
        return False, error_message

    
def send_hide_product_email(store_id, email):
    if store_id and email:
        store = store_util.get_store_by_id(store_id)        
        if store:
            mail_template = store_util.get_email_template(store_id, store_util.Template.hide_product_alert.value)                        
            subject = mail_template['subject']
            from_email = mail_template['email']
            password = mail_template['password']
            msg_body = mail_template['body']
            to_email = email.split(';')
            send_text_email("smtp.gmail.com", 587, from_email,
                            password, subject, msg_body, from_email, to_email)
            

def send_text_email_notification(store_id, email_template):
    if email_template:
        email_subject = email_template.get("subject", None)
        email_body = email_template.get("body", None)
        if email_subject and email_body:
            smpt_cred = store_util.get_smpt_info(store_id)
            to_emails = email_template.get("to_emails", None)
            smpt_host = email_template.get("smtp_host", None)
            smtp_port = email_template.get("smtp_port", None)
            from_email = email_template.get("email", None)
            password = email_template.get("password", None)

            if not to_emails or len(to_emails) == 0:
                to_emails = smpt_cred["to_emails"]
            if not smpt_host:
                smpt_host = smpt_cred['smtp_host']
            if not smtp_port:
                smtp_port = smpt_cred['smtp_port']
            if not from_email:
                from_email = smpt_cred['email']
            if not password:
                password = smpt_cred['password']

            send_text_email(smpt_host, smtp_port, from_email, password, email_subject, 
                                       email_body, from_email, to_emails)

def send_job_failed_notification(store_id, task_name, error):
    try:
        mail_template = store_util.get_email_template(store_id, store_util.Template.job_failed_alert.value)
        mail_template["email"]["body"] = mail_template["email"]["body"].format(task_name=task_name, task_error=error)
        send_text_email_notification(store_id, mail_template["email"])
        return True
    except Exception as ex:
        message = "Exception: " + str(traceback.format_exc())
        logger.error(message)
        raise Exception(message)
    return False

def send_replenishment_csv_data_email(store_id, csv_content, recipient_email, file_name, user_name, created_date_time):
    if store_id:
        mail_template = store_util.get_email_template(store_id, store_util.Template.replenishment_csv_data.value)
        subject = mail_template['subject']
        from_email = mail_template['email']
        password = mail_template['password']
        
        part = MIMEBase('application', 'octet-stream')
        part.set_payload(csv_content.getvalue())
        encoders.encode_base64(part) 

        part.add_header('Content-Disposition', f'attachment; filename="{file_name}"')
        msg_body = mail_template['body'].format(name=user_name, created_date=created_date_time)
        to_email = [recipient_email]
        send_html_email(store_id, subject, msg_body, from_email, to_email, bcc_emails=[], file=part)

def daily_sales_replenishment_csv_mail(store_id, csv_content, recipient_email, file_name, user_name, created_date_time, call_from_time_trigger=False):
    if store_id:
        mail_template = store_util.get_email_template(store_id, store_util.Template.replenishment_daily_sold_product_csv.value)
        subject = mail_template['subject']
        from_email = mail_template['email']
        password = mail_template['password']
        
        part = MIMEBase('application', 'octet-stream')
        part.set_payload(csv_content.getvalue())
        encoders.encode_base64(part) 

        part.add_header('Content-Disposition', f'attachment; filename="{file_name}"')
        msg_body = mail_template['body'].format(name=user_name, created_date=created_date_time)
        to_email = [recipient_email]
        if call_from_time_trigger:
            bcc_emails = ['<EMAIL>', '<EMAIL>']
        else:
            bcc_emails = []
        send_html_email(store_id, subject, msg_body, from_email, to_email, bcc_emails=bcc_emails, file=part)

def no_sold_products_analytics_csv_mail(store_id, csv_content, recipient_email, file_name, user_name, created_date_time):
    if store_id:
        mail_template = store_util.get_email_template(store_id, store_util.Template.no_sold_product_csv.value)
        subject = mail_template['subject']
        from_email = mail_template['email']
        password = mail_template['password']
        
        part = MIMEBase('application', 'octet-stream')
        part.set_payload(csv_content.getvalue())
        encoders.encode_base64(part) 

        part.add_header('Content-Disposition', f'attachment; filename="{file_name}"')
        msg_body = mail_template['body'].format(name=user_name, created_date=created_date_time)
        to_email = [recipient_email]
        send_html_email(store_id, subject, msg_body, from_email, to_email, bcc_emails=[], file=part)

def send_discontinued_products_daily_sold_csv_mail(store_id, csv_content, recipient_email, file_name, user_name, created_date_time):
    if store_id:
        mail_template = store_util.get_email_template(store_id, store_util.Template.daily_sold_discontinued_products_csv.value)
        subject = mail_template['subject']
        from_email = mail_template['email']
        password = mail_template['password']
        
        part = MIMEBase('application', 'octet-stream')
        part.set_payload(csv_content.getvalue())
        encoders.encode_base64(part) 

        part.add_header('Content-Disposition', f'attachment; filename="{file_name}"')
        msg_body = mail_template['body'].format(name=user_name, created_date=created_date_time)
        to_email = [recipient_email]
        send_html_email(store_id, subject, msg_body, from_email, to_email, bcc_emails=[], file=part)
        

def send_replenishment_dashboard_csv_data_email(store_id, csv_content, recipient_email, file_name, user_name, created_date_time):
    if store_id:
        mail_template = store_util.get_email_template(store_id, store_util.Template.replenishment_dashboard_csv_data.value)
        subject = mail_template['subject']
        from_email = mail_template['email']
        password = mail_template['password']

        # Attach the CSV file
        part = MIMEBase('application', 'octet-stream')
        part.set_payload(csv_content.getvalue())
        encoders.encode_base64(part) 

        part.add_header('Content-Disposition', f'attachment; filename="{file_name}"')
        msg_body = mail_template['body'].format(name=user_name, created_date=created_date_time)
        to_email = [recipient_email]
        send_html_email(store_id, subject, msg_body, from_email, to_email, bcc_emails=[], file=part)


def send_low_stock_email(store_id, low_stock_records):
    if store_id and low_stock_records:
        store = store_util.get_store_by_id(store_id)
        if store:
            mail_template = store_util.get_email_template(store_id, store_util.Template.safety_stock_alert.value)
            subject = mail_template['subject']
            from_email = mail_template['email']
            password = mail_template['password']
            html_template = mail_template['body']  # Get the body as an HTML template

            bcc_emails = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']

            # Group records by product_title
            grouped_by_product = {}
            for record in low_stock_records:
                product_title = str(record['product_title']).strip()
                user_email = str(record['user_email']).strip() if record['user_email'] else "<EMAIL>"

                if user_email not in grouped_by_product:
                    grouped_by_product[user_email] = {}
                
                if product_title not in grouped_by_product[user_email]:
                    grouped_by_product[user_email][product_title] = []
                grouped_by_product[user_email][product_title].append(record)
            
            # Construct product sections for each low stock item
            for user_email, products in grouped_by_product.items():
                user_name = ''
                product_sections = ''
                for product_title, records in products.items():
                    user_name = records[0]['user_name'] if records[0]['user_name'] else 'Imran'
                    table_rows = "".join(f"""
                    <tr>
                        <td style="text-align: left;">{record['child_sku']}<br/><strong style="font-size: 12px; display: block;">{record['variant_options']}</strong></td>
                        <td style="text-align: center;">{record['available_stock']}</td>
                        <td style="text-align: center;">{record['safety_stock']}</td>
                        <td style="text-align: center;">{record['incoming_stock']}</td>
                    </tr>
                    """ for record in records)

                    product_sections += f"""
                    <tr>
                        <td class="product-item-table-wrapper">
                            <table cellspacing="0" cellpadding="0" class="product-item-table">
                                <tr>
                                    <td>
                                        <strong>{product_title}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <table class="product-item-sku-table">
                                            <thead>
                                                <th>SKU</th>
                                                <th>Available Stock</th>
                                                <th>Safety Stock</th>
                                                <th>Incoming</th>
                                            </thead>
                                            <tbody>
                                                {table_rows}
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    """

                current_date = datetime.now().strftime("%B %d, %Y")
                template = Template(html_template)
                personalized_msg_body = template.render(
                    user_name=user_name,
                    current_date=current_date,
                    product_sections=product_sections
                )

                send_html_email(store_id, subject, personalized_msg_body, from_email, [user_email], bcc_emails=bcc_emails)

def send_product_customer_tracking_report_email(store_id, csv_content, recipient_email, file_name, user_name, created_date_time):
    if store_id:
        mail_template = store_util.get_email_template(store_id, store_util.Template.customer_tracking_csv.value)
        subject = mail_template['subject']
        from_email = mail_template['email']
        password = mail_template['password']

        # Attach the CSV file
        part = MIMEBase('application', 'octet-stream')
        part.set_payload(csv_content.getvalue())
        encoders.encode_base64(part) 

        part.add_header('Content-Disposition', f'attachment; filename="{file_name}"')
        msg_body = mail_template['body'].format(name=user_name, created_date=created_date_time)
        to_email = [recipient_email]
        send_html_email(store_id, subject, msg_body, from_email, to_email, bcc_emails=[], file=part)

def send_bulk_products_gloabl_report_email(store_id, csv_content, recipient_email, file_name, user_name, created_date_time):
    if store_id:
        mail_template = store_util.get_email_template(store_id, store_util.Template.bulk_product_global_report_csv.value)
        subject = mail_template['subject']
        from_email = mail_template['email']
        password = mail_template['password']

        # Attach the CSV file
        part = MIMEBase('application', 'octet-stream')
        part.set_payload(csv_content.getvalue())
        encoders.encode_base64(part) 

        part.add_header('Content-Disposition', f'attachment; filename="{file_name}"')
        msg_body = mail_template['body'].format(name=user_name, created_date=created_date_time)
        to_email = [recipient_email]
        send_html_email(store_id, subject, msg_body, from_email, to_email, bcc_emails=[], file=part)
        

def send_liquidated_products_csv_data_email(store_id, csv_content, recipient_email, file_name, user_name, created_date_time):
    if store_id:
        mail_template = store_util.get_email_template(store_id, store_util.Template.liquidated_products_csv.value)
        subject = mail_template['subject']
        from_email = mail_template['email']
        password = mail_template['password']

        # Attach the CSV file
        part = MIMEBase('application', 'octet-stream')
        part.set_payload(csv_content.getvalue())
        encoders.encode_base64(part) 

        part.add_header('Content-Disposition', f'attachment; filename="{file_name}"')
        msg_body = mail_template['body'].format(name=user_name, created_date=created_date_time)
        to_email = [recipient_email]
        send_html_email(store_id, subject, msg_body, from_email, to_email, bcc_emails=[], file=part)
        
def send_order_placed_email_to_compliance(store_id, order, billing_address, shipping_address, line_items):
    if store_id and order:
        store = store_util.get_store_by_id(store_id)
        if store:
            mail_template = store_util.get_email_template(store_id, store_util.Template.order_notification_to_compliance.value)
            subject = mail_template['subject'] + " #" + str(order['id'])
            from_email = mail_template['email']
            password = mail_template['password']
            html_template = mail_template['body']  # Get the body as an HTML template
            
            product_sections = ''
            shipping_address_section = ''
            billing_address_section = ''
            shipping_address_1 = ''
            # Construct product sections for each low stock item
            for line_item in line_items:
                item_options = "".join(f"""
                    <p><span>{options['display_name']}:</span> {options['display_value']}</p>
                """ for options in line_item['product_options'])

                product_sections += f"""
                    <tr class="products__item">
                        <th class="products__image-container">
                            <img src="{line_item['image']}" class="products__image" alt="product name">
                        </th>
                        <th class="products__content" style="padding: 15px;">
                            <p>
                                <strong>{line_item['name']}</strong>
                            </p>

                            <p class="products__sku">{line_item['sku']}</p>

                            <p class="products__options">
                                {item_options}
                            </p>

                            <p class="products__price">${round(float(line_item['price_inc_tax']), 2)}</p>

                        </th>

                        <th class="products__quantity">
                            <p>
                                Qty:
                                <span>{line_item['quantity']}</span>
                            </p>
                        </th>

                        <th class="products__total">
                            <p>
                                <strong>${round(float(line_item['total_inc_tax']), 2)}</strong>
                            </p>
                        </th>
                    </tr>
                """

            if billing_address:
                billing_address_section = build_address_section(billing_address)
            
            if shipping_address:
                shipping_address_section = build_address_section(shipping_address[0])
               
                # Filter out empty values and join with a comma
                shipping_address_1 = ", ".join(filter(None, [
                    shipping_address[0]['street_1'],
                    shipping_address[0]['street_2'],
                    shipping_address[0]['city'],
                    shipping_address[0]['state'],
                    shipping_address[0]['zip'],
                    shipping_address[0]['country']
                ]))
               
            template = Template(html_template)
            personalized_msg_body = template.render(
                order_id = order['id'],
                product_sections = product_sections,
                sub_total = round(float(order['subtotal_ex_tax']), 2),
                shipping_cost = round(float(order['shipping_cost_inc_tax']), 2),
                tax_amount = round(float(order['total_tax']), 2),
                grand_total = round(float(order['total_inc_tax']), 2),
                payment_method = order['payment_method'],
                shipping_method = shipping_address[0]['shipping_method'],
                billing_address_section = billing_address_section,
                shipping_address_section = shipping_address_section,
                order_comment = order['customer_message'],
                shipping_address_1 = shipping_address_1
            )


            send_html_email(store_id, subject, personalized_msg_body, from_email, ['<EMAIL>', '<EMAIL>'], bcc_emails=[])

def build_address_section(address):
    form_fields = ""
    if len(address['form_fields']) > 0:
        item_options = "".join(f"""
                <dt><strong>{fields['name']}</strong></dt>
                <dd>{fields['value']}</dd>
        """ for fields in address['form_fields'])
        
        form_fields = "".join(f"""<dl>{item_options}</dl>""")

    address_section = f"""
    <div class="addresses__content">
        <strong class="addresses__name">
            {address['first_name']} {address['last_name']}
        </strong>
        <br />
        {address['company']}
        <br />
        {address['street_1']} {address['street_2']}
        <br />
        {address['city']}
        <br />
        {address['state']}
        <br />
        {address['zip']}
        <br />
        {address['country']}
        <br />
        {address['phone']}

        {form_fields}
        """
    return address_section

def send_price_list_import_email(store_id, errors, recipient_email, user_name, filename, created_date_time):
    if store_id:
        mail_template = store_util.get_email_template(store_id, store_util.Template.price_list_import.value)

        subject = mail_template['subject']
        from_email = mail_template['email']
        password = mail_template['password']

        user_details = store_db.fetch_user_by_username(store_id, user_name)
        name = user_details['name'] if 'name' in user_details and user_details else ''

        # Determine status based on errors
        status = "successfully completed" if not errors else "failed"
        extra_message = "Please try uploading it again." if errors else ""

        # Format the error section properly
        if errors:
            errors_list = errors.split("\n")  # Convert errors string into a list
            errors_section = "<p class='error-list'><strong>Errors:</strong></p><ul>"
            errors_section += "".join(f"<li>{error}</li>" for error in errors_list)
            errors_section += "</ul>"
        else:
            errors_section = ""
        
        # Ensure all required keys are included in the format
        msg_body = mail_template['body'].format(
            name=name,
            filename=filename,
            created_date_time=created_date_time,
            status=status,
            extra_message=extra_message,
            errors_section=errors_section  # Ensure this key is passed
        )

        to_email = [recipient_email]

        send_html_email(store_id, subject, msg_body, from_email, to_email, bcc_emails=[])

def send_price_list_csv_data_email(store_id, file_content, recipient_email, filename, user_name, created_date):
    if store_id:
        mail_template = store_util.get_email_template(store_id, store_util.Template.price_list_export_csv.value)
        subject = mail_template['subject']
        from_email = mail_template['email']
        password = mail_template['password']

        # Attach the CSV file
        part = MIMEBase('application', 'octet-stream')
        part.set_payload(file_content.getvalue())
        encoders.encode_base64(part) 

        user_details = store_db.fetch_user_by_username(store_id, user_name)
        name = user_details['name'] if 'name' in user_details and user_details else ''

        part.add_header('Content-Disposition', f'attachment; filename="{filename}"')
        msg_body = mail_template['body'].format(name=name, created_date=created_date)
        to_email = [recipient_email]
        send_html_email(store_id, subject, msg_body, from_email, to_email, bcc_emails=[], file=part)

def send_customer_profitability_report_email(store_id, csv_content, recipient_email, file_name, user_name, created_date_time):
    if store_id:
        mail_template = store_util.get_email_template(store_id, store_util.Template.customer_profitability_csv.value)
        subject = mail_template['subject']
        from_email = mail_template['email']
        password = mail_template['password']

        # Attach the CSV file
        part = MIMEBase('application', 'octet-stream')
        part.set_payload(csv_content.getvalue())
        encoders.encode_base64(part) 

        part.add_header('Content-Disposition', f'attachment; filename="{file_name}"')
        msg_body = mail_template['body'].format(name=user_name, created_date=created_date_time)
        to_email = [recipient_email]
        send_html_email(store_id, subject, msg_body, from_email, to_email, bcc_emails=[], file=part)

def send_product_wise_profitability_report_email(store_id, csv_content, recipient_email, file_name, user_name, created_date_time):
    if store_id:
        mail_template = store_util.get_email_template(store_id, store_util.Template.product_wise_profitability_csv.value)
        subject = mail_template['subject']
        from_email = mail_template['email']
        password = mail_template['password']

        # Attach the CSV file
        part = MIMEBase('application', 'octet-stream')
        part.set_payload(csv_content.getvalue())
        encoders.encode_base64(part) 

        part.add_header('Content-Disposition', f'attachment; filename="{file_name}"')
        msg_body = mail_template['body'].format(name=user_name, created_date=created_date_time)
        to_email = [recipient_email]
        send_html_email(store_id, subject, msg_body, from_email, to_email, bcc_emails=[], file=part)

def send_suppliers_profitability_report_email(store_id, csv_content, recipient_email, file_name, user_name, created_date_time):
    if store_id:
        mail_template = store_util.get_email_template(store_id, store_util.Template.suppliers_profitability_csv.value)
        subject = mail_template['subject']
        from_email = mail_template['email']
        password = mail_template['password']

        # Attach the CSV file
        part = MIMEBase('application', 'octet-stream')
        part.set_payload(csv_content.getvalue())
        encoders.encode_base64(part) 

        part.add_header('Content-Disposition', f'attachment; filename="{file_name}"')
        msg_body = mail_template['body'].format(name=user_name, created_date=created_date_time)
        to_email = [recipient_email]
        send_html_email(store_id, subject, msg_body, from_email, to_email, bcc_emails=[], file=part)

def send_classifications_profitability_report_email(store_id, csv_content, recipient_email, file_name, user_name, created_date_time):
    if store_id:
        mail_template = store_util.get_email_template(store_id, store_util.Template.classifications_profitability_csv.value)
        subject = mail_template['subject']
        from_email = mail_template['email']
        password = mail_template['password']

        # Attach the CSV file
        part = MIMEBase('application', 'octet-stream')
        part.set_payload(csv_content.getvalue())
        encoders.encode_base64(part) 

        part.add_header('Content-Disposition', f'attachment; filename="{file_name}"')
        msg_body = mail_template['body'].format(name=user_name, created_date=created_date_time)
        to_email = [recipient_email]
        send_html_email(store_id, subject, msg_body, from_email, to_email, bcc_emails=[], file=part)

def send_orders_profitability_report_email(store_id, csv_content, recipient_email, file_name, user_name, created_date_time):
    if store_id:
        mail_template = store_util.get_email_template(store_id, store_util.Template.orders_profitability_csv.value)
        subject = mail_template['subject']
        from_email = mail_template['email']
        password = mail_template['password']

        # Attach the CSV file
        part = MIMEBase('application', 'octet-stream')
        part.set_payload(csv_content.getvalue())
        encoders.encode_base64(part) 

        part.add_header('Content-Disposition', f'attachment; filename="{file_name}"')
        msg_body = mail_template['body'].format(name=user_name, created_date=created_date_time)
        to_email = [recipient_email]
        send_html_email(store_id, subject, msg_body, from_email, to_email, bcc_emails=[], file=part)

def send_brands_profitability_report_email(store_id, csv_content, recipient_email, file_name, user_name, created_date_time):
    if store_id:
        mail_template = store_util.get_email_template(store_id, store_util.Template.brands_profitability_csv.value)
        subject = mail_template['subject']
        from_email = mail_template['email']
        password = mail_template['password']

        # Attach the CSV file
        part = MIMEBase('application', 'octet-stream')
        part.set_payload(csv_content.getvalue())
        encoders.encode_base64(part) 

        part.add_header('Content-Disposition', f'attachment; filename="{file_name}"')
        msg_body = mail_template['body'].format(name=user_name, created_date=created_date_time)
        to_email = [recipient_email]
        send_html_email(store_id, subject, msg_body, from_email, to_email, bcc_emails=[], file=part)


def send_project_notification_email(store_id, content, recipient_emails, username_map, event_type): 
    if not store_id or not event_type:
        logger.warning("Missing store_id or event_type for email notification.")
        return

    try:
        # Fetch template based on event_type
        template_enum = None
        ticket_operation = ""
        if event_type == "ticket_created" or event_type == "ticket_assigned":
            template_enum = store_util.Template.ticket_assigned_or_created_notification.value
            ticket_operation="New ticket created" if event_type == "ticket_created" else "Ticket Assigned"
        elif event_type == "comment_added":
            template_enum = store_util.Template.ticket_comment_added_notification.value
            ticket_operation="Ticket Comment Added"
        else:
            logger.warning(f"No template configured for event_type: {event_type}")
            return

        mail_template = store_util.get_email_template(store_id, template_enum)
        subject = mail_template['subject']
        from_email = mail_template['email']
        password = mail_template['password']
        html_template = mail_template['body']

        # Replace placeholders in subject dynamically
        if "{ticket_operation}" in subject:
            subject = subject.replace("{project_name}", content["project_name"])
            subject = subject.replace("{ticket_operation}", ticket_operation)
        
        recipient_emails = [email for email in recipient_emails if email.strip()]
        if not recipient_emails:
            logger.warning("Recipient email list is empty after sanitizing.")
            return

        for email in recipient_emails:
            msg_body = generate_project_notification_email_body(event_type, email, username_map, html_template, content, ticket_operation)
            if msg_body:
                send_html_email(store_id, subject, msg_body, from_email, [email], bcc_emails=[])

    except Exception as e:
        logger.error(f"Failed to send project notification email: {e}")
        logger.error(traceback.format_exc())


def generate_project_notification_email_body(event_type, email, username_map, html_template, content, ticket_operation):
    user_data = username_map.get(email)
    user_name = user_data.get("name") if user_data else email

    if event_type == "ticket_created" or event_type == "ticket_assigned":
        return html_template.format(
            ticket_operation=ticket_operation,
            user_name=user_name,
            project_name=content["project_name"],
            project_url=content["project_url"],
            ticket_name=content["title"],
            ticket_url=content["ticket_url"],
            card_identifier=content["card_identifier"],
            assignee_name=content["assignee_name"]
        )
    
    elif event_type == "comment_added":
        return html_template.format(
            ticket_operation=ticket_operation,
            user_name=user_name,
            project_name=content["project_name"],
            project_url=content["project_url"],
            ticket_name=content["title"],
            ticket_url=content["ticket_url"],
            card_identifier=content["card_identifier"],
            assignee_name=content["assignee_name"],
            comment=content["comment"]
        )

    logger.warning(f"No email template formatting logic for event_type: {event_type}")
    return None

def send_bulk_order_allocation_notification(store_id, sales_rep_email, sales_rep_name, customer_data, product_data, call_from_reminder=False):
    """
    Send bulk order allocation notification to sales rep
    
    Args:
        store_id (str): Store ID
        sales_rep_email (str): Sales rep's email address
        sales_rep_name (str): Sales rep's name
        customer_data (dict): Customer data including name, email, company name, and variants
        product_data (dict): Product data including name, SKU, etc.
    """
    if not store_id or not sales_rep_email:
        logger.warning("Missing store_id or sales_rep_email for bulk order notification")
        return False, "Missing required parameters"
    
    try:
        # Get email template
        if call_from_reminder:
            mail_template = store_util.get_email_template(store_id, store_util.Template.bulk_order_reminder_notification.value)
        else:
            mail_template = store_util.get_email_template(store_id, store_util.Template.bulk_order_allocation_notification.value)
        if not mail_template:
            logger.error("No email template found for bulk order allocation notification")
            return False, "Email template not found"

        subject = mail_template['subject']
        from_email = mail_template['email']
        html_template = mail_template['body']

        if '{product_name}' in subject:
            subject = subject.replace('{product_name}', product_data.get('bc_name', 'N/A'))

        # Generate customer sections
        customer_sections = []
        for customer in customer_data:
            customer_name = customer.get('customer_name', 'N/A')
            customer_email = customer.get('customer_email', 'N/A')
            company_name = customer.get('company_name', 'N/A')
            
            # Generate PO number links
            po_links = []
            for po in customer.get('po_numbers', []):
                po_link = f'<a href="{po["link"]}" style="padding: 2px 0; margin-right: 10px; color: #466aec;">{po["number"]}</a>'
                po_links.append(po_link)
            
            # Generate variant rows
            variant_rows = []
            for variant in customer.get('variants', []):
                variant_sku = variant.get('variant_sku', 'N/A')
                variant_name = variant.get('option', 'N/A')
                requested_qty = variant.get('requested_qty', 0)
                allocated_qty = variant.get('locked_qty', 0)
                
                variant_row = f"""
                <tr>
                    <td style="text-align: left;">{variant_sku}<br /><strong
                            style="font-size: 12px; display: block;">{variant_name}</strong></td>
                    <td style="text-align: center;">{requested_qty}</td>
                    <td style="text-align: center;">{allocated_qty}</td>
                </tr>
                """
                variant_rows.append(variant_row)

            # Generate customer section
            customer_section = f"""
            <tr>
                <td class="product-item-table-wrapper">
                    <table cellspacing="0" cellpadding="0" class="product-item-table">
                        <tr>
                            <td>
                                <table cellspacing="0" cellpadding="0" class="customer-info-table">
                                    <tr>
                                        <td width="150px;" style="padding: 5px 0;">
                                            <strong style="font-size: 14px; font-weight: 600; line-height: 1.5;">Customer Name:</strong>
                                        </td>
                                        <td style="padding: 5px 0;">
                                            <strong style="font-size: 14px; font-weight: 400; line-height: 1.5;">{customer_name}</strong>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td width="150px" style="padding: 2px 0;">
                                            <strong style="font-size: 14px; font-weight: 600; line-height: 1.5;">Customer Email:</strong>
                                        </td>
                                        <td style="padding: 2px 0;">
                                            <strong style="font-size: 14px; font-weight: 400; line-height: 1.5;">{customer_email}</strong>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td width="150px" style="padding: 2px 0;">
                                            <strong style="font-size: 14px; font-weight: 600; line-height: 1.5;">Company Name:</strong>
                                        </td>
                                        <td style="padding: 2px 0;">
                                            <strong style="font-size: 14px; font-weight: 400; line-height: 1.5;">{company_name}</strong>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td width="150px" style="padding: 2px 0;">
                                            <strong style="font-size: 14px; font-weight: 600; line-height: 1.5;">PO Numbers:</strong>
                                        </td>
                                        <td style="padding: 5px 0;">
                                            <strong style="font-size: 14px; font-weight: 400; line-height: 1.5;">
                                                {''.join(po_links) if po_links else 'N/A'}
                                            </strong>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <table cellspacing="0" cellpadding="0" class="product-item-title-table">
                                    <tr>
                                        <td>
                                            <strong style="font-size: 16px; font-weight: 600; line-height: 1.5;">{product_data.get('bc_name', 'N/A')}</strong>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <table cellspacing="0" cellpadding="0" class="product-item-sku-table">
                                    <thead>
                                        <th style="text-align: left;">SKU</th>
                                        <th style="width: 150px; text-align: center;">Requested Qty.</th>
                                        <th style="width: 150px; text-align: center;">Allocated Qty.</th>
                                    </thead>
                                    <tbody>
                                        {''.join(variant_rows)}
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            """
            customer_sections.append(customer_section)

        # Replace placeholders in template
        msg_body = html_template.format(
            Salesrep_name=sales_rep_name,
            customer_sections=''.join(customer_sections)
        )

        # Send email
        return send_html_email(store_id, subject, msg_body, from_email, [sales_rep_email], bcc_emails=["<EMAIL>", "<EMAIL>"])

    except Exception as ex:
        error_message = "Exception: " + str(traceback.format_exc())
        logger.error(error_message)
        return False, error_message

def send_bulk_order_updated_notification(store_id, email_data):
    if not store_id or not email_data:
        logger.warning("Missing required parameters for bulk order updated notification")
        return False, "Missing required parameters"
    
    try:
        # Get email template from MongoDB
        mail_template = store_util.get_email_template(store_id, store_util.Template.bulk_order_updated_notification.value)
        if not mail_template:
            logger.error("No email template found for bulk order updated notification")
            return False, "Email template not found"

        subject = mail_template['subject']
        from_email = mail_template['email']
        html_template = mail_template['body']

        # Replace order ID in subject if placeholder exists
        if '{order_id}' in subject:
            subject = subject.replace('{order_id}', str(email_data['order_id']))

        # Generate product table rows
        product_rows = []
        for product in email_data['order_product_details']:
            product_row = f"""
            <tr>
                <td style="text-align: left;">{product['product_name']}</td>
                <td style="text-align: left;">{product['sku']}</td>
                <td style="text-align: center;">{product['quantity']}</td>
                <td style="text-align: center;">${product['unit_price']:.2f}</td>
                <td style="text-align: center;">${product['total_price']:.2f}</td>
            </tr>
            """
            product_rows.append(product_row)

        # Replace placeholders in template
        msg_body = html_template.format(
            order_id=email_data['order_id'],
            customer_name=email_data['customer_name'],
            customer_email=email_data['customer_email'],
            company_name=email_data['company_name'],
            sales_rep_name=email_data['sales_rep_name'],
            sales_rep_email=email_data['sales_rep_email'],
            po_id=email_data['po_id'],
            order_date=email_data['order_date'],
            order_update_date=email_data['order_update_date'],
            product_rows=''.join(product_rows),
            total_order_value=f"${email_data['total_order_value']:.2f}"
        )

        # Send email to default recipients
        to_emails = ["<EMAIL>", "<EMAIL>"]
        
        # Send email
        return send_html_email(store_id, subject, msg_body, from_email, to_emails, bcc_emails=[])

    except Exception as ex:
        error_message = "Exception: " + str(traceback.format_exc())
        logger.error(error_message)
        return False, error_message
