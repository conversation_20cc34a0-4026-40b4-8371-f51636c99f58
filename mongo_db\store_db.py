import logging
import mongo_db
from bson import ObjectId

logger = logging.getLogger()

def fetch_active_stores():
    return mongo_db.get_active_stores()

def fetch_store_by_storehash(store_hash):
    return mongo_db.get_store_by_store_hash(store_hash)

def fetch_store_by_id(store_id):
    return mongo_db.get_store_by_id(str(store_id))

def fetch_email_template(store_id, template_id):
    template = None
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    template = db[mongo_db.EMAIL_TEMPLATE_COLLECTION].find_one({"_id": template_id})
    return template

def fetch_app_api_data(store_id, app_name):
    app_data = None
    store = fetch_store_by_id(store_id)
    if store:
        app_data = store.get("apps", {}).get(app_name, None)
    return app_data

def fetch_user_by_username(store_id, username):
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    result = db[mongo_db.USER_COLLECTION].find_one({"username": username})
    return result 

def fetch_users_by_usernames(store_id, usernames):
    if not usernames:
        return {}

    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    cursor = db[mongo_db.USER_COLLECTION].find({"username": {"$in": list(usernames)}}, {"username": 1, "name": 1, "_id": 0})

    return {user["username"]: user for user in cursor}

def fetch_user_by_id(store_id, user_id):
    db = mongo_db.get_admin_db_client_for_store_id(store_id)
    result = db[mongo_db.USER_COLLECTION].find_one({"_id": ObjectId(user_id)})
    return result 