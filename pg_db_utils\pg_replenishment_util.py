from mongo_db import store_db, get_store_db_client_for_store_id
import pg_db as db
from sqlalchemy import text
import pandas as pd
import logging
import traceback
import datetime
from utils import common_util, email_util, store_util, price_list_util
import utils
import os
import csv
import io
import pytz
from datetime import timezone
from mongo_db import get_admin_db_client_for_store_id
from plugin import bc_price_list
from pg_db.analytics_db import AnalyticsDB
import zipfile
from dateutil import parser
import pytz

logger = logging.getLogger()

def create_views(conn):
    query = f"""
DROP VIEW IF EXISTS analytics.sell_summary_products;
DROP VIEW IF EXISTS analytics.product_sell_7days; 
create view analytics.product_sell_7days as 
	SELECT MAX(product_id) AS product_id, parent_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_products_trend_table()} 
	WHERE order_date_time >= current_date - interval '7' day 
	group by parent_sku;
	
DROP VIEW IF EXISTS analytics.product_sell_15days;
  create view analytics.product_sell_15days as 
	SELECT MAX(product_id) AS product_id, parent_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_products_trend_table()} 
	WHERE order_date_time >= current_date - interval '15' day 
	group by parent_sku;
	
DROP VIEW IF EXISTS analytics.product_sell_30days;
  create view analytics.product_sell_30days as 
	SELECT MAX(product_id) AS product_id, parent_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_products_trend_table()} 
	WHERE order_date_time >= current_date - interval '30' day 
	group by parent_sku;
	
DROP VIEW IF EXISTS analytics.product_sell_45days;
  create view analytics.product_sell_45days as 
	SELECT MAX(product_id) AS product_id, parent_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_products_trend_table()} 
	WHERE order_date_time >= current_date - interval '45' day 
	group by parent_sku;

DROP VIEW IF EXISTS analytics.product_sell_60days;
  create view analytics.product_sell_60days as 
	SELECT MAX(product_id) AS product_id, parent_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_products_trend_table()} 
	WHERE order_date_time >= current_date - interval '60' day 
	group by parent_sku;
	
DROP VIEW IF EXISTS analytics.product_sell_90days;
  create view analytics.product_sell_90days as 
	SELECT MAX(product_id) AS product_id, parent_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_products_trend_table()} 
	WHERE order_date_time >= current_date - interval '90' day 
	group by parent_sku;
	
DROP VIEW IF EXISTS analytics.product_sell_180days;
  create view analytics.product_sell_180days as 
	SELECT MAX(product_id) AS product_id, parent_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_products_trend_table()} 
	WHERE order_date_time >= current_date - interval '180' day 
	group by parent_sku;

DROP VIEW IF EXISTS analytics.out_of_stock_date;
    CREATE VIEW analytics.out_of_stock_date AS
    SELECT
        o.parent_sku,
        MAX(COALESCE(o.out_of_stock_date, o.max_start_date)) AS out_of_stock_date
    FROM (
        SELECT
            v.parent_sku,
            v.out_of_stock_date,
            oss.max_start_date
        FROM variants v
        LEFT JOIN (
            SELECT
                product_id,
                MAX(out_of_stock_start_date::timestamp) AS max_start_date
            FROM out_of_stock_sku
            GROUP BY product_id
        ) oss ON v.product_id = oss.product_id
    ) o
    GROUP BY o.parent_sku;

DROP VIEW IF EXISTS analytics.out_of_stock_end_date;
  create view analytics.out_of_stock_end_date as 
    SELECT v.parent_sku AS parent_sku, MAX(o.out_of_stock_end_date) AS out_of_stock_end_date
    FROM out_of_stock_sku AS o
    LEFT JOIN variants AS v ON o.sku = v.variants_sku
    WHERE o.out_of_stock_end_date IS NOT NULL
    GROUP BY v.parent_sku;

DROP VIEW IF EXISTS analytics.zoho_expiry_date;
    CREATE VIEW analytics.zoho_expiry_date as 
    SELECT parent_sku, MIN(oldest_date) as oldest_date 
    FROM zoho_data
    WHERE oldest_date IS NOT NULL
    GROUP BY parent_sku;

DROP VIEW IF EXISTS analytics.zoho_rtv_data_parent_level;
CREATE VIEW analytics.zoho_rtv_data_parent_level AS
    SELECT 
        v.parent_sku,
        SUM(z.quantity) as total_rtv_quantity
    FROM zoho_rtv_data z
    LEFT JOIN variants v ON z.variant_sku = v.variants_sku
    WHERE v.parent_sku IS NOT NULL
    GROUP BY v.parent_sku;

DROP VIEW IF EXISTS analytics.instock_notify_parent_level;
CREATE VIEW analytics.instock_notify_parent_level AS
    SELECT
        parent_sku,
        COUNT(*) as total_instock_notify
    FROM product_instock_notify
    GROUP BY parent_sku;


DROP VIEW IF EXISTS analytics.zoho_customer_returns_data_parent_level;
CREATE VIEW analytics.zoho_customer_returns_data_parent_level AS
    SELECT 
        v.parent_sku,
        SUM(z.order_quantity) as total_return_quantity
    FROM zoho_customer_returns_data z
    LEFT JOIN variants v ON z.variant_sku = v.variants_sku
    WHERE v.parent_sku IS NOT NULL
    GROUP BY v.parent_sku;

DROP VIEW IF EXISTS analytics.out_of_stock_occurrence_by_parent;
CREATE VIEW analytics.out_of_stock_occurrence_by_parent AS
WITH variant_occurrence AS (
    SELECT 
        v.parent_sku,
        v.variants_sku,
        COUNT(oos.sku) AS raw_occurrence
    FROM variants v
    LEFT JOIN out_of_stock_sku oos ON oos.sku = v.variants_sku
    WHERE v.variant_options IS NOT NULL 
      AND v.variant_options != '' 
    GROUP BY v.parent_sku, v.variants_sku
),
zero_inventory_flags AS (
    SELECT 
        v.parent_sku,
        v.variants_sku,
        CASE WHEN v.variants_inventory_level = 0 THEN 1 ELSE 0 END AS zero_inventory_flag
    FROM variants v
    WHERE v.variant_options IS NOT NULL 
      AND v.variant_options != '' 
),
variant_combined AS (
    SELECT 
        vo.parent_sku,
        vo.variants_sku,
        vo.raw_occurrence + COALESCE(zf.zero_inventory_flag, 0) AS final_occurrence
    FROM variant_occurrence vo
    LEFT JOIN zero_inventory_flags zf 
      ON vo.variants_sku = zf.variants_sku
),
parent_sku_summary AS (
    SELECT 
        parent_sku,
        SUM(final_occurrence) AS total_out_of_stock_occurrence,
        MIN(final_occurrence) FILTER (WHERE final_occurrence > 0) AS min_occurrence,
        MAX(final_occurrence) AS max_occurrence
    FROM variant_combined
    GROUP BY parent_sku
)
SELECT 
    parent_sku,
    total_out_of_stock_occurrence,
    min_occurrence,
    max_occurrence,
    CASE 
        WHEN min_occurrence IS NULL THEN CAST(max_occurrence AS TEXT)  -- if all were 0
        WHEN min_occurrence = max_occurrence THEN CAST(max_occurrence AS TEXT)
        ELSE CONCAT(min_occurrence, '-', max_occurrence)
    END AS occurrence_range
FROM parent_sku_summary;
           	
DROP VIEW IF EXISTS analytics.sell_summary_products;
create view analytics.sell_summary_products as 
SELECT d180.product_id, d180.parent_sku, d7.quantity as d7_quantity, d15.quantity as d15_quantity,
d30.quantity as d30_quantity, d45.quantity as d45_quantity, 
d60.quantity as d60_quantity, d90.quantity as d90_quantity,d180.quantity as d180_quantity
FROM analytics.product_sell_180days d180 left join analytics.product_sell_90days d90 on d180.parent_sku = d90.parent_sku 
left join analytics.product_sell_60days d60 on d180.parent_sku = d60.parent_sku
left join analytics.product_sell_45days d45 on d180.parent_sku = d45.parent_sku
left join analytics.product_sell_30days d30 on d180.parent_sku = d30.parent_sku
left join analytics.product_sell_15days d15 on d180.parent_sku = d15.parent_sku
left join analytics.product_sell_7days d7 on d180.parent_sku = d7.parent_sku;

DROP VIEW IF EXISTS analytics.product_sell_cur_month;
create view analytics.product_sell_cur_month as 
	SELECT MAX(product_id) AS product_id, parent_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_products_trend_table()} 
	WHERE order_year = date_part('year', (SELECT current_timestamp)) and
	order_month = date_part('month', (SELECT current_timestamp))
	group by parent_sku;
	
DROP VIEW IF EXISTS analytics.product_sell_cur_less_1_month;
create view analytics.product_sell_cur_less_1_month as 
	SELECT MAX(product_id) AS product_id, parent_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_products_trend_table()} 
	WHERE order_year = date_part('year', (current_date - interval '1' month)) and
	order_month = date_part('month', (current_date - interval '1' month))
	group by parent_sku;
	
	
DROP VIEW IF EXISTS analytics.product_sell_cur_less_2_month;
create view analytics.product_sell_cur_less_2_month as 
	SELECT MAX(product_id) AS product_id, parent_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_products_trend_table()} 
	WHERE order_year = date_part('year', (current_date - interval '2' month)) and
	order_month = date_part('month', (current_date - interval '2' month))
	group by parent_sku;
	
DROP VIEW IF EXISTS analytics.product_sell_cur_less_3_month;
create view analytics.product_sell_cur_less_3_month as 
	SELECT MAX(product_id) AS product_id, parent_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_products_trend_table()} 
	WHERE order_year = date_part('year', (current_date - interval '3' month)) and
	order_month = date_part('month', (current_date - interval '3' month))
	group by parent_sku;
	
DROP VIEW IF EXISTS analytics.product_sell_cur_less_4_month;
create view analytics.product_sell_cur_less_4_month as 
	SELECT MAX(product_id) AS product_id, parent_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_products_trend_table()} 
	WHERE order_year = date_part('year', (current_date - interval '4' month)) and
	order_month = date_part('month', (current_date - interval '4' month))
	group by parent_sku;
	
DROP VIEW IF EXISTS analytics.product_sell_cur_less_5_month;
create view analytics.product_sell_cur_less_5_month as 
	SELECT MAX(product_id) AS product_id, parent_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_products_trend_table()} 
	WHERE order_year = date_part('year', (current_date - interval '5' month)) and
	order_month = date_part('month', (current_date - interval '5' month))
	group by parent_sku;
	
DROP VIEW IF EXISTS analytics.product_sell_cur_less_6_month;
create view analytics.product_sell_cur_less_6_month as 
	SELECT MAX(product_id) AS product_id, parent_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_products_trend_table()} 
	WHERE order_year = date_part('year', (current_date - interval '6' month)) and
	order_month = date_part('month', (current_date - interval '6' month))
	group by parent_sku;


DROP VIEW IF EXISTS analytics.replenishment_product_view;
create view analytics.replenishment_product_view as 
select max(pd.product_name) as product_title, sv.parent_sku, 
max(sv.cost) as cost, max(sv.retail_price) as retail_price, max(sv.sale_price) as sale_price, 
max(sv.reorder_point) as reorder_point, max(sv.incremental_quantity) as incremental_quantity,
sum(sv.quantity_on_hand) as quantity_on_hand, sum(sv.quantity_pending) as quantity_pending,
sum(sv.quantity_incoming) as quantity_incoming, sum(sv.quantity_available) as quantity_available,
sum(sv.quantity_on_hold) as quantity_on_hold           
from public.skuvault_catalog sv, public.products pd
where sv.parent_sku = pd.sku and sv.primary_supplier <> 'Unknown'
group by sv.parent_sku;

DROP VIEW IF EXISTS analytics.sell_summary_variants;
DROP VIEW IF EXISTS analytics.variant_sell_7days;
create view analytics.variant_sell_7days as 
	SELECT MAX(variant_id) AS variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_variants_trend_table()} 
	WHERE order_date_time >= current_date - interval '7' day 
	group by variants_sku;
	
DROP VIEW IF EXISTS analytics.variant_sell_15days;
create view analytics.variant_sell_15days as 
	SELECT MAX(variant_id) AS variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_variants_trend_table()} 
	WHERE order_date_time >= current_date - interval '15' day 
	group by variants_sku;
	
DROP VIEW IF EXISTS analytics.variant_sell_30days;
create view analytics.variant_sell_30days as 
	SELECT MAX(variant_id) AS variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_variants_trend_table()} 
	WHERE order_date_time >= current_date - interval '30' day 
	group by variants_sku;
	
DROP VIEW IF EXISTS analytics.variant_sell_45days;
create view analytics.variant_sell_45days as 
	SELECT MAX(variant_id) AS variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_variants_trend_table()} 
	WHERE order_date_time >= current_date - interval '45' day 
	group by variants_sku;
	
DROP VIEW IF EXISTS analytics.variant_sell_60days;
create view analytics.variant_sell_60days as 
	SELECT MAX(variant_id) AS variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_variants_trend_table()} 
	WHERE order_date_time >= current_date - interval '60' day 
	group by variants_sku;
	
DROP VIEW IF EXISTS analytics.variant_sell_90days;
create view analytics.variant_sell_90days as 
	SELECT MAX(variant_id) AS variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_variants_trend_table()} 
	WHERE order_date_time >= current_date - interval '90' day 
	group by variants_sku;
	
DROP VIEW IF EXISTS analytics.variant_sell_180days;
create view analytics.variant_sell_180days as 
	SELECT MAX(variant_id) AS variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_variants_trend_table()} 
	WHERE order_date_time >= current_date - interval '180' day 
	group by variants_sku;

DROP VIEW IF EXISTS analytics.latest_out_of_stock_info;
CREATE VIEW analytics.latest_out_of_stock_info AS
    SELECT DISTINCT ON (sku)
        sku,
        inventory_level,
        out_of_stock_start_date,
        out_of_stock_end_date
    FROM out_of_stock_sku
    WHERE out_of_stock_end_date IS NOT NULL
    ORDER BY sku, out_of_stock_end_date DESC;

DROP VIEW IF EXISTS analytics.total_out_of_stock_days;
CREATE VIEW analytics.total_out_of_stock_days AS
WITH base_days AS (
    SELECT 
        oos.sku,
        COUNT(*) AS total_days
    FROM out_of_stock_sku oos
    GROUP BY oos.sku
),
inventory_flags AS (
    SELECT 
        v.variants_sku,
        CASE 
            WHEN v.variants_inventory_level = 0 THEN 1 
            ELSE 0 
        END AS zero_inventory_flag
    FROM variants v
)
SELECT 
    b.sku,
    b.total_days + COALESCE(i.zero_inventory_flag, 0) AS total_days
FROM base_days b
LEFT JOIN inventory_flags i ON b.sku = i.variants_sku;

DROP VIEW IF EXISTS analytics.zoho_expiry_date_variant_level;
    CREATE VIEW analytics.zoho_expiry_date_variant_level AS
    SELECT 
        sku,
        expiry_dates,
        MIN(oldest_date) AS oldest_date
    FROM zoho_data
    WHERE oldest_date IS NOT NULL
    GROUP BY sku, expiry_dates;

DROP VIEW IF EXISTS analytics.zoho_rtv_data_variant_level;
    CREATE VIEW analytics.zoho_rtv_data_variant_level AS
    SELECT 
        variant_sku,
        SUM(quantity) as quantity
    FROM zoho_rtv_data
    GROUP BY variant_sku;

DROP VIEW IF EXISTS analytics.zoho_customer_returns_data_variant_level;
    CREATE VIEW analytics.zoho_customer_returns_data_variant_level AS
    SELECT 
        variant_sku,
        SUM(order_quantity) as order_quantity
    FROM zoho_customer_returns_data
    GROUP BY variant_sku;

DROP VIEW IF EXISTS analytics.instock_notify_variant_level;
CREATE VIEW analytics.instock_notify_variant_level AS
    SELECT
        variant_sku,
        COUNT(*) as total_instock_notify
    FROM product_instock_notify
    GROUP BY variant_sku;


DROP VIEW IF EXISTS analytics.sell_summary_variants;
CREATE VIEW analytics.sell_summary_variants AS
WITH d180 AS (
  SELECT
    variants_sku,
    MAX(variant_id) AS variant_id,
    MAX(product_id) AS product_id,
    MAX(parent_sku) AS parent_sku,
    SUM(quantity)::int AS quantity
  FROM analytics.variant_sell_180days
  GROUP BY variants_sku
),
d90 AS (
  SELECT variants_sku, SUM(quantity)::int AS quantity
  FROM analytics.variant_sell_90days
  GROUP BY variants_sku
),
d60 AS (
  SELECT variants_sku, SUM(quantity)::int AS quantity
  FROM analytics.variant_sell_60days
  GROUP BY variants_sku
),
d45 AS (
  SELECT variants_sku, SUM(quantity)::int AS quantity
  FROM analytics.variant_sell_45days
  GROUP BY variants_sku
),
d30 AS (
  SELECT variants_sku, SUM(quantity)::int AS quantity
  FROM analytics.variant_sell_30days
  GROUP BY variants_sku
),
d15 AS (
  SELECT variants_sku, SUM(quantity)::int AS quantity
  FROM analytics.variant_sell_15days
  GROUP BY variants_sku
),
d7 AS (
  SELECT variants_sku, SUM(quantity)::int AS quantity
  FROM analytics.variant_sell_7days
  GROUP BY variants_sku
)
SELECT
  d180.variant_id,
  d180.product_id,
  d180.parent_sku,
  d180.variants_sku,
  d180.quantity AS d180_quantity,
  d90.quantity AS d90_quantity,
  d60.quantity AS d60_quantity,
  d45.quantity AS d45_quantity,
  d30.quantity AS d30_quantity,
  d15.quantity AS d15_quantity,
  d7.quantity AS d7_quantity
FROM d180
LEFT JOIN d90 ON d180.variants_sku = d90.variants_sku
LEFT JOIN d60 ON d180.variants_sku = d60.variants_sku
LEFT JOIN d45 ON d180.variants_sku = d45.variants_sku
LEFT JOIN d30 ON d180.variants_sku = d30.variants_sku
LEFT JOIN d15 ON d180.variants_sku = d15.variants_sku
LEFT JOIN d7 ON d180.variants_sku = d7.variants_sku;

DROP VIEW IF EXISTS analytics.variant_sell_cur_month;
create view analytics.variant_sell_cur_month as 
	SELECT MAX(variant_id) AS variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_variants_trend_table()} 
	WHERE order_year = date_part('year', (SELECT current_timestamp)) and
	date_part('month', (order_date_time)) = date_part('month', (SELECT current_timestamp))
	group by variants_sku;
	
DROP VIEW IF EXISTS analytics.variant_sell_cur_less_1_month;
create view analytics.variant_sell_cur_less_1_month as 
	SELECT MAX(variant_id) AS variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_variants_trend_table()} 
	WHERE order_year = date_part('year', (current_date - interval '1' month)) and
	date_part('month', (order_date_time)) = date_part('month', (current_date - interval '1' month))
	group by variants_sku;
	
DROP VIEW IF EXISTS analytics.variant_sell_cur_less_2_month;
create view analytics.variant_sell_cur_less_2_month as 
	SELECT MAX(variant_id) AS variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_variants_trend_table()} 
	WHERE order_year = date_part('year', (current_date - interval '2' month)) and
	date_part('month', (order_date_time)) = date_part('month', (current_date - interval '2' month))
	group by variants_sku;
	
DROP VIEW IF EXISTS analytics.variant_sell_cur_less_3_month;
create view analytics.variant_sell_cur_less_3_month as 
	SELECT MAX(variant_id) AS variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_variants_trend_table()} 
	WHERE order_year = date_part('year', (current_date - interval '3' month)) and
	date_part('month', (order_date_time)) = date_part('month', (current_date - interval '3' month))
	group by variants_sku;
	
DROP VIEW IF EXISTS analytics.variant_sell_cur_less_4_month;
create view analytics.variant_sell_cur_less_4_month as 
	SELECT MAX(variant_id) AS variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_variants_trend_table()} 
	WHERE order_year = date_part('year', (current_date - interval '4' month)) and
	date_part('month', (order_date_time)) = date_part('month', (current_date - interval '4' month))
	group by variants_sku;
	
DROP VIEW IF EXISTS analytics.variant_sell_cur_less_5_month;
create view analytics.variant_sell_cur_less_5_month as 
	SELECT MAX(variant_id) AS variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_variants_trend_table()} 
	WHERE order_year = date_part('year', (current_date - interval '5' month)) and
	date_part('month', (order_date_time)) = date_part('month', (current_date - interval '5' month))
	group by variants_sku;
	
DROP VIEW IF EXISTS analytics.variant_sell_cur_less_6_month;
create view analytics.variant_sell_cur_less_6_month as 
	SELECT MAX(variant_id) AS variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM {AnalyticsDB.get_variants_trend_table()} 
	WHERE order_year = date_part('year', (current_date - interval '6' month)) and
	date_part('month', (order_date_time)) = date_part('month', (current_date - interval '6' month))
	group by variants_sku;

DROP VIEW IF EXISTS analytics.replenishment_variant_view;
create view analytics.replenishment_variant_view as 
select max(sv.title) as product_title, max(sv.parent_sku) as parent_sku, sv.sku, 
max(sv.cost) as cost, max(sv.retail_price) as retail_price, max(sv.sale_price) as sale_price, 
max(sv.reorder_point) as reorder_point, max(sv.incremental_quantity) as incremental_quantity,
sum(sv.quantity_on_hand) as quantity_on_hand, sum(sv.quantity_pending) as quantity_pending,
sum(sv.quantity_incoming) as quantity_incoming, sum(sv.quantity_available) as quantity_available,
sum(sv.quantity_on_hold) as quantity_on_hold           
from public.skuvault_catalog sv
where sv.primary_supplier <> 'Unknown'
group by sv.sku;
    """
    conn.execute(text(query))
    conn.commit()

# DROP VIEW IF EXISTS analytics.total_out_of_stock_days;
#     CREATE VIEW analytics.total_out_of_stock_days AS
#     SELECT 
#         sku,
#         COUNT(*) AS total_days
#     FROM out_of_stock_sku
#     GROUP BY sku;
def create_replenishment_table(store_id):
    conn = None
    try:
        conn = db.get_connection(store_id)

        query = f"""
            DROP TABLE if exists {AnalyticsDB.get_replenishment_products_table()}_1; 
            DROP TABLE if exists {AnalyticsDB.get_replenishment_variants_table()}_1;           
        """
        create_views(conn)
        conn.execute(text(query))
        conn.commit()
        query = f"""
            create table {AnalyticsDB.get_replenishment_products_table()}_1 as  
            select rp.product_title,rp.parent_sku,su.product_id, rp.cost, rp.retail_price,rp.sale_price, rp.reorder_point,
            rp.incremental_quantity,rp.quantity_on_hand,rp.quantity_pending, rp.quantity_incoming, rp.quantity_available,
            rp.quantity_on_hold, COALESCE(su.d180_quantity, 0) AS total_sold_180, COALESCE(su.d90_quantity, 0) AS total_sold_90, 
            COALESCE(su.d60_quantity, 0) AS total_sold_60, COALESCE(su.d45_quantity, 0) AS total_sold_45, COALESCE(su.d30_quantity, 0) AS total_sold_30,
            COALESCE(su.d15_quantity, 0) AS total_sold_15, COALESCE(su.d7_quantity, 0) AS total_sold_7,            
            CEIL((COALESCE(su.d30_quantity, 0) / 4.28 - rp.quantity_available) - rp.quantity_incoming) AS suggested_order_qty_7,
            CEIL((COALESCE(su.d30_quantity, 0) / 0.5 - rp.quantity_available) - rp.quantity_incoming) AS suggested_order_qty_15,  
            (COALESCE(su.d30_quantity, 0) - rp.quantity_available) - rp.quantity_incoming AS suggested_order_qty_30,
            (COALESCE(su.d30_quantity, 0) * 1.5 - rp.quantity_available) - rp.quantity_incoming AS suggested_order_qty_45,
            (COALESCE(su.d30_quantity, 0) * 2 - rp.quantity_available) - rp.quantity_incoming AS suggested_order_qty_60,
            (COALESCE(su.d30_quantity, 0) * 3 - rp.quantity_available) - rp.quantity_incoming AS suggested_order_qty_90,
            (COALESCE(su.d30_quantity, 0) * 6 - rp.quantity_available) - rp.quantity_incoming AS suggested_order_qty_180,
            CASE 
                WHEN CEIL((COALESCE(su.d30_quantity, 0) / 4.28 - rp.quantity_available) - rp.quantity_incoming) > 0 
                THEN CEIL(CEIL(COALESCE(su.d30_quantity, 0) / 4.28 - rp.quantity_available - rp.quantity_incoming) / rp.incremental_quantity) * rp.incremental_quantity 
                ELSE 0 
            END AS to_order_qty_7, 
            CASE 
                WHEN CEIL((COALESCE(su.d30_quantity, 0) / 0.5 - rp.quantity_available) - rp.quantity_incoming) > 0 
                THEN CEIL(CEIL(COALESCE(su.d30_quantity, 0) / 0.5 - rp.quantity_available - rp.quantity_incoming) / rp.incremental_quantity) * rp.incremental_quantity 
                ELSE 0 
            END AS to_order_qty_15,
            CASE 
                WHEN (COALESCE(su.d30_quantity, 0) - rp.quantity_available) - rp.quantity_incoming > 0 
                THEN CEIL((COALESCE(su.d30_quantity, 0) - rp.quantity_available - rp.quantity_incoming) / rp.incremental_quantity) * rp.incremental_quantity 
                ELSE 0 
            END AS to_order_qty_30,
            CASE 
                WHEN (COALESCE(su.d30_quantity, 0) * 1.5 - rp.quantity_available) - rp.quantity_incoming > 0 
                THEN CEIL((COALESCE(su.d30_quantity, 0) * 1.5 - rp.quantity_available - rp.quantity_incoming) / rp.incremental_quantity) * rp.incremental_quantity 
                ELSE 0 
            END AS to_order_qty_45,
            CASE 
                WHEN (COALESCE(su.d30_quantity, 0) * 2 - rp.quantity_available) - rp.quantity_incoming > 0 
                THEN CEIL((COALESCE(su.d30_quantity, 0) * 2 - rp.quantity_available - rp.quantity_incoming) / rp.incremental_quantity) * rp.incremental_quantity 
                ELSE 0 
            END AS to_order_qty_60,
             CASE 
                WHEN (COALESCE(su.d30_quantity, 0) * 3 - rp.quantity_available) - rp.quantity_incoming > 0 
                THEN CEIL((COALESCE(su.d30_quantity, 0) * 3 - rp.quantity_available - rp.quantity_incoming) / rp.incremental_quantity) * rp.incremental_quantity 
                ELSE 0 
            END AS to_order_qty_90,
            CASE 
                WHEN (COALESCE(su.d30_quantity, 0) * 6 - rp.quantity_available) - rp.quantity_incoming > 0 
                THEN CEIL((COALESCE(su.d30_quantity, 0) * 6 - rp.quantity_available - rp.quantity_incoming) / rp.incremental_quantity) * rp.incremental_quantity 
                ELSE 0 
            END AS to_order_qty_180,                                
            m6.quantity as month_7, m5.quantity as month_6, m4.quantity as month_5,
            m3.quantity as month_4, m2.quantity as month_3, m1.quantity as month_2, cur_m.quantity as month_1,
            o.out_of_stock_date as out_of_stock_date,
            oe.out_of_stock_end_date as out_of_stock_end_date,
            ze.oldest_date as oldest_expiry_date,
            zr.total_rtv_quantity as total_rtv_quantity,
            zcr.total_return_quantity as total_return_quantity,
            inp.total_instock_notify as total_instock_notify,
            oobp.total_out_of_stock_occurrence as total_out_of_stock_occurrence,
            oobp.min_occurrence as min_occurrence,
            oobp.max_occurrence as max_occurrence,
            oobp.occurrence_range as occurrence_range
            from analytics.replenishment_product_view rp left join analytics.sell_summary_products su on rp.parent_sku = su.parent_sku
            left join analytics.product_sell_cur_month cur_m on rp.parent_sku = cur_m.parent_sku
            left join analytics.product_sell_cur_less_1_month m1 on rp.parent_sku = m1.parent_sku
            left join analytics.product_sell_cur_less_2_month m2 on rp.parent_sku = m2.parent_sku
            left join analytics.product_sell_cur_less_3_month m3 on rp.parent_sku = m3.parent_sku
            left join analytics.product_sell_cur_less_4_month m4 on rp.parent_sku = m4.parent_sku
            left join analytics.product_sell_cur_less_5_month m5 on rp.parent_sku = m5.parent_sku
            left join analytics.product_sell_cur_less_6_month m6 on rp.parent_sku = m6.parent_sku
            left join analytics.out_of_stock_date o on rp.parent_sku = o.parent_sku
            left join analytics.out_of_stock_end_date oe on rp.parent_sku = oe.parent_sku
            left join analytics.zoho_expiry_date ze on rp.parent_sku = ze.parent_sku
            left join analytics.zoho_rtv_data_parent_level zr on rp.parent_sku = zr.parent_sku
            left join analytics.zoho_customer_returns_data_parent_level zcr on rp.parent_sku = zcr.parent_sku
            left join analytics.instock_notify_parent_level inp on rp.parent_sku = inp.parent_sku
            left join analytics.out_of_stock_occurrence_by_parent oobp on rp.parent_sku = oobp.parent_sku;       
        """
        
        logger.info(f"Creating {AnalyticsDB.get_replenishment_products_table()}_1 table")         
        start_time = datetime.datetime.now().timestamp()
        conn.execute(text(query))
        conn.commit()
        end_time = datetime.datetime.now().timestamp()
        total_time = (end_time - start_time)
        logger.info(f"{AnalyticsDB.get_replenishment_products_table()}_1 table has been created. Time: {total_time}")          
        
        query = f"""
            create table {AnalyticsDB.get_replenishment_variants_table()}_1 as  
            select rp.product_title, su.product_id, rp.parent_sku, rp.sku, su.variant_id,
            rp.cost, rp.retail_price, rp.sale_price, rp.reorder_point, rp.incremental_quantity,
            rp.quantity_on_hand, rp.quantity_pending, rp.quantity_incoming, rp.quantity_available,
            rp.quantity_on_hold, COALESCE(su.d180_quantity, 0) AS total_sold_180, COALESCE(su.d90_quantity, 0) AS total_sold_90, COALESCE(su.d60_quantity, 0) AS total_sold_60, COALESCE(su.d45_quantity, 0) AS total_sold_45, COALESCE(su.d30_quantity, 0) AS total_sold_30,
            COALESCE(su.d15_quantity, 0) AS total_sold_15, COALESCE(su.d7_quantity, 0) AS total_sold_7,             
            CEIL((COALESCE(su.d30_quantity, 0) / 4.28 - rp.quantity_available) - rp.quantity_incoming) AS suggested_order_qty_7,
            CEIL((COALESCE(su.d30_quantity, 0) / 0.5 - rp.quantity_available) - rp.quantity_incoming) AS suggested_order_qty_15,  
            (COALESCE(su.d30_quantity, 0) - rp.quantity_available) - rp.quantity_incoming AS suggested_order_qty_30,
            (COALESCE(su.d30_quantity, 0) * 1.5 - rp.quantity_available) - rp.quantity_incoming AS suggested_order_qty_45,
            (COALESCE(su.d30_quantity, 0) * 2 - rp.quantity_available) - rp.quantity_incoming AS suggested_order_qty_60,
            (COALESCE(su.d30_quantity, 0) * 3 - rp.quantity_available) - rp.quantity_incoming AS suggested_order_qty_90,
            (COALESCE(su.d30_quantity, 0) * 6 - rp.quantity_available) - rp.quantity_incoming AS suggested_order_qty_180,            
            CASE 
                WHEN CEIL((COALESCE(su.d30_quantity, 0) / 4.28 - rp.quantity_available) - rp.quantity_incoming) > 0 
                THEN CEIL(CEIL(COALESCE(su.d30_quantity, 0) / 4.28 - rp.quantity_available - rp.quantity_incoming) / rp.incremental_quantity) * rp.incremental_quantity 
                ELSE 0 
            END AS to_order_qty_7, 
            CASE 
                WHEN CEIL((COALESCE(su.d30_quantity, 0) / 0.5 - rp.quantity_available) - rp.quantity_incoming) > 0 
                THEN CEIL(CEIL(COALESCE(su.d30_quantity, 0) / 0.5 - rp.quantity_available - rp.quantity_incoming) / rp.incremental_quantity) * rp.incremental_quantity 
                ELSE 0 
            END AS to_order_qty_15,
            CASE 
                WHEN (COALESCE(su.d30_quantity, 0) - rp.quantity_available) - rp.quantity_incoming > 0 
                THEN CEIL((COALESCE(su.d30_quantity, 0) - rp.quantity_available - rp.quantity_incoming) / rp.incremental_quantity) * rp.incremental_quantity 
                ELSE 0 
            END AS to_order_qty_30,
            CASE 
                WHEN (COALESCE(su.d30_quantity, 0) * 1.5 - rp.quantity_available) - rp.quantity_incoming > 0 
                THEN CEIL((COALESCE(su.d30_quantity, 0) * 1.5 - rp.quantity_available - rp.quantity_incoming) / rp.incremental_quantity) * rp.incremental_quantity 
                ELSE 0 
            END AS to_order_qty_45,
            CASE 
                WHEN (COALESCE(su.d30_quantity, 0) * 2 - rp.quantity_available) - rp.quantity_incoming > 0 
                THEN CEIL((COALESCE(su.d30_quantity, 0) * 2 - rp.quantity_available - rp.quantity_incoming) / rp.incremental_quantity) * rp.incremental_quantity 
                ELSE 0 
            END AS to_order_qty_60,
            CASE 
                WHEN (COALESCE(su.d30_quantity, 0) * 3 - rp.quantity_available) - rp.quantity_incoming > 0 
                THEN CEIL((COALESCE(su.d30_quantity, 0) * 3 - rp.quantity_available - rp.quantity_incoming) / rp.incremental_quantity) * rp.incremental_quantity 
                ELSE 0 
            END AS to_order_qty_90,
            CASE 
                WHEN (COALESCE(su.d30_quantity, 0) * 6 - rp.quantity_available) - rp.quantity_incoming > 0 
                THEN CEIL((COALESCE(su.d30_quantity, 0) * 6 - rp.quantity_available - rp.quantity_incoming) / rp.incremental_quantity) * rp.incremental_quantity 
                ELSE 0 
            END AS to_order_qty_180,                                 
            m6.quantity as month_7, m5.quantity as month_6, m4.quantity as month_5,
            m3.quantity as month_4, m2.quantity as month_3, 
            m1.quantity as month_2, cur_m.quantity as month_1,
            lo.inventory_level as restocked_inventory_level, lo.out_of_stock_start_date, lo.out_of_stock_end_date, tod.total_days as total_out_of_stock_days,
            zev.expiry_dates as expiry_dates, zev.oldest_date as oldest_expiry_date,
            zrt.quantity as rtv_quantity,
            zcr.order_quantity as order_quantity,
            inp.total_instock_notify as total_instock_notify
            from analytics.replenishment_variant_view rp left join analytics.sell_summary_variants su on rp.sku = su.variants_sku
            left join analytics.variant_sell_cur_month cur_m on rp.sku = cur_m.variants_sku
            left join analytics.variant_sell_cur_less_1_month m1 on rp.sku = m1.variants_sku
            left join analytics.variant_sell_cur_less_2_month m2 on rp.sku = m2.variants_sku
            left join analytics.variant_sell_cur_less_3_month m3 on rp.sku = m3.variants_sku
            left join analytics.variant_sell_cur_less_4_month m4 on rp.sku = m4.variants_sku
            left join analytics.variant_sell_cur_less_5_month m5 on rp.sku = m5.variants_sku
            left join analytics.variant_sell_cur_less_6_month m6 on rp.sku = m6.variants_sku
            left join analytics.latest_out_of_stock_info lo on rp.sku = lo.sku
            left join analytics.total_out_of_stock_days tod on rp.sku = tod.sku
            left join analytics.zoho_expiry_date_variant_level zev on rp.sku = zev.sku
            left join analytics.zoho_rtv_data_variant_level zrt on rp.sku = zrt.variant_sku
            left join analytics.zoho_customer_returns_data_variant_level zcr on rp.sku = zcr.variant_sku
            left join analytics.instock_notify_variant_level inp on rp.sku = inp.variant_sku;
        """
        logger.info(f"Creating {AnalyticsDB.get_replenishment_variants_table()}_1 table")                
        start_time = datetime.datetime.now().timestamp()
        conn.execute(text(query))
        conn.commit()
        end_time = datetime.datetime.now().timestamp()
        total_time = (end_time - start_time)
        logger.info(f"{AnalyticsDB.get_replenishment_variants_table()}_1 table has been created. Time: {total_time}")                              

        query = f"""
            DROP TABLE if exists {AnalyticsDB.get_replenishment_products_table()};
            ALTER TABLE {AnalyticsDB.get_replenishment_products_table()}_1 RENAME TO {AnalyticsDB.replenishment_products_table}; 
            DROP TABLE if exists {AnalyticsDB.get_replenishment_variants_table()};
            ALTER TABLE {AnalyticsDB.get_replenishment_variants_table()}_1 RENAME TO {AnalyticsDB.replenishment_variants_table};           
        """
        conn.execute(text(query))
        conn.commit()
       
    except Exception as ex:
        error_msg = str(traceback.format_exc())
        logger.error(error_msg)
        raise Exception("Failed to create analytics replenishment table: " + error_msg)
    finally:
        if conn:
            conn.commit()
            conn.close()

def get_replenishment_aggregate_data_csv(store_id, query_params):
    response = {
        "status": 400,
    }
    store = store_util.get_store_by_id(store_id)
    user_details = store_db.fetch_user_by_username(store_id, query_params['username'])
    user_name = user_details['name'] if 'name' in user_details and user_details else ''
    query_brand = query_params.get('brand','').strip()
    query_classification = query_params.get('classification','').strip()
    query_tags = query_params.get('tags','').strip()
    hide_discontinued = query_params.get('hide_discontinued', 'true')    
    show_zero_qty_products = query_params.get('show_zero_qty_products', 'true')    
    query_primary_supplier = query_params.get('primary_supplier', None) 
    sort_by = query_params.get('sort_by','').strip()
    sort_array = sort_by.split("/") if sort_by != '' else []
    search_key = query_params.get('searchKey', '').strip()
    search_value = query_params.get('searchValue', None)
    filtered_skus = query_params.get('filtered_skus','').strip()
    if query_primary_supplier and query_primary_supplier.strip() != '':
        query_primary_supplier = query_primary_supplier.split(";")
    else:
        query_primary_supplier = []   
    user = query_params.get('user', None)

    sale_history_months = query_params.get('sale_history_months', 6)
    days_to_replenishment = query_params.get('days_to_replenishment', 30) 
    no_sold_days = query_params.get('no_sold_days', False)
    classified_as = query_params.get('classified_as', None) 

    column_query = ''   
    
    if days_to_replenishment:
        column_query = column_query + 'sum(rp.to_order_qty_' + str(days_to_replenishment) + ')'
        column_query = column_query + ', sum(rp.total_sold_' + str(days_to_replenishment) + ')'
        column_query = column_query + ', sum(rp.suggested_order_qty_' + str(days_to_replenishment) + ') AS suggested_order_qty'        

    if sale_history_months:
        for i in range(int(sale_history_months), 0, -1):
            index = i + 1
            column_query = column_query + ', sum(rp.month_' + str(index) + ')'                   

    append_query = ''
    
    if query_brand != '':
        values_list = query_brand.split(';')
        trimmed_values_list = [value.strip()
                                for value in values_list]  # Trim each value 
        formatted_values = "','".join(trimmed_values_list)
        formatted_values = f"'{formatted_values}'"
        append_query = ' AND sv.brand IN(' + formatted_values + ')'

    if query_classification != '':
        values_list = query_classification.split(';')
        trimmed_values_list = [value.strip() for value in values_list]  # Trim spaces

        # Escape single quotes inside classification names if necessary
        escaped_values = [v.replace("'", "''") for v in trimmed_values_list]

        # Build a string like: 'value1','value2','value3'
        formatted_values = "','".join(escaped_values)

        # Build the SQL array literal with type cast
        append_query += f"""
        AND (
            ca.classifications && ARRAY['{formatted_values}']::varchar[]
        )
        """
   
    if search_value and search_value != '':
        append_query = append_query + " AND  (rp.product_title ILIKE '%" + search_value + "%' or rp.parent_sku ILIKE '%" + search_value + "%') "        

    if hide_discontinued == 'true':        
        append_query = append_query + " AND ((LOWER(rp.product_title) NOT ILIKE '%discontinued%') AND (LOWER(rp.product_title) NOT ILIKE '%discountinued%')) AND ((LOWER(sv.classification) NOT ILIKE '%discontinued%') AND (LOWER(sv.classification) NOT ILIKE '%discountinued%'))"
    
    if show_zero_qty_products == 'false':
        append_query = append_query + " AND rp.quantity_available > 0"
    
    if no_sold_days == 'true':
        append_query = append_query + " AND rp.total_sold_" + str(days_to_replenishment) + " = 0"

    append_conditions = []
    if classified_as:
        parts = [part.strip() for part in classified_as.split(",")]
        ids = [int(part) for part in parts if part.isdigit()]
        
        if "Unassigned" in parts:
            append_conditions.append("rd.classified_as_id IS NULL")

        if ids:
            placeholders = ','.join(map(str, ids))
            append_conditions.append(f"rd.classified_as_id IN ({placeholders})")

        if append_conditions:
            append_query += " AND (" + " OR ".join(append_conditions) + ")"

    order_by = ""
    if len(sort_array):
        sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
        order_by = " ORDER BY " + sort_array[0] + " " + sort_direction

    month_names, day_difference = common_util.get_month_array_for_meta(sale_history_months)
    conn = db.get_connection(store['id'])
    try:
        if len(query_primary_supplier) == 0 and user:
            query = f"SELECT suppliers FROM public.user_supplier_mapping where user_name='{user.strip()}'"
            rs = conn.execute(text(query))

            for _supplier in rs:
                query_primary_supplier.append(_supplier[0])
            if len(query_primary_supplier) == 0:
                response['message'] = 'Not found'
                response['data'] = {'data':[]}
                response['status'] = 200
                return response 
                           
        if len(query_primary_supplier):
            append_query += f"""
            AND (
                ca.primary_suppliers && ARRAY{query_primary_supplier}::varchar[]
            )
            """
        
        if filtered_skus != '':
            p_skus = filtered_skus.split(',')
            p_skus_list = [value.strip() for value in p_skus]  # Trim each value
            p_skus_list_values = "','".join(p_skus_list)
            append_query = append_query + f" AND rp.parent_sku IN ('{p_skus_list_values}')"
  
        # ...
        base_query = ''
        if query_tags != '':
            values_list = query_tags.split(',')
            trimmed_values_list = [value.strip() for value in values_list]  # Trim each value
            formatted_values = "','".join(trimmed_values_list)
            formatted_values = f"'{formatted_values}'"
            base_query = f"""WITH consolidated_skuvault AS (
                                SELECT DISTINCT primary_supplier, classification, max(brand) as brand, parent_sku
                                FROM skuvault_catalog GROUP BY parent_sku, primary_supplier, classification
                            ),
                            classifications_agg AS (
                                SELECT
                                    sc.parent_sku,
                                        ARRAY_AGG(DISTINCT COALESCE(rc.new_classification, sc.classification)) AS classifications,
                                        ARRAY_AGG(DISTINCT sc.primary_supplier) AS primary_suppliers
                                FROM skuvault_catalog sc
                                LEFT JOIN replenishment_classifications rc ON sc.sku = rc.sku
                                WHERE sc.parent_sku IS NOT NULL
                                GROUP BY sc.parent_sku
                            )
                        select MAX(rp.product_title) AS product_title, MAX(rp.product_id) AS product_id, rp.parent_sku, ca.primary_suppliers, ca.classifications, MAX(sv.brand) AS brand, MAX(rp.cost) AS cost, MAX(rp.retail_price) AS retail_price, MAX(rp.sale_price) AS sale_price, MAX(rp.reorder_point) AS reorder_point, MAX(rp.incremental_quantity) AS incremental_quantity,
                        sum(DISTINCT rp.quantity_on_hand), sum(DISTINCT rp.quantity_pending), sum(DISTINCT rp.quantity_incoming), sum(DISTINCT rp.quantity_available), sum(DISTINCT rp.quantity_on_hold), 
                        sum(DISTINCT rp.suggested_order_qty_45) AS suggested_order_qty_45, sum(DISTINCT rp.suggested_order_qty_60) AS suggested_order_qty_60, sum(DISTINCT rp.month_1), rp.out_of_stock_date AS last_out_of_stock_date, rp.out_of_stock_end_date AS last_received_date, rp.oldest_expiry_date AS expiry_date, rd.classified_as, rp.total_rtv_quantity, rp.total_return_quantity AS total_zero_quantity, rp.total_instock_notify, rp.occurrence_range, {column_query} 
                        from {AnalyticsDB.get_replenishment_products_table()} rp left join public.product_tags pt ON rp.parent_sku = pt.sku
                        left join consolidated_skuvault sv ON sv.parent_sku = rp.parent_sku
                        left join replenishment_dashboard rd ON rp.parent_sku = rd.parent_sku
                        left join classifications_agg ca ON rp.parent_sku = ca.parent_sku
                        where pt.tag_id IN ({formatted_values}) AND sv.primary_supplier <> 'Unknown' {append_query}              
                        group by rp.parent_sku, ca.primary_suppliers, ca.classifications, rp.out_of_stock_date, rp.out_of_stock_end_date, rp.oldest_expiry_date, rd.classified_as, rp.total_rtv_quantity, rp.total_return_quantity, rp.total_instock_notify, rp.occurrence_range
                        {order_by}
                        """            
        else: 
            base_query = f"""WITH consolidated_skuvault AS (
                                SELECT DISTINCT primary_supplier, classification, max(brand) as brand, parent_sku
                                FROM skuvault_catalog GROUP BY parent_sku, primary_supplier, classification
                            ),
                            classifications_agg AS (
                                SELECT
                                    sc.parent_sku,
                                        ARRAY_AGG(DISTINCT COALESCE(rc.new_classification, sc.classification)) AS classifications,
                                        ARRAY_AGG(DISTINCT sc.primary_supplier) AS primary_suppliers
                                FROM skuvault_catalog sc
                                LEFT JOIN replenishment_classifications rc ON sc.sku = rc.sku
                                WHERE sc.parent_sku IS NOT NULL
                                GROUP BY sc.parent_sku
                            )
                        select MAX(rp.product_title) AS product_title, MAX(rp.product_id) AS product_id, rp.parent_sku, ca.primary_suppliers, ca.classifications, MAX(sv.brand) AS brand, MAX(rp.cost) AS cost, MAX(rp.retail_price) AS retail_price, MAX(rp.sale_price) AS sale_price, MAX(rp.reorder_point) AS reorder_point, MAX(rp.incremental_quantity) AS incremental_quantity,
                        sum(DISTINCT rp.quantity_on_hand), sum(DISTINCT rp.quantity_pending), sum(DISTINCT rp.quantity_incoming), sum(DISTINCT rp.quantity_available), sum(DISTINCT rp.quantity_on_hold), 
                        sum(DISTINCT rp.suggested_order_qty_45) AS suggested_order_qty_45, sum(DISTINCT rp.suggested_order_qty_60) AS suggested_order_qty_60, sum(DISTINCT rp.month_1), rp.out_of_stock_date AS last_out_of_stock_date, rp.out_of_stock_end_date AS last_received_date, rp.oldest_expiry_date AS expiry_date, rd.classified_as, rp.total_rtv_quantity, rp.total_return_quantity AS total_zero_quantity, rp.total_instock_notify, rp.occurrence_range, {column_query} 
                        from {AnalyticsDB.get_replenishment_products_table()} rp
                        left join consolidated_skuvault sv ON sv.parent_sku = rp.parent_sku
                        left join replenishment_dashboard rd ON rp.parent_sku = rd.parent_sku
                        left join classifications_agg ca ON rp.parent_sku = ca.parent_sku
                        where sv.primary_supplier <> 'Unknown' {append_query}              
                        group by rp.parent_sku, ca.primary_suppliers, ca.classifications, rp.out_of_stock_date, rp.out_of_stock_end_date, rp.oldest_expiry_date, rd.classified_as, rp.total_rtv_quantity, rp.total_return_quantity, rp.total_instock_notify, rp.occurrence_range
                        {order_by}
                        """                 

        final_query = base_query                                        
        query_result = conn.execute(text(final_query), {"suppliers": query_primary_supplier})

        formated_data = [] 
        parent_sku_str = ''                     
        if query_result:         
            for data in query_result:
                obj = {}   
                parent_sku_str = parent_sku_str + "'" + str(data[2]) + "', "       
                obj["Title"] = data[0]
                obj["Parent Sku"] = data[2] 
                obj['Item Search Term'] = ''               
                obj["Classification"] = ", ".join(data[4])
                obj["Classified As"] = data[22]
                obj["Supplier Company Name"] = ", ".join(data[3])
                obj["Brand"] = data[5]
                obj["Item Cost"] = float(data[6])
                obj["Retail Price"] = float(data[7])
                obj["Sale Price"] = float(data[8])
                # obj["reorder_point"] = float(data[9])
                obj["Incremental Quantity"] = int(data[10])
                obj["Case Quantity"] = "-"
                obj["Quantity On Hand"] = int(data[11])
                obj["Quantity Pending"] = int(data[12])
                obj["Quantity Incoming"] = int(data[13])
                obj["Quantity Available"] = int(data[14])
                obj["Quantity On Hold"] = int(data[15])                                         
                obj["Suggested Order Qty 45"] = int(data[16])
                obj["Suggested Order Qty 60"] = int(data[17])
                obj["Last Out Of Stock Date"] = data[19].strftime("%Y-%m-%d") if isinstance(data[19], datetime.datetime) and data[19] != None else None
                obj["Last Received Date"] = data[20]
                obj["Total RTV Quantity"] = data[23]
                obj["Total Return Quantity"] = data[24]
                obj["Total Instock Notify"] = data[25]
                obj["Total Out Of Stock Occurrence"] = f'="{data[26]}"'
                # obj["Expiry Date"] = data[21]
                
                expiry = data[21]  # e.g., "2029-04"
                try:
                    parsed_date = datetime.datetime.strptime(expiry, "%Y-%m")
                    obj["Expiry Date"] = parsed_date.strftime("%b-%Y").upper()  # "APR-2029"
                except Exception as e:
                    obj["Expiry Date"] = expiry  # fallback if format is invalid
                
                obj["Item Ordered Quantity"] = int(data[27])
                obj["Total Sold 30"] = int(data[28]) if data[28] != None else 0 
                obj["Suggested Order Qty"] = int(data[29])                      

                count = 1    
                total_sum = 0                                                         
                for i in range(int(sale_history_months), 0, -1):
                    month_index = i + 1
                    key = "month_" + str(month_index)
                    # if key not in month_names or key == 'month_1':
                    #     continue
                    value_key = 29 + count                                       
                    obj[month_names[key]] = int(data[value_key]) if data[value_key] != None else 'NA'
                    total_sum = total_sum + int(data[value_key]) if data[value_key] != None else 0
                    count = count+1 
                                    
                obj[month_names['month_1']] = int(data[18]) if data[18] != None else 'NA' 

                turn_rate = (((int(total_sum) / int(data[14]) if int(data[14]) != 0 else 1) * 365) / day_difference)                           
                weeks_on_hand = 52 / turn_rate if turn_rate != 0 else 1 
                obj["Total Sold"] = total_sum 
                obj["Turn Rate"] = float(turn_rate)
                obj["Weeks On Hand"] = float(weeks_on_hand)
                obj["Days To Replenishment"] = days_to_replenishment
                obj['Sale History Months'] = sale_history_months

                obj["Reserved Quantity"] = 0

                formated_data.append(obj)                                                                                                  
                                     
            parent_sku_str = parent_sku_str.rstrip(', ')
            child_data_array = get_on_load_child_data(store, parent_sku_str, '', sale_history_months, days_to_replenishment, hide_discontinued, query_tags)   
            if child_data_array['status'] == 200:
                child_array = child_data_array['data']['data']
                reserved_qty_array = child_data_array['data']['reserved_qty_data']
                item_order_qty_array = child_data_array['data']['item_order_qty_data']
                backorder_qty_array = child_data_array['data']['backorder_qty_data']
                safetystock_qty_array = child_data_array['data']['safetystock_qty_data']
                total_cost_array = child_data_array['data']['total_cost_data']
                case_qty_array = child_data_array['data']['case_qty_data']
                parent_sku_list = [sku.strip().strip("'") for sku in parent_sku_str.split(',')]
                
                for p_sku in parent_sku_list:                                        
                    for item in formated_data:                        
                        if p_sku == item.get('Parent Sku'):     
                            item['Reserved Quantity'] = reserved_qty_array.get(p_sku, 0)  
                            item['Item Order Qty'] = item_order_qty_array.get(p_sku, 0)   
                            item['Backorder Quantity'] = backorder_qty_array.get(p_sku, 0)
                            item['Safetystock Quantity'] = safetystock_qty_array.get(p_sku, 0)
                            item['Total Cost'] = total_cost_array.get(p_sku, 0)
                            item['Case Quantity'] = case_qty_array.get(p_sku, 0)
                            # item['Out Of Stock Date'] = "NA"              
                            # item['Days Out Of Stock'] = "NA"
                            child_variants = child_array.get(p_sku, [])
                            item['child_data'] = child_array.get(p_sku, {})
                            # Extract days_out_of_stock values from child_variants, skipping None or missing
                            days_list = [
                                child.get('Days Out Of Stock') 
                                for child in child_variants 
                                if child.get('Days Out Of Stock') not in (None, 0)
                            ]

                            if days_list:
                                min_days = min(days_list)
                                max_days = max(days_list)
                                if min_days == max_days:
                                    item['Days Out Of Stock'] = str(min_days)
                                else:
                                    item['Days Out Of Stock'] = f"{min_days}-{max_days}"
                            else:
                                item['Days Out Of Stock'] = 0  

                            # ✅ Add latest PO date at product level
                            latest_po_date = max(
                                [child.get('Latest PO Date', None) for child in child_variants if child.get('Latest PO Date')],
                                default=0
                            )
                            item['Latest PO Date'] = latest_po_date
                            break
            
            # Create CSV content in memory
            if formated_data:
                utc_now = datetime.datetime.now(pytz.utc)
                cst_now = utc_now.astimezone(pytz.timezone('America/Chicago'))
                created_date = cst_now .strftime("%m-%d-%Y_%H:%M")
                zip_filename = f"Replenishment_{created_date}.zip"

                csv_content = io.StringIO()

                keys = list(formated_data[0].keys())
                if 'child_data' in keys:
                    keys.remove('child_data')

                # Define the fields to reposition
                fields_to_move = ["Expiry Date", "Last Received Date", "Restocked Inventory Level", "Last Out Of Stock Date", "Total Out Of Stock Occurrence"]
                insert_after = "Days Out Of Stock"

                # Remove the fields to reposition from the list
                for field in fields_to_move:
                    if field in keys:
                        keys.remove(field)

                # Find index to insert after
                if insert_after in keys:
                    insert_index = keys.index(insert_after) + 1
                else:
                    insert_index = len(keys)  # If not found, append to the end

                # Insert the fields in order
                for i, field in enumerate(fields_to_move):
                    keys.insert(insert_index + i, field)

                writer = csv.DictWriter(csv_content, fieldnames=keys)
                writer.writeheader()
                
                for row in formated_data:
                    if 'child_data' in row:
                        parent_row = row.copy()
                        child_data = parent_row.pop('child_data', [])

                        parent_classified_as = row.get('Classified As', '')

                        # Write parent row first
                        writer.writerow(parent_row)

                        # Then write children
                        for child in child_data:
                            child['Classified As'] = parent_classified_as
                            writer.writerow(child)
                    else:
                        # If no child_data, write the row normally and add blank line
                        writer.writerow(row)
                
                if conn:
                    conn.close()  

                # Compress CSV into ZIP
                zip_buffer = compress_csv(csv_content, zip_filename)

                email_util.send_replenishment_csv_data_email(store_id, zip_buffer, query_params['username'], zip_filename, user_name, created_date)
    
    finally:
        if conn:
            conn.close()  


def get_on_load_child_data(store, parent_sku_str, append_query, sale_history_months, days_to_replenishment, hide_discontinued, query_tags):
    response = {
        "status": 400,
        "data": {}
    }
    column_query = ''   
    
    if days_to_replenishment:        
        column_query = column_query + 'rp.to_order_qty_' + str(days_to_replenishment)
        column_query = column_query + ', rp.total_sold_' + str(days_to_replenishment) 
        column_query = column_query + ', rp.suggested_order_qty_' + str(days_to_replenishment)        

    if sale_history_months:
        for i in range(int(sale_history_months), 0, -1):
            index = i + 1
            column_query = column_query + ', rp.month_' + str(index) 

    # if hide_discontinued == 'true':
    #         append_query = append_query + " AND rp.quantity_available > 0 "

    month_names, day_difference = common_util.get_month_array_for_meta(sale_history_months) 
    conn = db.get_connection(store['id'])
    try:
        query_result = None
        if parent_sku_str != '':
            if query_tags != '':
                values_list = query_tags.split(',')
                trimmed_values_list = [value.strip() for value in values_list]  # Trim each value
                formatted_values = "','".join(trimmed_values_list)
                formatted_values = f"'{formatted_values}'"
                base_query = f"""select rp.product_title, rp.sku, rp.parent_sku, sv.primary_supplier, COALESCE(rc.new_classification, sv.classification) AS classification, sv.brand, rp.cost, rp.retail_price, rp.sale_price, rp.reorder_point, rp.incremental_quantity,
                        rp.quantity_on_hand, rp.quantity_pending, rp.quantity_incoming, rp.quantity_available, rp.quantity_on_hold,
                        rp.suggested_order_qty_45, rp.suggested_order_qty_60, CASE WHEN po.quantity is NULL THEN 0 ELSE po.quantity END as item_order_qty, max(v.out_of_stock_date) as out_of_stock_date, rp.month_1, sum(rrv.quantity) as reserved_quantity, rbv.qty, rss.qty, sv.attribute4_value, rp.out_of_stock_end_date AS last_received_date, rp.total_out_of_stock_days AS total_out_of_stock_days, rp.restocked_inventory_level, rp.out_of_stock_start_date, rp.oldest_expiry_date AS expiry_date,
                        MAX(rp.rtv_quantity) AS rtv_quantity, MAX(rp.order_quantity) AS zero_quantity, rp.total_instock_notify, {column_query}
                        from {AnalyticsDB.get_replenishment_variants_table()} rp left join po_reorders po ON rp.sku = po.sku
                        left join variants v ON rp.sku = v.variants_sku
                        left join replenishment_reserved_variants rrv ON rp.variant_id = rrv.variant_id
                        left join replenishment_backorder_variants rbv ON rp.sku = rbv.child_sku
                        left join replenishment_safety_stock rss ON rp.sku = rss.child_sku
                        left join replenishment_classifications rc ON rp.sku = rc.sku
                        left join product_tags pt ON rp.sku = pt.variant_sku
                        left join skuvault_catalog sv ON sv.sku = rp.sku
                        where (pt.tag_id IN ({formatted_values}) AND pt.variant_sku IS NOT NULL) AND rp.parent_sku IN ({parent_sku_str}) {append_query} GROUP BY rp.product_title, rp.sku, rp.parent_sku, sv.primary_supplier, sv.classification, rc.new_classification, sv.brand, rp.cost, rp.retail_price, rp.sale_price, 
                        rp.reorder_point, rp.incremental_quantity, rp.quantity_on_hand, rp.quantity_pending, rp.quantity_incoming, rp.quantity_available, rp.quantity_on_hold, rp.suggested_order_qty_45, rp.suggested_order_qty_60, po.quantity, rp.month_1, rbv.qty, rss.qty, sv.attribute4_value, rp.out_of_stock_end_date, rp.total_out_of_stock_days, rp.restocked_inventory_level, rp.out_of_stock_start_date, rp.oldest_expiry_date, rp.total_instock_notify, {column_query}   
                        ORDER BY rp.product_title ASC """
            else:
                base_query = f"""
                            select rp.product_title, rp.sku, rp.parent_sku, sv.primary_supplier, COALESCE(rc.new_classification, sv.classification) AS classification, sv.brand, rp.cost, rp.retail_price, rp.sale_price, rp.reorder_point, rp.incremental_quantity,
                            rp.quantity_on_hand, rp.quantity_pending, rp.quantity_incoming, rp.quantity_available, rp.quantity_on_hold,
                            rp.suggested_order_qty_45, rp.suggested_order_qty_60, CASE WHEN po.quantity is NULL THEN 0 ELSE po.quantity END as item_order_qty, max(v.out_of_stock_date), rp.month_1, sum(rrv.quantity) as reserved_quantity, rbv.qty, rss.qty, sv.attribute4_value, rp.out_of_stock_end_date AS last_received_date, rp.total_out_of_stock_days AS total_out_of_stock_days, rp.restocked_inventory_level, rp.out_of_stock_start_date, rp.oldest_expiry_date AS expiry_date, 
                            MAX(rp.rtv_quantity) AS rtv_quantity, MAX(rp.order_quantity) AS zero_quantity, rp.total_instock_notify, {column_query}
                            from {AnalyticsDB.get_replenishment_variants_table()} rp left join po_reorders po ON rp.sku = po.sku
                            left join variants v ON rp.sku = v.variants_sku
                            left join replenishment_reserved_variants rrv ON rp.variant_id = rrv.variant_id
                            left join replenishment_backorder_variants rbv ON rp.sku = rbv.child_sku
                            left join replenishment_safety_stock rss ON rp.sku = rss.child_sku
                            left join replenishment_classifications rc ON rp.sku = rc.sku
                            left join skuvault_catalog sv ON sv.sku = rp.sku
                            where rp.parent_sku IN ({parent_sku_str}) {append_query} GROUP BY rp.product_title, rp.sku, rp.parent_sku, sv.primary_supplier, sv.classification, rc.new_classification, sv.brand, rp.cost, rp.retail_price, rp.sale_price, 
                            rp.reorder_point, rp.incremental_quantity, rp.quantity_on_hand, rp.quantity_pending, rp.quantity_incoming, rp.quantity_available, rp.quantity_on_hold, rp.suggested_order_qty_45, rp.suggested_order_qty_60, po.quantity, rp.month_1, rbv.qty, rss.qty, sv.attribute4_value, rp.out_of_stock_end_date, rp.total_out_of_stock_days, rp.restocked_inventory_level, rp.out_of_stock_start_date, rp.oldest_expiry_date, rp.total_instock_notify, {column_query}   
                            ORDER BY rp.product_title ASC                                              
                            """
            query_result = conn.execute(text(base_query))
            query_result = list(query_result)
        
        formatted_data = {} 
        reserved_qty_data = {}  
        item_order_qty_data = {}  
        backorder_qty_data = {}  
        safetystock_qty_data = {}
        total_cost_data = {}    
        case_qty_data = {}            
        final_result = {
            'data': [],
            'reserved_qty_data': [],
            'item_order_qty_data': [],
            'backorder_qty_data': [],
            'safetystock_qty_data': [],
            'case_qty_data': [],
            'meta': {}
        }
        if query_result:
            child_skus = [data[1] for data in query_result]
            latest_po_dates = _fetch_latest_po_date(store['id'], child_skus)
            for data in query_result:
                obj = {}                                                   

                obj["Title"] = data[0]
                obj["Item Search Term"] = data[1]
                obj["Parent Sku"] = data[2]                
                obj["Classification"] = data[4]
                obj["Supplier Company Name"] = data[3]
                obj["Brand"] = data[5]
                obj["Item Cost"] = float(data[6])
                obj["Retail Price"] = float(data[7])
                obj["Sale Price"] = float(data[8])
                # obj["reorder_point"] = float(data[9])
                obj["Incremental Quantity"] = int(data[10])
                obj["Case Quantity"] = data[24] if data[24] != "" else "-"
                obj["Quantity On Hand"] = int(data[11])
                obj["Quantity Pending"] = int(data[12])
                obj["Quantity Incoming"] = int(data[13])
                obj["Quantity Available"] = int(data[14])
                obj["Quantity On Hold"] = int(data[15])                                           
                obj["Suggested Order Qty 45"] = int(data[16])
                obj["Suggested Order Qty 60"] = int(data[17])                
                obj["Item Order Qty"] = int(data[18])
                
                # Handle Last Out Of Stock Date with priority: data[19], fallback to data[28]
                if isinstance(data[19], datetime.datetime) and data[19] is not None:
                    obj["Last Out Of Stock Date"] = data[19].strftime("%Y-%m-%d")
                elif isinstance(data[28], datetime.datetime) and data[28] is not None:
                    obj["Last Out Of Stock Date"] = data[28].strftime("%Y-%m-%d")
                else:
                    obj["Last Out Of Stock Date"] = str(data[28])

                obj[month_names['month_1']] = int(data[20]) if data[20] != None else 'NA' 
                obj["Reserved Quantity"] = int(data[21]) if data[21] != None else 0 
                obj["Backorder Quantity"] = int(data[22]) if data[22] != None else 0 
                obj["Safetystock Quantity"] = int(data[23]) if data[23] != None else 0
                
                obj["Last Received Date"] = data[25].strftime("%Y-%m-%d") if isinstance(data[25], datetime.datetime) else str(data[25])
                obj["Total Out Of Stock Occurrence"] = int(data[26]) if data[26] != None else 0
                obj["Restocked Inventory Level"] = int(data[27]) if data[27] != None else 0
                
                # oldest_expiry_date conversion with null/empty check
                obj["Expiry Date"] = (
                    datetime.datetime.strptime(data[29], "%Y-%m").strftime("%b-%Y").upper()
                    if data[29] else None
                )
                
                obj["Total RTV Quantity"] = data[30]
                obj["Total Return Quantity"] = data[31]
                obj["Total Instock Notify"] = data[32]

                obj["Item Ordered Quantity"] = int(data[33])
                obj["Total Sold 30"] = int(data[34]) if data[34] != None else 0   
                obj["Suggested Order Qty"] = int(data[35])  
                # Check if out_of_stock_date is present and calculate out_of_stock_days
                out_of_stock_date = data[19]
                if out_of_stock_date:
                    if isinstance(out_of_stock_date, datetime.datetime):
                        days_out_of_stock = (datetime.datetime.now() - out_of_stock_date).days
                    else:
                        days_out_of_stock = 0  # Handle cases where the date is invalid or cannot be parsed
                else:
                    days_out_of_stock = 0  # Set to 0 if no out_of_stock_date
                obj["Days Out Of Stock"] = days_out_of_stock

                obj["Total Cost"] = float(data[6]) * int(data[18])            

                count = 1  
                total_sum = 0                                                           
                for i in range(int(sale_history_months), 0, -1):
                    month_index = i + 1
                    key = "month_" + str(month_index)
                    value_key = 35 + count  
                    if key == 'month_1':
                        continue
                    obj[month_names[key]] = int(data[value_key]) if data[value_key] != None else 'NA'
                    total_sum = total_sum + int(data[value_key]) if data[value_key] != None else 0
                    count = count + 1      
                                
                turn_rate = (((int(total_sum) / int(data[14]) if int(data[14]) != 0 else 1) * 365) / day_difference)                           
                weeks_on_hand = 52 / turn_rate if turn_rate != 0 else 1
                obj["Total Sold"] = total_sum 
                obj["Turn Rate"] = float(turn_rate)
                obj["Weeks On Hand"] = float(weeks_on_hand)
                obj["Days To Replenishment"] = days_to_replenishment
                obj['Sale History Months'] = sale_history_months

                central_tz = pytz.timezone('America/Chicago')

                latest_po_date = latest_po_dates.get(data[1], None)
                if latest_po_date:
                    if latest_po_date.tzinfo is None:
                        latest_po_date = pytz.utc.localize(latest_po_date)

                    # Convert to Central Time
                    local_latest_po_date = latest_po_date.astimezone(central_tz)

                    obj["Latest PO Date"] = local_latest_po_date.strftime("%Y-%m-%d")
                else:
                    obj["Latest PO Date"] = None

                reserved_qty = int(data[21]) if data[21] != None else 0   
                backorder_qty = int(data[22]) if data[22] != None else 0
                safetystock_qty = int(data[23]) if data[23] != None else 0   
                total_cost = obj['Total Cost'] if obj['Total Cost'] != None else 0
                case_qty = int(data[24]) if str(data[24]).isdigit() else 0                                                                                     
                if data[2] in formatted_data:
                    if formatted_data[data[2]] is not None:
                        formatted_data[data[2]].append(obj)
                        reserved_qty_data[data[2]] = reserved_qty_data[data[2]] + int(reserved_qty)
                        item_order_qty_data[data[2]] = item_order_qty_data[data[2]] + int(data[18])
                        backorder_qty_data[data[2]] = backorder_qty_data[data[2]] + int(backorder_qty)
                        safetystock_qty_data[data[2]] = safetystock_qty_data[data[2]] + int(safetystock_qty)
                        total_cost_data[data[2]] = total_cost_data[data[2]] + total_cost
                        case_qty_data[data[2]] = case_qty_data[data[2]] + int(case_qty)
                    else:
                        formatted_data[data[2]] = [obj]
                        reserved_qty_data[data[2]] = reserved_qty_data[data[2]] + int(reserved_qty) 
                        item_order_qty_data[data[2]] = item_order_qty_data[data[2]] + int(data[18])
                        backorder_qty_data[data[2]] = backorder_qty_data[data[2]] + int(backorder_qty)
                        safetystock_qty_data[data[2]] = safetystock_qty_data[data[2]] + int(safetystock_qty)
                        total_cost_data[data[2]] = total_cost_data[data[2]] + total_cost
                        case_qty_data[data[2]] = case_qty_data[data[2]] + int(case_qty)
                else:
                    formatted_data[data[2]] = [obj]         
                    reserved_qty_data[data[2]] = int(reserved_qty)  
                    item_order_qty_data[data[2]] = int(data[18])          
                    backorder_qty_data[data[2]] = int(backorder_qty)
                    safetystock_qty_data[data[2]] = int(safetystock_qty)  
                    total_cost_data[data[2]] = total_cost 
                    case_qty_data[data[2]] = int(case_qty) 
            final_result['data'] = formatted_data 
            final_result['reserved_qty_data'] = reserved_qty_data
            final_result['item_order_qty_data'] = item_order_qty_data
            final_result['backorder_qty_data'] = backorder_qty_data
            final_result['safetystock_qty_data'] = safetystock_qty_data
            final_result['total_cost_data'] = total_cost_data
            final_result['case_qty_data'] = case_qty_data
            final_result['meta'] = month_names      

            response['data'] = final_result
            response['status'] = 200           
    finally:
        conn.close()
    
    return response

def _fetch_latest_po_date(store_id, skus):
    db = get_store_db_client_for_store_id(store_id)
    latest_po_dates = {}
    try:
        # Fetch only documents for the provided SKUs
        cursor = db["skuvault_pos"].find(
            {"_id": {"$in": skus}},
            {"pos": 1}
        )

        for doc in cursor:
            sku = doc["_id"]
            pos_entries = doc.get("pos", {})
            if not pos_entries:
                continue

            latest_date = None
            for po_info in pos_entries.values():
                created_at_str = po_info.get("created_at")
                if created_at_str:
                    try:
                        created_at = parser.isoparse(created_at_str)    
                        if not latest_date or created_at > latest_date:
                            latest_date = created_at
                    except Exception as e:
                        logger.warning(f"Exception for SKU {sku}, created_at: {created_at_str}")
                        logger.warning(traceback.format_exc())

            if latest_date:
                latest_po_dates[sku] = latest_date
        return latest_po_dates

    except Exception as e:
        logger.error(traceback.format_exc())
        return None


def get_replenishment_daily_sold_aggregate_data_csv(store_id, query_params, call_from_time_trigger=False):
    response = {
        "status": 400,
    }
    conn = db.get_connection(store_id)
    try:
        query_params['username'] = '<EMAIL>' if call_from_time_trigger else query_params['username']
        user_details = store_db.fetch_user_by_username(store_id, query_params['username'])
        user_name = user_details['name'] if 'name' in user_details and user_details else ''
        query_brand = query_params.get('brand','').strip()
        query_classification = query_params.get('classification','').strip()
        query_tags = query_params.get('tags','').strip()
        hide_discontinued = query_params.get('hide_discontinued', 'true')    
        show_zero_qty_products = query_params.get('show_zero_qty_products', 'true')    
        query_primary_supplier = query_params.get('primary_supplier', None) 
        sort_by = query_params.get('sort_by','').strip()
        sort_array = sort_by.split("/") if sort_by != '' else []
        search_value = query_params.get('searchValue', None)
        no_sold_days = query_params.get('no_sold_days', False) 
        if query_primary_supplier and query_primary_supplier.strip() != '':
            query_primary_supplier = query_primary_supplier.split(";")
        else:
            query_primary_supplier = []   
        user = query_params.get('user', None)

        # Get current day of month
        today = datetime.datetime.now().day
        month = datetime.datetime.now().strftime("%b")

        # Dynamically create SQL part for days
        day_columns = []
        for i in range(1, today + 1):
            day_columns.append(f"rp.day_{i}")

        day_columns_query = ", ".join(day_columns)

        append_query = ''
        join_query = ''
        conditions = []
        join_conditions = []
        params = {}
        
        if query_brand != '':
            values_list = [value.strip() for value in query_brand.split(';')]
            conditions.append("sv.brand = ANY(:brands)")
            params['brands'] = values_list

        if query_classification != '':
            values_list = [value.strip() for value in query_classification.split(';')]
            conditions.append("COALESCE(rc.new_classification, sv.classification) = ANY(:classifications)")
            params['classifications'] = values_list

        if query_tags != '':
            value_list = [value.strip() for value in query_tags.split(',')]    
            formatted_values = "','".join(value_list)
            formatted_values = f"'{formatted_values}'"
            conditions.append(f"pt.tag_id IN ({formatted_values})")
            join_conditions.append(" left join product_tags pt ON rp.parent_sku = pt.sku ")
            

        if search_value and search_value != '':
            conditions.append(" (rp.product_title ILIKE :search or rp.parent_sku ILIKE :search) ")
            params['search'] = '%' + search_value + '%'

        if not call_from_time_trigger:
            if hide_discontinued == 'true':        
                conditions.append(" ((LOWER(rp.product_title) NOT ILIKE '%discontinued%') AND (LOWER(rp.product_title) NOT ILIKE '%discountinued%')) AND ((LOWER(sv.classification) NOT ILIKE '%discontinued%') AND (LOWER(sv.classification) NOT ILIKE '%discountinued%'))")

            if show_zero_qty_products == 'false':
                conditions.append("rp.quantity_available > 0")

            if no_sold_days == 'true':
                conditions.append("rp.total_month_quantity = 0")

        order_by = ""
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'     
            nulls_order = "NULLS FIRST" if sort_direction.lower() == "asc" else "NULLS LAST"   
            order_by = " ORDER BY " + sort_array[0] + " " + sort_direction + " " + nulls_order

        day_names = common_util.get_day_array_for_meta()    
    
        if len(query_primary_supplier) == 0 and user:
            query = f"SELECT suppliers FROM user_supplier_mapping where user_name='{user.strip()}'"
            rs = conn.execute(text(query))

            for _supplier in rs:
                query_primary_supplier.append(_supplier[0])
            if len(query_primary_supplier) == 0:
                response['message'] = 'Not found'
                response['data'] = {'data':[]}
                response['status'] = 200
                return response 

        if len(query_primary_supplier):
            conditions.append("sv.primary_supplier = ANY(:suppliers)")
            params['suppliers'] = query_primary_supplier

        if conditions:
            append_query = ' AND (' + ' AND '.join(conditions) + ')'
        
        if join_conditions:
            join_query = ' ' + ' '.join(join_conditions) + ' '

        base_query = f"""WITH consolidated_skuvault AS (
                            SELECT DISTINCT primary_supplier, classification, max(brand) as brand, parent_sku
                            FROM skuvault_catalog GROUP BY parent_sku, primary_supplier, classification
                        )
                        select MAX(rp.product_title) AS product_title, rp.parent_sku AS parent_sku,
                        sum(rp.quantity_incoming) AS quantity_incoming, sum(rp.quantity_available)  AS quantity_available, sum(rp.quantity_on_hold) AS quantity_on_hold, rp.total_month_quantity AS total_month_quantity, {day_columns_query}
                        from {AnalyticsDB.get_current_month_daily_sales_table()} rp {join_query} left join replenishment_classifications rc ON rp.parent_sku = rc.sku 
                        left join consolidated_skuvault sv ON sv.parent_sku = rp.parent_sku 
                        where sv.primary_supplier <> 'Unknown' {append_query}              
                        group by rp.parent_sku, sv.primary_supplier, sv.classification, rc.new_classification, rp.total_month_quantity, {day_columns_query}
                        {order_by}"""               

        final_query = base_query
        query_result = conn.execute(text(final_query), params)

        formated_data = [] 
        parent_sku_str = ''                     
        if query_result:         
            for data in query_result:
                obj = {}   
                parent_sku_str = parent_sku_str + "'" + str(data[1]) + "', " 
                   
                obj["PRODUCT TITLE"] = data[0] 
                obj["PARENT SKU"] = data[1]  
                obj["SKU"] = ''
                obj['BLOCKED QTY'] = 0
                obj["AVAILABLE"] = int(data[3]) 
                obj["INCOMING"] = int(data[2])
                count = 1
                for i in range(1, today + 1):
                    day_key = str(i).zfill(2) + " " + month
                    value_key = 5 + count 
                    day_value = int(data[value_key]) if data[value_key] is not None else 0
                    obj[day_key] = day_value
                    count += 1 

                obj["TOTAL"] = int(data[5]) if data[5] is not None else 0
                obj['child_data'] = []

                formated_data.append(obj)  

            parent_sku_str = parent_sku_str.rstrip(', ')
            
            child_data_array = get_on_load_daily_sold_child_data(conn, parent_sku_str, '', hide_discontinued, query_tags, params)   
            
            if child_data_array['status'] == 200:
                child_array = child_data_array['data']['data']
                reserved_qty_array = child_data_array['data']['reserved_qty_data']
                parent_sku_list = [sku.strip().strip("'") for sku in parent_sku_str.split(',')]
                
                for p_sku in parent_sku_list:                                        
                    for item in formated_data:                        
                        if p_sku == item.get('PARENT SKU'):     
                            item['BLOCKED QTY'] = reserved_qty_array.get(p_sku, 0) 
                            item['child_data'] = child_array.get(p_sku, {})
                            break   
            
            # Create CSV content in memory
            if formated_data:
                utc_now = datetime.datetime.now(pytz.utc)
                cst_now = utc_now.astimezone(pytz.timezone('America/Chicago'))
                created_date = cst_now .strftime("%m-%d-%Y_%H:%M")
                zip_filename = f"Replenishment_Daily_Sold_Product_report_{created_date}.zip"

                csv_content = io.StringIO()
                keys = list(formated_data[0].keys())
                if 'child_data' in keys:  # Check if 'child_data' exists
                    keys.remove('child_data')
                writer = csv.DictWriter(csv_content, fieldnames=keys)
                writer.writeheader()
                for row in formated_data:
                    if 'child_data' in row:
                        for child in row['child_data']:
                            writer.writerow(child)
                        del row['child_data']
                    writer.writerow(row)  

                # Compress CSV into ZIP
                zip_buffer = compress_csv(csv_content, zip_filename)

                email_util.daily_sales_replenishment_csv_mail(store_id, zip_buffer, query_params['username'], zip_filename, user_name, created_date, call_from_time_trigger)
    except Exception as e:
        logger.error(traceback.format_exc())
    finally:
        if conn:
            conn.close()

def get_on_load_daily_sold_child_data(conn, parent_sku_str, append_query, hide_discontinued, query_tags, params):
    response = {
        "status": 400,
        "data": {}
    }
    try:
        # Get current day of month
        today = datetime.datetime.now().day
        month = datetime.datetime.now().strftime("%b")

        # Dynamically create SQL part for days
        day_columns = []
        for i in range(1, today + 1):
            day_columns.append(f"SUM(rp.day_{i}) as day_{i}")

        day_columns_query = ", ".join(day_columns)

        day_names = common_util.get_day_array_for_meta()

        join_query = ''
        if query_tags != '':
            value_list = [value.strip() for value in query_tags.split(',')]    
            formatted_values = "','".join(value_list)
            formatted_values = f"'{formatted_values}'"
            append_query = f" AND (pt.tag_id IN ({formatted_values}) AND pt.variant_sku IS NOT NULL)"
            join_query = join_query + " left join product_tags pt ON rp.sku = pt.variant_sku "

        query_result = None
        if parent_sku_str != '':
            base_query = f"""
                            select rp.product_title, rp.sku, rp.parent_sku,
                            rp.quantity_incoming, rp.quantity_available, rp.quantity_on_hold, rp.total_month_quantity, sum(rrv.quantity) as reserved_quantity, {day_columns_query}
                            from {AnalyticsDB.get_current_month_daily_sales_variants_table()} rp
                            left join replenishment_reserved_variants rrv ON rp.sku = rrv.sku
                            {join_query}
                            where rp.parent_sku IN ({parent_sku_str}) {append_query} GROUP BY rp.product_title, rp.sku, rp.parent_sku, rp.total_month_quantity, 
                            rp.quantity_incoming, rp.quantity_available, rp.quantity_on_hold
                            ORDER BY rp.product_title ASC                                              
                            """
            query_result = conn.execute(text(base_query), params)
        
            formatted_data = {} 
            reserved_qty_data = {}
            final_result = {
                'data': [],
                'reserved_qty_data': [],
                'meta': {}
            }
            if query_result:
                for data in query_result:
                    obj = {}                                                   
                    obj["PRODUCT TITLE"] = data[0]
                    obj["PARENT SKU"] = data[2]
                    obj["SKU"] = data[1]  
                    obj["BLOCKED QTY"] = int(data[7]) if data[7] != None else 0 
                    obj["AVAILABLE"] = int(data[4])
                    obj["INCOMING"] = int(data[3])
                    count = 1
                    for i in range(1, today + 1):
                        day_key = str(i).zfill(2) + " " + month
                        value_key = 7 + count
                        day_value = int(data[value_key]) if data[value_key] is not None else 0
                        obj[day_key] = day_value
                        count += 1

                    obj["TOTAL"] = int(data[6]) if data[6] is not None else 0 
                    reserved_qty = int(data[7]) if data[7] != None else 0                                      
                    if data[2] in formatted_data:
                        if formatted_data[data[2]] is not None:
                            formatted_data[data[2]].append(obj)
                            reserved_qty_data[data[2]] = reserved_qty_data[data[2]] + int(reserved_qty)
                        else:
                            formatted_data[data[2]] = [obj]
                            reserved_qty_data[data[2]] = reserved_qty_data[data[2]] + int(reserved_qty)
                    else:
                        formatted_data[data[2]] = [obj]         
                        reserved_qty_data[data[2]] = int(reserved_qty)                                                                              
            
                final_result['data'] = formatted_data 
                final_result['reserved_qty_data'] = reserved_qty_data
                final_result['meta'] = day_names
            
                response['data'] = final_result
                response['status'] = 200           
    except Exception as e:
        print(e)
        response['message'] = 'Something went wrong'
        response['status'] = 500
    
    return response

def get_replenishment_dashboard_aggregate_data_csv(store_id, query_params):
    response = {
        "status": 400,
    }
    store = store_util.get_store_by_id(store_id)
    user_details = store_db.fetch_user_by_username(store_id, query_params['username'])
    user_name = user_details['name'] if 'name' in user_details and user_details else ''
    query_brand = query_params.get('brand','').strip()
    query_classification = query_params.get('classification','').strip()
    query_tags = query_params.get('tags','').strip()
    hide_discontinued = query_params.get('hide_discontinued', 'true')    
    show_zero_qty_products = query_params.get('show_zero_qty_products', 'true')    
    query_primary_supplier = query_params.get('primary_supplier', None) 
    sort_by = query_params.get('sort_by','').strip()
    sort_array = []
    if sort_by != 'ratio/1' and sort_by != 'ratio/-1' and sort_by != 'total_cost/1' and sort_by != 'total_cost/-1':
        sort_array = sort_by.split("/") if sort_by != '' else []
    
    search_key = query_params.get('searchKey', '').strip()
    search_value = query_params.get('searchValue', None)
    filtered_skus = query_params.get('filtered_skus','').strip()
    ratio_filter = query_params.get('ratio_filter', None)
    year = query_params.get('year', None)
    if query_primary_supplier and query_primary_supplier.strip() != '':
        query_primary_supplier = query_primary_supplier.split(";")
    else:
        query_primary_supplier = []   
    user = query_params.get('user', None)
    sale_history_months = query_params.get('sale_history_months', 6)
    days_to_replenishment = query_params.get('days_to_replenishment', 30)      
    no_sold_days = query_params.get('no_sold_days', False)
    classified_as = query_params.get('classified_as', None)  

    price_list_data = {}

    column_query = ''  
    
    if days_to_replenishment:
        column_query = column_query + 'sum(rp.to_order_qty_' + str(days_to_replenishment) + ')'
        column_query = column_query + ', sum(rp.total_sold_' + str(days_to_replenishment) + ')'
        column_query = column_query + ', sum(rp.suggested_order_qty_' + str(days_to_replenishment) + ') AS suggested_order_qty'        

    if sale_history_months:
        for i in range(int(sale_history_months), 0, -1):
            index = i + 1
            column_query = column_query + ', sum(rp.month_' + str(index) + ')'                   

    append_query = ''
    
    if query_brand != '':
        values_list = query_brand.split(';')
        trimmed_values_list = [value.strip()
                                for value in values_list]  # Trim each value 
        formatted_values = "','".join(trimmed_values_list)
        formatted_values = f"'{formatted_values}'"
        append_query = ' AND sv.brand IN(' + formatted_values + ')'

    if query_classification != '':
        values_list = query_classification.split(';')
        trimmed_values_list = [value.strip() for value in values_list]  # Trim spaces

        # Escape single quotes inside classification names if necessary
        escaped_values = [v.replace("'", "''") for v in trimmed_values_list]

        # Build a string like: 'value1','value2','value3'
        formatted_values = "','".join(escaped_values)

        # Build the SQL array literal with type cast
        append_query += f"""
        AND (
            ca.classifications && ARRAY['{formatted_values}']::varchar[]
        )
        """
        # append_query = append_query + ' AND rp.classification IN(' + \
        #     formatted_values + ')'

    if year and year != None:
        append_query = append_query + f" AND EXTRACT(YEAR FROM p.date_created) = {int(year)}"

    if user and user.strip() != '':
        append_query = append_query + " AND usm.user_name ILIKE '%" + user.strip() + "%'"
   
    if search_value and search_value != '':
        append_query = append_query + " AND  (rp.product_title ILIKE '%" + search_value + "%' or rp.parent_sku ILIKE '%" + search_value + "%') "        

    if hide_discontinued == 'true':        
        append_query = append_query + " AND ((LOWER(rp.product_title) NOT ILIKE '%discontinued%') AND (LOWER(rp.product_title) NOT ILIKE '%discountinued%')) AND ((LOWER(sv.classification) NOT ILIKE '%discontinued%') AND (LOWER(sv.classification) NOT ILIKE '%discountinued%'))"
    
    if show_zero_qty_products == 'false':
        append_query = append_query + " AND rp.quantity_available > 0"
    
    if no_sold_days == 'true':
        append_query = append_query + " AND rp.total_sold_" + str(days_to_replenishment) + " = 0"
    
    append_conditions = []
    if classified_as:
        parts = [part.strip() for part in classified_as.split(",")]
        ids = [int(part) for part in parts if part.isdigit()]
        
        if "Unassigned" in parts:
            append_conditions.append("rd.classified_as_id IS NULL")

        if ids:
            placeholders = ','.join(map(str, ids))
            append_conditions.append(f"rd.classified_as_id IN ({placeholders})")

        if append_conditions:
            append_query += " AND (" + " OR ".join(append_conditions) + ")"

    if filtered_skus != '':
        p_skus = filtered_skus.split(',')
        p_skus_list = [value.strip() for value in p_skus]  # Trim each value
        p_skus_list_values = "','".join(p_skus_list)
        append_query = append_query + f" AND rp.parent_sku IN ('{p_skus_list_values}')"

    order_by = ""
    if len(sort_array):
        sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
        if sort_array[0] in ["created_at"]:                
            order_by += f" ORDER BY {sort_array[0]} {sort_direction}" 
        else:       
            order_by = " ORDER BY " + sort_array[0] + " " + sort_direction

    month_names, day_difference = common_util.get_month_array_for_meta(sale_history_months)   
    conn = db.get_connection(store['id'])
    try:
        if len(query_primary_supplier) == 0 and user:
            query = f"SELECT suppliers FROM public.user_supplier_mapping where user_name ILIKE '{user.strip()}'"
            rs = conn.execute(text(query))
            for _supplier in rs:
                query_primary_supplier.append(_supplier[0])
            if len(query_primary_supplier) == 0:
                response['message'] = 'Supplier Not found'
                response['data'] = {'data':[]}
                response['status'] = 200
                return response 
                           
        if len(query_primary_supplier):
            append_query = append_query + ' AND sv.primary_supplier = ANY(:suppliers)'

                 
        # ...
        base_query = ''
        if query_tags != '':
            values_list = query_tags.split(',')
            trimmed_values_list = [value.strip() for value in values_list]  # Trim each value
            formatted_values = "','".join(trimmed_values_list)
            formatted_values = f"'{formatted_values}'"
            base_query = f"""WITH consolidated_skuvault AS (
                                SELECT DISTINCT primary_supplier, classification, max(brand) as brand, parent_sku
                                FROM skuvault_catalog GROUP BY parent_sku, primary_supplier, classification
                            ),
                            classifications_agg AS (
                                SELECT
                                    sc.parent_sku,
                                        ARRAY_AGG(DISTINCT COALESCE(rc.new_classification, sc.classification)) AS classifications,
                                        ARRAY_AGG(DISTINCT sc.primary_supplier) AS primary_suppliers,
                                        ARRAY_AGG(DISTINCT usm.user_name ORDER BY usm.user_name) AS purchasers
                                FROM skuvault_catalog sc
                                LEFT JOIN replenishment_classifications rc ON sc.sku = rc.sku
                                LEFT JOIN user_supplier_mapping usm ON sc.primary_supplier = usm.suppliers
                                WHERE sc.parent_sku IS NOT NULL
                                GROUP BY sc.parent_sku
                            )
                            select MAX(rp.product_title) AS product_title, MAX(rp.product_id) AS product_id, rp.parent_sku, ca.primary_suppliers, ca.classifications, MAX(sv.brand) AS brand, MAX(rp.cost) AS cost, MAX(rp.retail_price) AS retail_price, MAX(rp.sale_price) AS sale_price, MAX(rp.reorder_point) AS reorder_point, MAX(rp.incremental_quantity) AS incremental_quantity,
                            sum(DISTINCT rp.quantity_on_hand), sum(DISTINCT rp.quantity_pending), sum(DISTINCT rp.quantity_incoming), sum(DISTINCT rp.quantity_available), sum(DISTINCT rp.quantity_on_hold), 
                            sum(DISTINCT rp.suggested_order_qty_45) AS suggested_order_qty_45, sum(DISTINCT rp.suggested_order_qty_60) AS suggested_order_qty_60, sum(DISTINCT rp.month_1), p.out_of_stock_date, p.date_created AS created_at, 
                            rd.classified_as_id AS classified_as, 
                            COALESCE(rd.terms_consignment, NULL) AS terms_consignment, 
                            COALESCE(rd.action_taken, NULL) AS action_taken,
                            ca.purchasers,
                            p.price AS wholesale_price,
                            {column_query}
                            from {AnalyticsDB.get_replenishment_products_table()} rp left join product_tags pt ON rp.parent_sku = pt.sku left join products p ON rp.parent_sku = p.sku left join replenishment_dashboard rd ON rp.parent_sku = rd.parent_sku 
                            left join consolidated_skuvault sv ON sv.parent_sku = rp.parent_sku left join user_supplier_mapping usm ON sv.primary_supplier = usm.suppliers left join classifications_agg ca ON rp.parent_sku = ca.parent_sku
                            where pt.tag_id IN ({formatted_values}) AND (pt.variant_sku IS NULL OR pt.variant_sku = pt.sku) AND sv.primary_supplier <> 'Unknown' AND rp.parent_sku IS NOT NULL AND rp.parent_sku != '' {append_query}              
                            group by rp.parent_sku, ca.primary_suppliers, ca.classifications, p.out_of_stock_date, p.date_created, rd.classified_as_id, rd.terms_consignment, rd.action_taken, ca.purchasers, p.price
                            {order_by}"""            
        else: 
            base_query = f"""WITH consolidated_skuvault AS (
                                SELECT DISTINCT primary_supplier, classification, max(brand) as brand, parent_sku
                                FROM skuvault_catalog GROUP BY parent_sku, primary_supplier, classification
                            ),
                            classifications_agg AS (
                                SELECT
                                    sc.parent_sku,
                                        ARRAY_AGG(DISTINCT COALESCE(rc.new_classification, sc.classification)) AS classifications,
                                        ARRAY_AGG(DISTINCT sc.primary_supplier) AS primary_suppliers,
                                        ARRAY_AGG(DISTINCT usm.user_name ORDER BY usm.user_name) AS purchasers
                                FROM skuvault_catalog sc
                                LEFT JOIN replenishment_classifications rc ON sc.sku = rc.sku
                                LEFT JOIN user_supplier_mapping usm ON sc.primary_supplier = usm.suppliers
                                WHERE sc.parent_sku IS NOT NULL
                                GROUP BY sc.parent_sku
                            )
                            select MAX(rp.product_title) AS product_title, MAX(rp.product_id) AS product_id, rp.parent_sku, ca.primary_suppliers, ca.classifications, MAX(sv.brand) AS brand, MAX(rp.cost) AS cost, MAX(rp.retail_price) AS retail_price, MAX(rp.sale_price) AS sale_price, MAX(rp.reorder_point) AS reorder_point, MAX(rp.incremental_quantity) AS incremental_quantity,
                            sum(DISTINCT rp.quantity_on_hand), sum(DISTINCT rp.quantity_pending), sum(DISTINCT rp.quantity_incoming), sum(DISTINCT rp.quantity_available), sum(DISTINCT rp.quantity_on_hold), 
                            sum(DISTINCT rp.suggested_order_qty_45) AS suggested_order_qty_45, sum(DISTINCT rp.suggested_order_qty_60) AS suggested_order_qty_60, sum(DISTINCT rp.month_1), p.out_of_stock_date, p.date_created AS created_at,
                            rd.classified_as_id AS classified_as, 
                            COALESCE(rd.terms_consignment, NULL) AS terms_consignment, 
                            COALESCE(rd.action_taken, NULL) AS action_taken,
                            ca.purchasers,
                            p.price AS wholesale_price,
                            {column_query}
                            from {AnalyticsDB.get_replenishment_products_table()} rp left join replenishment_dashboard rd ON rp.parent_sku = rd.parent_sku left join products p ON rp.parent_sku = p.sku 
                            left join consolidated_skuvault sv ON sv.parent_sku = rp.parent_sku left join user_supplier_mapping usm ON sv.primary_supplier = usm.suppliers left join classifications_agg ca ON rp.parent_sku = ca.parent_sku where sv.primary_supplier <> 'Unknown' AND rp.parent_sku IS NOT NULL AND rp.parent_sku != '' {append_query}         
                            group by rp.parent_sku, ca.primary_suppliers, ca.classifications, p.out_of_stock_date, p.date_created, rd.classified_as_id, rd.terms_consignment, rd.action_taken, ca.purchasers, p.price
                            {order_by}"""               
        final_query = base_query
        query_result = conn.execute(text(final_query), {"suppliers": query_primary_supplier})
        query_result = list(query_result)

        formated_data = [] 
        sku_count = 1
        parent_sku_str = ''                     
        if query_result:
            parent_skus = [data[2] for data in query_result]
            price_list_data = get_product_price_list_replenishment_dashboard(store, parent_skus)
            for data in query_result:
                obj = {}   
                if sku_count <= 10:
                    parent_sku_str = parent_sku_str + "'" + str(data[2]) + "', " 
                    sku_count = sku_count + 1           
                obj["Title"] = data[0]
                obj["Product Id"] = data[1]
                obj["Item Search Term"] = data[2]                
                obj["Supplier Company Name"] = ", ".join(data[3])
                obj["Purchaser Name"] = ", ".join(data[24])
                obj["Classification"] = ", ".join(data[4])
                obj["Cost"] = float(data[6])
                obj["Quantity On Hand"] = int(data[11])
                obj["Quantity Pending"] = int(data[12])
                obj["Quantity Incoming"] = int(data[13])
                obj["Quantity Available"] = int(data[14])
                obj["Quantity On Hold"] = int(data[15])                            
                obj["Total Sold 30"] = int(data[26]) if data[26] != None else 0 
                obj["Year"] = data[20].year if isinstance(data[20], datetime.datetime) else 0
                obj["Classified As"] = data[21]
                obj["Terms Consignment"] = data[22]
                obj["Action Taken"] = data[23]
                obj["Wholesale Price"] = data[25]

                out_of_stock_date = data[19]

                # Assuming `price_list_data` is the new structure you get from the function
                if price_list_data and isinstance(price_list_data, dict):
                    sku = data[2]  # Replace with the actual key that holds the SKU in your object
                    sku_price_data = price_list_data.get(sku)

                    if sku_price_data and "data" in sku_price_data and "meta" in sku_price_data:
                        meta_info = sku_price_data["meta"].get("price_lists", {})  # Get meta info for that SKU
                        price_list_entries = sku_price_data["data"][0].get("price_list", [])  # Assumes one data entry per SKU

                        # Flatten price_list_entries into a single dictionary for easy lookup
                        flattened_price_data = {}
                        for entry in price_list_entries:
                            flattened_price_data.update(entry)  # Merge dictionaries (price_1, price_2, etc.)

                        # Iterate through the meta price list definitions
                        for price_key, price_meta in meta_info.items():
                            price_name = price_meta.get("name", price_key)
                            price_value = flattened_price_data.get(price_key, '')  # Default to empty string if not found
                            obj[price_name] = price_value

                if out_of_stock_date:
                    if isinstance(out_of_stock_date, datetime.datetime):
                        # Make datetime.now() timezone-aware
                        now = datetime.datetime.now(timezone.utc) if out_of_stock_date.tzinfo else datetime.datetime.now()
                        days_out_of_stock = (now - out_of_stock_date).days
                    else:
                        days_out_of_stock = 0  # Handle cases where the date is invalid or cannot be parsed
                else:
                    days_out_of_stock = 0  # Set to 0 if no out_of_stock_date

                obj["Days Out Of Stock"] = days_out_of_stock             

                count = 1    
                total_sum = 0                                                   
                for i in range(int(sale_history_months), 0, -1):
                    month_index = i + 1
                    key = "month_" + str(month_index)
                    if key not in month_names:
                        continue
                    value_key = 28 + count
                    obj[month_names[key]] = int(data[value_key]) if data[value_key] != None else 'NA'
                    total_sum = total_sum + int(data[value_key]) if data[value_key] != None else 0
                    count = count+1 
                
                obj[month_names['month_1']] = int(data[18]) if data[18] != None else 'NA'
                turn_rate = (((int(total_sum) / int(data[14]) if int(data[14]) != 0 else 1) * 365) / day_difference)                           
                weeks_on_hand = 52 / turn_rate if turn_rate != 0 else 1 
                obj["Total Sold"] = total_sum 
                obj["Turn Rate"] = float(turn_rate)
                obj["Weeks On Hand"] = float(weeks_on_hand)
                obj["Days To Replenishment"] = days_to_replenishment
                obj['Sale History Months'] = sale_history_months
                # obj['child_data'] = []
                
                if ratio_filter:
                    count = 1
                    # Extract the month number from the ratio_filter (e.g., "month_5" -> 5)
                    try:
                        month_limit = int(ratio_filter.split('_')[1])  # Extract the number after 'month_'
                    except (IndexError, ValueError):
                        month_limit = 1  # Default to month_1 if ratio_filter is invalid

                    specified_months_sum = 0  # Initialize sum for the specified months

                    # Always include month_1 (which is at index 18)
                    if month_limit >= 1:
                        obj["month_1"] = int(data[18]) if data[18] is not None else 'NA'
                        specified_months_sum += obj["month_1"] if obj["month_1"] != 'NA' else 0

                    # Loop through the months from sale_history_months down to month_2, month_3, etc.
                    for i in range(int(sale_history_months), 0, -1):
                        month_index = i + 1  # Generate the month number (e.g., month_2, month_3)
                        key = "month_" + str(month_index)  # Form the key, e.g., "month_2"
                        value_key = 28 + count  # Adjust value_key based on the count

                        # Only sum the values if the current month is within the limit (e.g., month_2, month_3, etc.)
                        if month_index <= month_limit:
                            obj[key] = int(data[value_key]) if data[value_key] is not None else 'NA'
                            specified_months_sum += obj[key] if obj[key] != 'NA' else 0

                        count += 1  # Increment the count

                    # Calculate total quantity (available + incoming)
                    total_qty = obj["Quantity Available"] + obj["Quantity Incoming"]

                    # Calculate the ratio and update the object
                    if total_qty > 0:
                        ratio = (specified_months_sum / total_qty)
                        obj["Ratio"] = str(round(ratio, 2)) + '%'
                    else:
                        obj["Ratio"] = "0%"
                else:
                    obj["Ratio"] = "0%"


                   
                obj["Total Cost"] = int(data[14]) * float(data[6])

                formated_data.append(obj)


            exclude_keys = ['price_list_id'] + [f"month_{i}" for i in range(1, 13)]  # Exclude month_1, month_2, ..., month_12
            filtered_obj = {key: value for key, value in obj.items() if key not in exclude_keys}
            # Create CSV content in memory
            if formated_data:
                utc_now = datetime.datetime.now(pytz.utc)
                cst_now = utc_now.astimezone(pytz.timezone('America/Chicago'))
                created_date = cst_now .strftime("%m-%d-%Y_%H:%M")
                zip_filename = f"Replenishment_{created_date}.zip"
                csv_content = io.StringIO()
                keys = list(filtered_obj.keys())
                # if 'child_data' in keys:  # Check if 'child_data' exists
                #     keys.remove('child_data')
                for row in formated_data:
                    extra_keys = set(row.keys()) - set(keys)
                    for key in extra_keys:
                        del row[key]  # Remove any extra key not in fieldnames
                
                writer = csv.DictWriter(csv_content, fieldnames=keys)
                writer.writeheader()
                
                for row in formated_data:
                    writer.writerow(row)
                
                if conn:
                    conn.close()  

                # Compress CSV into ZIP
                zip_buffer = compress_csv(csv_content, zip_filename)

                email_util.send_replenishment_dashboard_csv_data_email(store_id, zip_buffer, query_params['username'], zip_filename, user_name, created_date)

    finally:
        if conn:
            conn.close()

def get_discontinued_products_data_csv(store_id, query_params):
    conn = db.get_connection(store_id)
    try:
        user_details = store_db.fetch_user_by_username(store_id, query_params['username'])
        user_name = user_details['name'] if 'name' in user_details and user_details else ''       
        # query_classification = query_params.get('classification','').strip()

        # Get current day of month
        today = datetime.datetime.now().day
        month = datetime.datetime.now().strftime("%b")

        # Dynamically create SQL part for days
        day_columns = []
        for i in range(1, today + 1):
            day_columns.append(f"rp.day_{i}")

        day_columns_query = ", ".join(day_columns)

        append_query = ''
        
        # if query_classification != '':
            # values_list = query_classification.split(';')
            # trimmed_values_list = [value.strip()
            #                         for value in values_list]  # Trim each value
            # formatted_values = "','".join(trimmed_values_list)
            # formatted_values = f"'{formatted_values}'"
        append_query += f"""AND (LOWER(COALESCE(rc.new_classification, sv.classification)) IN ('discontinued - prefilled disposables', 'prefilled disposables'))"""

        
        base_query = f"""WITH consolidated_skuvault AS (
                            SELECT DISTINCT primary_supplier, classification, max(brand) as brand, parent_sku
                            FROM skuvault_catalog GROUP BY parent_sku, primary_supplier, classification
                        )
                        select MAX(rp.product_title) AS product_title, rp.parent_sku AS parent_sku,
                        sum(rp.quantity_incoming) AS quantity_incoming, sum(rp.quantity_available)  AS quantity_available, sum(rp.quantity_on_hold) AS quantity_on_hold, rp.total_month_quantity AS total_month_quantity, {day_columns_query}
                        from {AnalyticsDB.get_current_month_daily_sales_table()} rp left join products p ON rp.parent_sku = p.sku left join replenishment_classifications rc ON rp.parent_sku = rc.sku 
                        left join consolidated_skuvault sv ON sv.parent_sku = rp.parent_sku 
                        where sv.primary_supplier <> 'Unknown' {append_query}              
                        group by rp.parent_sku, sv.primary_supplier, sv.classification, rc.new_classification, p.out_of_stock_date, rp.total_month_quantity, {day_columns_query}
                        ORDER BY total_month_quantity DESC"""                  

        final_query = base_query
        query_result = conn.execute(text(final_query))

        formated_data = [] 
        parent_sku_str = ''                     
        if query_result:         
            for data in query_result:
                obj = {}   
                parent_sku_str = parent_sku_str + "'" + str(data[1]) + "', " 
                obj["PRODUCT TITLE"] = data[0] 
                obj["PARENT SKU"] = data[1]  
                obj["SKU"] = ''
                obj['BLOCKED QTY'] = 0
                obj["AVAILABLE"] = int(data[3]) 
                obj["INCOMING"] = int(data[2])
                count = 1
                for i in range(1, today + 1):
                    day_key = str(i).zfill(2) + " " + month
                    value_key = 5 + count 
                    day_value = int(data[value_key]) if data[value_key] is not None else 0
                    obj[day_key] = day_value
                    count += 1 

                obj["TOTAL"] = int(data[5]) if data[5] is not None else 0
                obj['child_data'] = []

                formated_data.append(obj)  

            parent_sku_str = parent_sku_str.rstrip(', ')
            
            child_data_array = get_discontinued_products_daily_sold_child_data(conn, parent_sku_str)   
            
            if child_data_array['status'] == 200:
                child_array = child_data_array['data']['data']
                reserved_qty_array = child_data_array['data']['reserved_qty_data']
                parent_sku_list = [sku.strip().strip("'") for sku in parent_sku_str.split(',')]
                
                for p_sku in parent_sku_list:                                        
                    for item in formated_data:                        
                        if p_sku == item.get('PARENT SKU'):     
                            item['BLOCKED QTY'] = reserved_qty_array.get(p_sku, 0) 
                            item['child_data'] = child_array.get(p_sku, {})
                            break   
            
            # Create CSV content in memory
            if formated_data:
                utc_now = datetime.datetime.now(pytz.utc)
                cst_now = utc_now.astimezone(pytz.timezone('America/Chicago'))
                created_date = cst_now .strftime("%m-%d-%Y_%H:%M")
                zip_filename = f"Replenishment_Discontinued_Product_report_{created_date}.zip"

                csv_content = io.StringIO()
                keys = list(formated_data[0].keys())
                if 'child_data' in keys:  # Check if 'child_data' exists
                    keys.remove('child_data')
                writer = csv.DictWriter(csv_content, fieldnames=keys)
                writer.writeheader()
                for row in formated_data:
                    if 'child_data' in row:
                        for child in row['child_data']:
                            writer.writerow(child)
                        del row['child_data']
                    writer.writerow(row)

                # Compress CSV into ZIP
                zip_buffer = compress_csv(csv_content, zip_filename)

                email_util.send_discontinued_products_daily_sold_csv_mail(store_id, zip_buffer, query_params['username'], zip_filename, user_name, created_date)
    
    finally:
        if conn:
            conn.close()

def get_discontinued_products_daily_sold_child_data(conn, parent_sku_str):
    response = {
        "status": 400,
        "data": {}
    }
    try:
        # Get current day of month
        today = datetime.datetime.now().day
        month = datetime.datetime.now().strftime("%b")

        # Dynamically create SQL part for days
        day_columns = []
        for i in range(1, today + 1):
            day_columns.append(f"SUM(rp.day_{i}) as day_{i}")

        day_columns_query = ", ".join(day_columns)
        day_names = common_util.get_day_array_for_meta()
        
        query_result = None
        if parent_sku_str != '':
            base_query = f"""
                            select rp.product_title, rp.sku, rp.parent_sku,
                            rp.quantity_incoming, rp.quantity_available, rp.quantity_on_hold, rp.total_month_quantity, sum(rrv.quantity) as reserved_quantity, {day_columns_query}
                            from {AnalyticsDB.get_current_month_daily_sales_variants_table()} rp
                            left join replenishment_reserved_variants rrv ON rp.sku = rrv.sku
                            where rp.parent_sku IN ({parent_sku_str}) GROUP BY rp.product_title, rp.sku, rp.parent_sku, rp.total_month_quantity, 
                            rp.quantity_incoming, rp.quantity_available, rp.quantity_on_hold
                            ORDER BY rp.product_title ASC                                              
                            """
            query_result = conn.execute(text(base_query))

            formatted_data = {} 
            reserved_qty_data = {}
            final_result = {
                'data': [],
                'reserved_qty_data': [],
                'meta': {}
            }
            if query_result:
                for data in query_result:
                    obj = {}                                                   
                    obj["PRODUCT TITLE"] = data[0]
                    obj["PARENT SKU"] = data[2]
                    obj["SKU"] = data[1]  
                    obj["BLOCKED QTY"] = int(data[7]) if data[7] != None else 0 
                    obj["AVAILABLE"] = int(data[4])
                    obj["INCOMING"] = int(data[3])
                    count = 1
                    for i in range(1, today + 1):
                        day_key = str(i).zfill(2) + " " + month
                        value_key = 7 + count
                        day_value = int(data[value_key]) if data[value_key] is not None else 0
                        obj[day_key] = day_value
                        count += 1

                    obj["TOTAL"] = int(data[6]) if data[6] is not None else 0 

                    reserved_qty = int(data[7]) if data[7] != None else 0                                      
                    if data[2] in formatted_data:
                        if formatted_data[data[2]] is not None:
                            formatted_data[data[2]].append(obj)
                            reserved_qty_data[data[2]] = reserved_qty_data[data[2]] + int(reserved_qty)
                        else:
                            formatted_data[data[2]] = [obj]
                            reserved_qty_data[data[2]] = reserved_qty_data[data[2]] + int(reserved_qty)
                    else:
                        formatted_data[data[2]] = [obj]         
                        reserved_qty_data[data[2]] = int(reserved_qty)       
                
                final_result['data'] = formatted_data 
                final_result['reserved_qty_data'] = reserved_qty_data
                final_result['meta'] = day_names
            
                response['data'] = final_result
                response['status'] = 200           
    except Exception as e:
        response['message'] = 'Something went wrong'
        response['status'] = 500
    
    return response


def get_product_price_list_replenishment_dashboard(store, parent_skus):
    db = get_admin_db_client_for_store_id(store['id'])
    static_price_lists = []

    if store['id'] != '661239751b9ce4bd7f85237c':
        static_price_lists = price_list_util.fetch_static_price_lists(store['id'])

    # Fetch price lists from BigCommerce
    price_lists, _ = bc_price_list.fetch_price_lists(store)

    if static_price_lists:
        for static_price_list in static_price_lists:
            price_lists['data'].append({
                'id': static_price_list['id'],
                'name': static_price_list['name'],
                'date_created': None,
                'date_modified': None,
                'active': static_price_list['active']
            })

    # Prepare metadata and active lists
    price_list_meta = {}
    price_list_map = {}
    active_price_lists = []

    for price_list in price_lists['data']:
        price_list_entry = {
            'id': price_list['id'],
            'key': f"price_{len(price_list_meta) + 1}",
            'name': price_list['name'],
            'active': price_list['active']
        }

        if price_list['active']:
            active_price_lists.append(price_list_entry)

        price_list_map[price_list['id']] = price_list_entry

    # Sort active_price_lists to put id=7 first
    active_price_lists.sort(key=lambda x: (x['id'] != 7, x['id']))

    # Re-index price list keys
    for idx, price_list in enumerate(active_price_lists, 1):
        price_list_key = f"price_{idx}"
        price_list_meta[price_list_key] = {
            'name': price_list['name'],
            'active': price_list['active']
        }
        price_list_map[price_list['id']]['key'] = price_list_key

    # 🔍 Fetch all products in one query
    products_cursor = db["product_price_lists"].find({"parent_product_sku": {"$in": [str(sku) for sku in parent_skus]}})
    products = {prod['parent_product_sku']: prod for prod in products_cursor}

    # 🧾 Prepare final response mapped by parent_sku
    result = {}

    for parent_sku in parent_skus:
        product = products.get(str(parent_sku))
        if not product:
            result[str(parent_sku)] = {"message": f"Product with parent_sku {parent_sku} not found", "status": 404}
            continue

        variant_prices = [variant['variant_price'] for variant in product['variants']]
        valid_prices = [price for price in variant_prices if price is not None]

        if len(set(valid_prices)) > 1:
            product['default_price'] = f"{min(valid_prices)}-{max(valid_prices)}"
        elif valid_prices:
            product['default_price'] = str(valid_prices[0])
        else:
            product['default_price'] = ""

        price_list_ranges = {pl['id']: [] for pl in active_price_lists}

        for variant in product['variants']:
            variant['variant_price'] = str(variant.get('variant_price', ''))
            variant_price_lists = []
            default_price_lists = {pl['id']: "" for pl in active_price_lists}

            for price in variant.get('price_list', []):
                price_list_id = price['price_list_id']
                if price_list_id in price_list_map and price_list_map[price_list_id]['active']:
                    price_value = price.get('price')
                    if price_value is not None:
                        price_str = str(price_value)
                        default_price_lists[price_list_id] = price_str
                        try:
                            price_list_ranges[price_list_id].append(float(price_str))
                        except ValueError:
                            pass

            for price_list in active_price_lists:
                variant_price_lists.append({
                    "price_list_id": price_list['id'],
                    price_list['key']: default_price_lists[price_list['id']]
                })

            variant['price_list'] = variant_price_lists

        product_price_lists = []
        for price_list in active_price_lists:
            price_list_id = price_list['id']
            if price_list_ranges[price_list_id]:
                min_price = min(price_list_ranges[price_list_id])
                max_price = max(price_list_ranges[price_list_id])
                price_range = str(min_price) if min_price == max_price else f"{min_price}-{max_price}"
            else:
                price_range = ""

            product_price_lists.append({
                "price_list_id": price_list_id,
                price_list['key']: price_range
            })

        result[str(parent_sku)] = {
            "data": [{"price_list": product_price_lists}],
            "meta": {"price_lists": price_list_meta}
        }

    return result

def compress_csv(csv_content, zip_filename):
    """Compress the CSV content and save it as a ZIP file."""
    zip_buffer = io.BytesIO()  # Create an in-memory zip buffer

    with zipfile.ZipFile(zip_buffer, "w", zipfile.ZIP_DEFLATED) as zipf:
        zipf.writestr(zip_filename.replace(".zip", ".csv"), csv_content.getvalue())  # Add CSV file to zip

    zip_buffer.seek(0)  # Move to the beginning of the buffer
    return zip_buffer  # Return the compressed zip file

def create_daily_sales_views(conn):
    """
    Creates views for daily sales data at both product and variant levels.
    This function creates two views:
    1. analytics.current_month_daily_sales - Product-level daily sales data
    2. analytics.current_month_daily_sales_variants - Variant-level daily sales data
    """
    # Create product-level daily sales view
    product_view_query = f"""
    -- Drop existing view if it exists
    DROP VIEW IF EXISTS analytics.current_month_daily_sales;
    
    -- Create the new view with current month daily sales data
    CREATE VIEW analytics.current_month_daily_sales AS
    WITH current_month_dates AS (
        -- Generate a series of dates for the current month from the 1st to today in CST timezone
        SELECT generate_series(
            date_trunc('month', (current_timestamp AT TIME ZONE 'UTC' AT TIME ZONE 'CST')::date)::date,
            (current_timestamp AT TIME ZONE 'UTC' AT TIME ZONE 'CST')::date,
            '1 day'::interval
        )::date AS sale_date
    ),
    daily_sales AS (
        -- Get daily sales data for the current month in CST timezone
        SELECT 
            product_id,
            parent_sku,
            order_date_time AS sale_date,
            SUM(quantity) AS daily_quantity
        FROM {AnalyticsDB.get_products_trend_table()}
        WHERE order_date_time >= date_trunc('month', (current_timestamp AT TIME ZONE 'UTC' AT TIME ZONE 'CST')::date)
        AND order_date_time <= (current_timestamp AT TIME ZONE 'UTC' AT TIME ZONE 'CST')::date
        GROUP BY product_id, parent_sku, order_date_time
    ),
    product_info AS (
        -- Get product information
        SELECT 
            sv.parent_sku,
            MAX(pd.product_name) AS product_title,
            MAX(sv.cost) AS cost,
            MAX(sv.retail_price) AS retail_price,
            MAX(sv.sale_price) AS sale_price,
            MAX(sv.incremental_quantity) AS incremental_quantity,
            SUM(sv.quantity_on_hand) AS quantity_on_hand,
            SUM(sv.quantity_pending) AS quantity_pending,
            SUM(sv.quantity_incoming) AS quantity_incoming,
            SUM(sv.quantity_available) AS quantity_available,
            SUM(sv.quantity_on_hold) AS quantity_on_hold,
            MAX(sv.primary_supplier) AS primary_supplier,
            MAX(sv.classification) AS classification,
            MAX(sv.brand) AS brand
        FROM public.skuvault_catalog sv
        JOIN public.products pd ON sv.parent_sku = pd.sku
        WHERE sv.primary_supplier <> 'Unknown'
        GROUP BY sv.parent_sku
    ),
    total_month_sales AS (
        -- Calculate total sales for the current month in CST timezone
        SELECT 
            product_id,
            parent_sku,
            SUM(quantity) AS total_month_quantity
        FROM {AnalyticsDB.get_products_trend_table()}
        WHERE order_date_time >= date_trunc('month', (current_timestamp AT TIME ZONE 'UTC' AT TIME ZONE 'CST')::date)
        AND order_date_time <= (current_timestamp AT TIME ZONE 'UTC' AT TIME ZONE 'CST')::date
        GROUP BY product_id, parent_sku
    ),
    -- Create a pivot of daily sales data
    daily_sales_pivot AS (
        SELECT 
            parent_sku,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 1 THEN daily_quantity ELSE 0 END) AS day_1,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 2 THEN daily_quantity ELSE 0 END) AS day_2,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 3 THEN daily_quantity ELSE 0 END) AS day_3,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 4 THEN daily_quantity ELSE 0 END) AS day_4,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 5 THEN daily_quantity ELSE 0 END) AS day_5,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 6 THEN daily_quantity ELSE 0 END) AS day_6,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 7 THEN daily_quantity ELSE 0 END) AS day_7,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 8 THEN daily_quantity ELSE 0 END) AS day_8,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 9 THEN daily_quantity ELSE 0 END) AS day_9,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 10 THEN daily_quantity ELSE 0 END) AS day_10,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 11 THEN daily_quantity ELSE 0 END) AS day_11,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 12 THEN daily_quantity ELSE 0 END) AS day_12,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 13 THEN daily_quantity ELSE 0 END) AS day_13,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 14 THEN daily_quantity ELSE 0 END) AS day_14,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 15 THEN daily_quantity ELSE 0 END) AS day_15,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 16 THEN daily_quantity ELSE 0 END) AS day_16,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 17 THEN daily_quantity ELSE 0 END) AS day_17,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 18 THEN daily_quantity ELSE 0 END) AS day_18,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 19 THEN daily_quantity ELSE 0 END) AS day_19,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 20 THEN daily_quantity ELSE 0 END) AS day_20,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 21 THEN daily_quantity ELSE 0 END) AS day_21,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 22 THEN daily_quantity ELSE 0 END) AS day_22,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 23 THEN daily_quantity ELSE 0 END) AS day_23,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 24 THEN daily_quantity ELSE 0 END) AS day_24,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 25 THEN daily_quantity ELSE 0 END) AS day_25,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 26 THEN daily_quantity ELSE 0 END) AS day_26,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 27 THEN daily_quantity ELSE 0 END) AS day_27,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 28 THEN daily_quantity ELSE 0 END) AS day_28,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 29 THEN daily_quantity ELSE 0 END) AS day_29,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 30 THEN daily_quantity ELSE 0 END) AS day_30,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 31 THEN daily_quantity ELSE 0 END) AS day_31
        FROM daily_sales
        GROUP BY parent_sku
    )
    -- Combine all the data
    SELECT 
        pi.parent_sku,
        pi.product_title,
        pi.cost,
        pi.retail_price,
        pi.sale_price,
        pi.incremental_quantity,
        pi.quantity_on_hand,
        pi.quantity_pending,
        pi.quantity_incoming,
        pi.quantity_available,
        pi.quantity_on_hold,
        pi.primary_supplier,
        pi.classification,
        pi.brand,
        COALESCE(ts.total_month_quantity, 0) AS total_month_quantity,
        -- Daily sales columns
        dsp.day_1, dsp.day_2, dsp.day_3, dsp.day_4, dsp.day_5, dsp.day_6, dsp.day_7, dsp.day_8, dsp.day_9, dsp.day_10,
        dsp.day_11, dsp.day_12, dsp.day_13, dsp.day_14, dsp.day_15, dsp.day_16, dsp.day_17, dsp.day_18, dsp.day_19, dsp.day_20,
        dsp.day_21, dsp.day_22, dsp.day_23, dsp.day_24, dsp.day_25, dsp.day_26, dsp.day_27, dsp.day_28, dsp.day_29, dsp.day_30, dsp.day_31,
        -- Add a field for the current month and year in CST timezone
        EXTRACT(MONTH FROM (current_timestamp AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) AS current_month,
        EXTRACT(YEAR FROM (current_timestamp AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) AS current_year,
        -- Add a field for the number of days in the current month
        EXTRACT(DAY FROM (date_trunc('month', (current_timestamp AT TIME ZONE 'UTC' AT TIME ZONE 'CST')::date) + interval '1 month - 1 day')::date) AS days_in_month,
        -- Add a field for the current day of the month in CST timezone
        EXTRACT(DAY FROM (current_timestamp AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) AS current_day
    FROM product_info pi
    LEFT JOIN total_month_sales ts ON pi.parent_sku = ts.parent_sku
    LEFT JOIN daily_sales_pivot dsp ON pi.parent_sku = dsp.parent_sku
    ORDER BY COALESCE(ts.total_month_quantity, 0) DESC;
    """
    
    # Create variant-level daily sales view
    variant_view_query = f"""
    -- Drop existing view if it exists
    DROP VIEW IF EXISTS analytics.current_month_daily_sales_variants;
    
    -- Create the new view with current month daily sales data for variants
    CREATE VIEW analytics.current_month_daily_sales_variants AS
    WITH current_month_dates AS (
        -- Generate a series of dates for the current month from the 1st to today in CST timezone
        SELECT generate_series(
            date_trunc('month', (current_timestamp AT TIME ZONE 'UTC' AT TIME ZONE 'CST')::date)::date,
            (current_timestamp AT TIME ZONE 'UTC' AT TIME ZONE 'CST')::date,
            '1 day'::interval
        )::date AS sale_date
    ),
    daily_sales AS (
        -- Get daily sales data for the current month in CST timezone
        SELECT 
            variant_id,
            product_id,
            parent_sku,
            variants_sku,
            order_date_time AS sale_date,
            SUM(quantity) AS daily_quantity
        FROM {AnalyticsDB.get_variants_trend_table()}
        WHERE order_date_time >= date_trunc('month', (current_timestamp AT TIME ZONE 'UTC' AT TIME ZONE 'CST')::date)
        AND order_date_time <= (current_timestamp AT TIME ZONE 'UTC' AT TIME ZONE 'CST')::date
        GROUP BY variant_id, variants_sku, parent_sku, product_id, order_date_time
    ),
    variant_info AS (
        -- Get variant information
        SELECT 
            sv.sku,
            sv.parent_sku,
            MAX(sv.title) AS product_title,
            MAX(sv.cost) AS cost,
            MAX(sv.retail_price) AS retail_price,
            MAX(sv.sale_price) AS sale_price,
            MAX(sv.reorder_point) AS reorder_point,
            MAX(sv.incremental_quantity) AS incremental_quantity,
            SUM(sv.quantity_on_hand) AS quantity_on_hand,
            SUM(sv.quantity_pending) AS quantity_pending,
            SUM(sv.quantity_incoming) AS quantity_incoming,
            SUM(sv.quantity_available) AS quantity_available,
            SUM(sv.quantity_on_hold) AS quantity_on_hold,
            MAX(sv.primary_supplier) AS primary_supplier,
            MAX(sv.classification) AS classification,
            MAX(sv.brand) AS brand
        FROM public.skuvault_catalog sv
        WHERE sv.primary_supplier <> 'Unknown'
        GROUP BY sv.sku, sv.parent_sku
    ),
    total_month_sales AS (
        -- Calculate total sales for the current month in CST timezone
        SELECT 
            variant_id,
            product_id,
            parent_sku,
            variants_sku,
            SUM(quantity) AS total_month_quantity
        FROM {AnalyticsDB.get_variants_trend_table()}
        WHERE order_date_time >= date_trunc('month', (current_timestamp AT TIME ZONE 'UTC' AT TIME ZONE 'CST')::date)
        AND order_date_time <= (current_timestamp AT TIME ZONE 'UTC' AT TIME ZONE 'CST')::date
        GROUP BY variant_id, variants_sku, parent_sku, product_id
    ),
    -- Create a pivot of daily sales data
    daily_sales_pivot AS (
        SELECT 
            variant_id,
            product_id,
            parent_sku,
            variants_sku,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 1 THEN daily_quantity ELSE 0 END) AS day_1,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 2 THEN daily_quantity ELSE 0 END) AS day_2,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 3 THEN daily_quantity ELSE 0 END) AS day_3,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 4 THEN daily_quantity ELSE 0 END) AS day_4,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 5 THEN daily_quantity ELSE 0 END) AS day_5,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 6 THEN daily_quantity ELSE 0 END) AS day_6,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 7 THEN daily_quantity ELSE 0 END) AS day_7,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 8 THEN daily_quantity ELSE 0 END) AS day_8,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 9 THEN daily_quantity ELSE 0 END) AS day_9,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 10 THEN daily_quantity ELSE 0 END) AS day_10,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 11 THEN daily_quantity ELSE 0 END) AS day_11,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 12 THEN daily_quantity ELSE 0 END) AS day_12,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 13 THEN daily_quantity ELSE 0 END) AS day_13,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 14 THEN daily_quantity ELSE 0 END) AS day_14,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 15 THEN daily_quantity ELSE 0 END) AS day_15,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 16 THEN daily_quantity ELSE 0 END) AS day_16,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 17 THEN daily_quantity ELSE 0 END) AS day_17,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 18 THEN daily_quantity ELSE 0 END) AS day_18,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 19 THEN daily_quantity ELSE 0 END) AS day_19,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 20 THEN daily_quantity ELSE 0 END) AS day_20,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 21 THEN daily_quantity ELSE 0 END) AS day_21,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 22 THEN daily_quantity ELSE 0 END) AS day_22,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 23 THEN daily_quantity ELSE 0 END) AS day_23,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 24 THEN daily_quantity ELSE 0 END) AS day_24,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 25 THEN daily_quantity ELSE 0 END) AS day_25,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 26 THEN daily_quantity ELSE 0 END) AS day_26,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 27 THEN daily_quantity ELSE 0 END) AS day_27,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 28 THEN daily_quantity ELSE 0 END) AS day_28,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 29 THEN daily_quantity ELSE 0 END) AS day_29,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 30 THEN daily_quantity ELSE 0 END) AS day_30,
            MAX(CASE WHEN EXTRACT(DAY FROM sale_date) = 31 THEN daily_quantity ELSE 0 END) AS day_31
        FROM daily_sales
        GROUP BY variant_id, product_id, parent_sku, variants_sku
    )
    -- Combine all the data
    SELECT 
        vi.sku,
        vi.parent_sku,
        vi.product_title,
        vi.cost,
        vi.retail_price,
        vi.sale_price,
        vi.reorder_point,
        vi.incremental_quantity,
        vi.quantity_on_hand,
        vi.quantity_pending,
        vi.quantity_incoming,
        vi.quantity_available,
        vi.quantity_on_hold,
        vi.primary_supplier,
        vi.classification,
        vi.brand,
        COALESCE(ts.total_month_quantity, 0) AS total_month_quantity,
        -- Daily sales columns
        dsp.day_1, dsp.day_2, dsp.day_3, dsp.day_4, dsp.day_5, dsp.day_6, dsp.day_7, dsp.day_8, dsp.day_9, dsp.day_10,
        dsp.day_11, dsp.day_12, dsp.day_13, dsp.day_14, dsp.day_15, dsp.day_16, dsp.day_17, dsp.day_18, dsp.day_19, dsp.day_20,
        dsp.day_21, dsp.day_22, dsp.day_23, dsp.day_24, dsp.day_25, dsp.day_26, dsp.day_27, dsp.day_28, dsp.day_29, dsp.day_30, dsp.day_31,
        -- Add a field for the current month and year in CST timezone
        EXTRACT(MONTH FROM (current_timestamp AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) AS current_month,
        EXTRACT(YEAR FROM (current_timestamp AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) AS current_year,
        -- Add a field for the number of days in the current month
        EXTRACT(DAY FROM (date_trunc('month', (current_timestamp AT TIME ZONE 'UTC' AT TIME ZONE 'CST')::date) + interval '1 month - 1 day')::date) AS days_in_month,
        -- Add a field for the current day of the month in CST timezone
        EXTRACT(DAY FROM (current_timestamp AT TIME ZONE 'UTC' AT TIME ZONE 'CST')) AS current_day
    FROM variant_info vi
    LEFT JOIN total_month_sales ts ON vi.sku = ts.variants_sku
    LEFT JOIN daily_sales_pivot dsp ON vi.sku = dsp.variants_sku
    ORDER BY COALESCE(ts.total_month_quantity, 0) DESC;
    """
    
    try:
        # Execute product view query
        conn.execute(text(product_view_query))
        logger.info("Successfully created current_month_daily_sales view")
        
        # Execute variant view query
        conn.execute(text(variant_view_query))
        logger.info("Successfully created current_month_daily_sales_variants view")
        
        conn.commit()
    except Exception as ex:
        error_msg = str(traceback.format_exc())
        logger.error(error_msg)
        raise Exception("Failed to create daily sales views: " + error_msg)

def create_daily_sales_tables(store_id):
    """
    Creates tables for daily sales data at both product and variant levels.
    This function creates two tables:
    1. analytics.current_month_daily_sales_table - Product-level daily sales data
    2. analytics.current_month_daily_sales_variants_table - Variant-level daily sales data
    """
    conn = None
    try:
        conn = db.get_connection(store_id)
        
        # First create the views
        create_daily_sales_views(conn)
        
        # Now create the product-level table from the view
        product_table_query = f"""
            -- Drop existing table if it exists
            DROP TABLE IF EXISTS analytics.current_month_daily_sales_table;
            
            -- Create the table from the view
            CREATE TABLE analytics.current_month_daily_sales_table AS
            SELECT * FROM analytics.current_month_daily_sales;
            
            -- Create indexes for better performance
            CREATE INDEX IF NOT EXISTS idx_current_month_daily_sales_parent_sku 
            ON analytics.current_month_daily_sales_table(parent_sku);
            
            CREATE INDEX IF NOT EXISTS idx_current_month_daily_sales_primary_supplier 
            ON analytics.current_month_daily_sales_table(primary_supplier);
            
            CREATE INDEX IF NOT EXISTS idx_current_month_daily_sales_classification 
            ON analytics.current_month_daily_sales_table(classification);
            
            CREATE INDEX IF NOT EXISTS idx_current_month_daily_sales_brand 
            ON analytics.current_month_daily_sales_table(brand);
            
            -- Add a last_updated timestamp column
            ALTER TABLE analytics.current_month_daily_sales_table 
            ADD COLUMN IF NOT EXISTS last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
            
            -- Update the timestamp
            UPDATE analytics.current_month_daily_sales_table 
            SET last_updated = CURRENT_TIMESTAMP;
        """
        
        # Now create the variant-level table from the view
        variant_table_query = f"""
            -- Drop existing table if it exists
            DROP TABLE IF EXISTS analytics.current_month_daily_sales_variants_table;
            
            -- Create the table from the view
            CREATE TABLE analytics.current_month_daily_sales_variants_table AS
            SELECT * FROM analytics.current_month_daily_sales_variants;
            
            -- Create indexes for better performance
            CREATE INDEX IF NOT EXISTS idx_current_month_daily_sales_variants_sku 
            ON analytics.current_month_daily_sales_variants_table(sku);
            
            CREATE INDEX IF NOT EXISTS idx_current_month_daily_sales_variants_parent_sku 
            ON analytics.current_month_daily_sales_variants_table(parent_sku);
            
            CREATE INDEX IF NOT EXISTS idx_current_month_daily_sales_variants_primary_supplier 
            ON analytics.current_month_daily_sales_variants_table(primary_supplier);
            
            CREATE INDEX IF NOT EXISTS idx_current_month_daily_sales_variants_classification 
            ON analytics.current_month_daily_sales_variants_table(classification);
            
            CREATE INDEX IF NOT EXISTS idx_current_month_daily_sales_variants_brand 
            ON analytics.current_month_daily_sales_variants_table(brand);
            
            -- Add a last_updated timestamp column
            ALTER TABLE analytics.current_month_daily_sales_variants_table 
            ADD COLUMN IF NOT EXISTS last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
            
            -- Update the timestamp
            UPDATE analytics.current_month_daily_sales_variants_table 
            SET last_updated = CURRENT_TIMESTAMP;
        """
        
        # Execute product table query
        logger.info(f"Creating analytics.current_month_daily_sales_table table")
        start_time = datetime.datetime.now().timestamp()
        conn.execute(text(product_table_query))
        end_time = datetime.datetime.now().timestamp()
        total_time = (end_time - start_time)
        logger.info(f"analytics.current_month_daily_sales_table table has been created. Time: {total_time}")
        
        # Execute variant table query
        logger.info(f"Creating analytics.current_month_daily_sales_variants_table table")
        start_time = datetime.datetime.now().timestamp()
        conn.execute(text(variant_table_query))
        end_time = datetime.datetime.now().timestamp()
        total_time = (end_time - start_time)
        logger.info(f"analytics.current_month_daily_sales_variants_table table has been created. Time: {total_time}")
        
        conn.commit()
        
    except Exception as ex:
        error_msg = str(traceback.format_exc())
        logger.error(error_msg)
        raise Exception("Failed to create daily sales tables: " + error_msg)
    finally:
        if conn:
            conn.commit()
            conn.close()

def get_no_sold_products_analytics_data_csv(store_id, query_params):
    response = {
        "status": 400,
    }
    user_details = store_db.fetch_user_by_username(store_id, query_params['username'])
    user_name = user_details['name'] if 'name' in user_details and user_details else ''
    hide_discontinued = query_params.get('hide_discontinued', 'true')    
    sort_by = query_params.get('sort_by','').strip()
    sort_array = sort_by.split("/") if sort_by != '' else []
    search_value = query_params.get('searchValue', None)
    days_to_replenishment = query_params.get('days_to_replenishment', 180)
    purchaser_filter = query_params.get('purchaser', '').strip()

    column_query = ''   
    
    if days_to_replenishment:
        column_query = column_query + ', sum(rp.total_sold_' + str(days_to_replenishment) + ')'

    append_query = ''

    append_query += f" AND rp.total_sold_{days_to_replenishment} = 0"
   
    if search_value and search_value != '':
        append_query = append_query + " AND  (rp.product_title ILIKE '%" + search_value + "%' or rp.parent_sku ILIKE '%" + search_value + "%') "     

    if hide_discontinued == 'true':        
        append_query = append_query + " AND ((LOWER(rp.product_title) NOT ILIKE '%discontinued%') AND (LOWER(rp.product_title) NOT ILIKE '%discountinued%')) AND ((LOWER(sv.classification) NOT ILIKE '%discontinued%') AND (LOWER(sv.classification) NOT ILIKE '%discountinued%'))"

    if purchaser_filter:
        append_query += f" AND usm.user_name = '{purchaser_filter}'"
        
    order_by = ""
    if len(sort_array):
        sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'   
        nulls_order = "NULLS FIRST" if sort_direction.lower() == "asc" else "NULLS LAST"      
        order_by = " ORDER BY " + sort_array[0] + " " + sort_direction + " " + nulls_order

    conn = db.get_connection(store_id)
    try:           
        final_count_query = f"SELECT COUNT(*) FROM (select rp.parent_sku from {AnalyticsDB.get_replenishment_products_table()} rp left join products p ON rp.parent_sku = p.sku left join skuvault_catalog sv ON sv.parent_sku = rp.parent_sku left join user_supplier_mapping usm ON sv.primary_supplier = ANY(string_to_array(usm.suppliers, ','))  where rp.product_title <> ''" + append_query + " group by rp.parent_sku) AS subquery"                     
                 
        rs = conn.execute(text(final_count_query))
        total_count = int(rs.scalar())

        base_query = f"""WITH consolidated_skuvault AS (
                            SELECT DISTINCT primary_supplier, classification, parent_sku
                            FROM skuvault_catalog GROUP BY parent_sku, primary_supplier, classification
                        )
                        select MAX(rp.product_title) AS product_title, MAX(rp.product_id) AS product_id, rp.parent_sku, MAX(rp.cost) AS cost, MAX(usm.user_name) AS purchaser_name {column_query} 
                        from {AnalyticsDB.get_replenishment_products_table()} rp 
                        left join products p ON rp.parent_sku = p.sku 
                        left join replenishment_classifications rc ON rp.parent_sku = rc.sku 
                        left join consolidated_skuvault sv ON sv.parent_sku = rp.parent_sku 
                        left join user_supplier_mapping usm ON sv.primary_supplier = usm.suppliers
                        where sv.primary_supplier <> 'Unknown' {append_query}              
                        group by rp.parent_sku, sv.primary_supplier, sv.classification
                        {order_by}"""         

        final_query = base_query
        query_result = conn.execute(text(final_query))

        formated_data = [] 
        parent_sku_str = ''                     
        if query_result:         
            for data in query_result:
                obj = {}   
                parent_sku_str = parent_sku_str + "'" + str(data[2]) + "', " 
                   
                obj["PRODUCT NAME"] = data[0]
                obj["PARENT SKU"] = data[2]
                obj["SKU"] = ''  
                obj["COST"] = float(data[3])
                obj["PURCHASER NAME"] = data[4]
                # obj["days_to_replenishment"] = days_to_replenishment
                obj['child_data'] = []

                formated_data.append(obj)      

            parent_sku_str = parent_sku_str.rstrip(', ')
            
            child_data_array = get_on_load_no_sold_products_child_data(conn, parent_sku_str, '', days_to_replenishment, hide_discontinued)   
            
            if child_data_array['status'] == 200:
                child_array = child_data_array['data']
                parent_sku_list = [sku.strip().strip("'") for sku in parent_sku_str.split(',')]
                
                for p_sku in parent_sku_list:                                        
                    for item_data in formated_data:                        
                        if p_sku == item_data.get('PARENT SKU'):    
                            item_data['child_data'] = child_array.get(p_sku, {})
                            break 
          
            # Create CSV content in memory
            if formated_data:
                utc_now = datetime.datetime.now(pytz.utc)
                cst_now = utc_now.astimezone(pytz.timezone('America/Chicago'))
                created_date = cst_now .strftime("%m-%d-%Y_%H:%M")
                zip_filename = f"Analytics_No_Sold_Product_report_{created_date}.zip"

                csv_content = io.StringIO()
                keys = list(formated_data[0].keys())
                if 'child_data' in keys:  # Check if 'child_data' exists
                    keys.remove('child_data')
                writer = csv.DictWriter(csv_content, fieldnames=keys)
                writer.writeheader()
                for row in formated_data:
                    if 'child_data' in row:
                        for child in row['child_data']:
                            writer.writerow(child)
                        del row['child_data']
                    writer.writerow(row)  

                # Compress CSV into ZIP
                zip_buffer = compress_csv(csv_content, zip_filename)

                email_util.no_sold_products_analytics_csv_mail(store_id, zip_buffer, query_params['username'], zip_filename, user_name, created_date)
    except Exception as e:
        logger.error(traceback.format_exc())
    finally:
        if conn:
            conn.close()

def get_on_load_no_sold_products_child_data(conn, parent_sku_str, append_query, days_to_replenishment, hide_discontinued):
    response = {
        "status": 400,
        "data": {}
    }
    column_query = ''   
    
    if days_to_replenishment:        
        column_query = column_query + ', rp.total_sold_' + str(days_to_replenishment)      

    try:
        query_result = None
        if parent_sku_str != '':
                base_query = f"""
                            select rp.product_title, rp.sku, rp.parent_sku, rp.cost, MAX(usm.user_name) AS purchaser_name {column_query}
                            from {AnalyticsDB.get_replenishment_variants_table()} rp
                            left join variants v ON rp.sku = v.variants_sku
                            left join skuvault_catalog sv ON sv.sku = rp.sku
                            left join user_supplier_mapping usm ON sv.primary_supplier = usm.suppliers
                            where rp.parent_sku IN ({parent_sku_str}) {append_query} GROUP BY rp.product_title, rp.sku, rp.parent_sku, rp.cost {column_query}   
                            ORDER BY rp.product_title ASC                                              
                            """
                            
                query_result = conn.execute(text(base_query))
        
        formatted_data = {} 
        if query_result:
            for data in query_result:
                obj = {}                                                   
                obj["PRODUCT NAME"] = data[0]
                obj["SKU"] = data[1] 
                obj["PARENT SKU"] = data[2]
                obj["COST"] = float(data[3])
                obj["PURCHASER NAME"] = data[4]
                # obj["days_to_replenishment"] = days_to_replenishment                              
                if data[2] in formatted_data:
                    if formatted_data[data[2]] is not None:
                        formatted_data[data[2]].append(obj)
                    else:
                        formatted_data[data[2]] = [obj]
                else:
                    formatted_data[data[2]] = [obj]  
            
            response['data'] = formatted_data
            response['status'] = 200           
    except Exception as e:
        logger.error(traceback.format_exc())
    
    return response
