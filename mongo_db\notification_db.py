import datetime
import mongo_db

NOTIFICATION_DB = "notifications"

STORE_LOG_COLLECTION = "store_logs"
NOTIFICATION_COLLECTION = "notification"

def _get_notification_db_client(store):
    return mongo_db.get_admin_db_client(store)

def insert_notifcation(store, notification_type, message, error_content=""):
    current_time = datetime.datetime.now(datetime.timezone.utc)
    notification = {}
    notification["error"] = error_content
    notification["notified_at"] = current_time.timestamp()
    notification["notified_at_str"] = str(current_time)
    notification["type"] = notification_type
    notification["message"] = message
    db = _get_notification_db_client(store)
    db[NOTIFICATION_COLLECTION].insert_one(notification)

def get_latest_store_log(store):
    db = _get_notification_db_client(store)
    last_log = db[STORE_LOG_COLLECTION].find().sort("_id", -1).limit(1)
    result = None
    for log in last_log:
        result = log
    return result

def get_latest_notification(store, notification_type):
    db = _get_notification_db_client(store)
    last_notification = None
    cur = db[NOTIFICATION_COLLECTION].find({"type": notification_type}).sort("notified_at", -1).limit(1)
    if cur:
        for row in cur:
            last_notification = row
    
    return last_notification

def insert_store_logs(store, logs):
    db = _get_notification_db_client(store)
    db[STORE_LOG_COLLECTION].insert_many(logs)