from config import appconfig
import datetime

class ADConfigKey:
    ad_config = "ad_config"
    redis = "redis"
    db_info = "db_info"
    analytics_db = "analytics_db"

def get_db_config_from_store(store, app_name):
    profile = appconfig.get_app_profile()    
    return store.get(ADConfigKey.ad_config, {}).get(profile, {}).get(ADConfigKey.db_info, {}).get(app_name, None)

def get_analytics_db_config_from_store(store):
    return get_db_config_from_store(store, ADConfigKey.analytics_db)

def get_redis_config_from_store(store):
    profile = appconfig.get_app_profile()
    return store.get(ADConfigKey.ad_config, {}).get(profile, {}).get(ADConfigKey.redis, None)

def get_redis_config(store_id):
    redis_config = None
    import mongo_db
    store = mongo_db.get_store_by_id(store_id)
    if store:
        redis_config = get_redis_config_from_store(store)
    return redis_config

def get_current_timestamp():
    return datetime.datetime.now(datetime.timezone.utc)