from sqlalchemy import create_engine, inspect
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import datetime
import threading 
import logging
import traceback
import atexit
import utils

logger = logging.getLogger()

Base = declarative_base()

customers_table = "customers"
customer_addresses_table = "customer_addresses"
customer_form_fields_table = "customer_form_fields"
customer_login_sessions_table = "customer_login_sessions"
customer_carts = "customer_carts"
cart_line_items_inventory = "cart_line_items_inventory"
salesforce_customer_rep_table = "salesforce_customer_rep"
salesforce_order_details_table = "salesforce_order_details"

express_orders_table = "express_orders"
products_table = "products"
variants_table = "variants"
product_categories_table = "product_categories"
product_custom_fields_table = "product_custom_fields"
out_of_stock_sku_table = "out_of_stock_sku"
variants_visibility_rules_table = "variants_visibility_rules"
products_visibility_rules_table = "products_visibility_rules"
products_unhide_rules_table = "products_unhide_rules"
variants_shipping_groups_table = "variants_shipping_groups"
product_customer_price_mapping_table = "product_customer_price"

orders_table = "orders"
order_line_items_table = "order_line_items"
order_billing_addresses_table = "order_billing_addresses"
order_shipping_addresses_table = "order_shipping_addresses"
order_line_item_discounts_table = "order_line_item_discounts"
order_ny_restricted_zipcodes_table = "order_ny_restricted_zipcodes"

blocked_orders = "blocked_orders"
blocked_orders_logs = "blocked_order_logs"

skuvault_catalog = "skuvault_catalog"
order_consignment="order_consignment"
skuvault_sales = "skuvault_sales"
skuvault_cost = "skuvault_cost"

replenishment_products_data = "replenishment_products"
replenishment_variants_data = "replenishment_variants"
replenishment_reserved_variants = "replenishment_reserved_variants"

notifications_table = "notifications"

bo_purchase_orders = 'bo_purchase_orders'
bo_distribution_lineitems = 'bo_distribution_lineitems'
bo_published_distribution_logs = 'bo_published_distribution_logs'

class PGDBConnectionPool:
    _instance = None    

    def __new__(cls):
        if cls._instance is None:
            logger.info('Creating the PG DB connection pool')
            cls._instance = super(PGDBConnectionPool, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        logger.info("Entering PGDBConnectionPool")
        self._conn_pool = {}
        self.lock = threading.Lock()
        logger.info("Exiting PGDBConnectionPool")

    def get_analytics_db_info(self, store_id):
        analytics_db_info = None
        import mongo_db
        store = mongo_db.get_store_by_id(store_id)
        if store:
            analytics_db_info = utils.get_analytics_db_config_from_store(store)
        return analytics_db_info
    
    def get_db_engine(self, store_id):
        db_engine = self._conn_pool.get(store_id, None)
        if db_engine is None:
            self.lock.acquire()
            try:
                db_engine = self._conn_pool.get(store_id, None)
                if db_engine is None:
                    cur_time = str(datetime.datetime.now())
                    analytics_db_info = self.get_analytics_db_info(store_id)
                    db_user = analytics_db_info["app_users"]["taskworker"]
                    username = db_user["username"]
                    pswd = db_user["pswd"] 
                    host = analytics_db_info["host_name"]
                    port = analytics_db_info["port"]
                    db_name = analytics_db_info["db_name"]
                    if store_id == "63da3e98b702e324567f76f9":
                        conn_str = f"******************************************************************/stage"
                    else:
                        conn_str = f"******************************************************************/cbdtostore"
                    db_engine = create_engine(conn_str, connect_args={"application_name":cur_time}, 
                                    pool_size=1, max_overflow=20, pool_timeout=10, pool_pre_ping=True, 
                                    pool_recycle=3600, echo=False)
                    if db_engine is not None:
                        self._conn_pool[store_id] = db_engine
                        from pg_db import tablemeta_db, orders_db, skuvault_db, customers_db, products_db, salesforce_db
                        Base.metadata.create_all(db_engine)
            finally:
                self.lock.release()
        return db_engine

    def get_connection(self, store_id):
        return self.get_db_engine(store_id).connect()
    
    def get_session(self, store_id):
        db_engine = self.get_db_engine(store_id)
        session = sessionmaker(bind=db_engine)
        return session()
    
    def dispose_engines(self):
        for store_id, engine in self._conn_pool.items():
            engine.dispose()

conn_pool = PGDBConnectionPool()


@atexit.register
def shutdown_db_engine():
    logger.error("shutdown_db_engine: Exiting from the application. Closing DB connection pool")
    conn_pool.dispose_engines()

def get_connection(store_id):
    return conn_pool.get_connection(store_id=store_id)

def get_session(store_id):
    return conn_pool.get_session(store_id=store_id)

def object_as_dict(obj):
    return {c.key: getattr(obj, c.key)
            for c in inspect(obj).mapper.column_attrs}

def fetch_all(session, model):
    query = session.query(model)
    result = []
    for user in query:
        result.append(object_as_dict(user))
    return result