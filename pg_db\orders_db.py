import datetime
from sqlalchemy import Column, DateTime, String, Integer, Boolean, Float, text, BIGINT
from sqlalchemy import func, delete
import pg_db as db

class ExpressOrders(db.Base):
    __tablename__ = db.express_orders_table

    order_id = Column(Integer, primary_key=True)
    customer_id = Column(Integer)
    cart_id = Column(String)

class Orders(db.Base):
    __tablename__ = db.orders_table
    
    order_id = Column(Integer, primary_key=True)
    order_status_id = Column(Integer)
    order_status = Column(String)
    order_archived = Column(String)
    order_source = Column(String)
    external_source = Column(String)
    external_id = Column(String)
    external_merchant_id = Column(String)
    channel_id = Column(Integer)
    customer_id = Column(Integer)
    cart_id = Column(String)
    payment_status = Column(String)
    payment_provider_id = Column(String)
    payment_method_type = Column(String)
    credit_card_type = Column(String)
    total_items = Column(Integer)
    total_items_shipped = Column(Integer)
    coupon_id = Column(String)
    coupon_code = Column(String)
    coupon_discount = Column(Integer)
    gift_certificate_amount_redeemed = Column(Float)
    store_credit_redeemed = Column(Float)
    sub_total_excluding_tax = Column(Float)
    sub_total_including_tax = Column(Float)
    sub_total_tax = Column(Float)
    base_shipping_cost = Column(Float)
    shipping_excluding_tax = Column(Float)
    shipping_including_tax = Column(Float)
    shipping_tax = Column(Float)
    base_handling_cost = Column(Float)
    handling_excluding_tax = Column(Float)
    handling_including_tax = Column(Float)
    handling_tax = Column(Float)
    base_wrapping_cost = Column(Float)
    wrapping_excluding_tax = Column(Float)
    wrapping_including_tax = Column(Float)
    wrapping_tax = Column(Float)
    total_tax = Column(Float)
    total_excluding_tax = Column(Float)
    total_including_tax = Column(Float)
    default_currency_code = Column(String)
    currency_exchange_rate = Column(Float)
    refund_amount = Column(Float)
    date_shipped = Column(DateTime, default=None)
    order_created_date_time = Column(DateTime)
    last_updated_datetime = Column(DateTime)
    order_day = Column(Integer)
    order_month = Column(String)
    order_year = Column(Integer)
    order_qtr = Column(String)
    updated_by = Column(String)
    
    def __repr__(self):
        return f'Orders {self.order_id}'
    
    @classmethod
    def get_last_modified_at(cls, store_id, session=None):
        local_session = None
        if not session:
            session = db.get_session(store_id)
            local_session = session
        try:
            q = session.query(func.max(Orders.last_updated_datetime)).first()
            last_modified_at = None
            if q and len(q) > 0:
                last_modified_at = q[0]
            return last_modified_at
        finally:
            if local_session:
                local_session.close()

class OrderBillingAddress(db.Base):
    __tablename__ = db.order_billing_addresses_table
    
    order_id = Column(Integer, primary_key=True)
    first_name = Column(String)
    last_name = Column(String)
    company = Column(String)
    street_1 = Column(String)
    street_2 = Column(String)
    city = Column(String)
    state = Column(String)
    zip = Column(String)
    country = Column(String)
    country_code = Column(String)
    phone = Column(String)
    email = Column(String)
    last_updated_datetime = Column(DateTime)

    def __repr__(self):
        return f'OrderBillingAddress {self.order_id}'

class OrderShippingAddress(db.Base):
    __tablename__ = db.order_shipping_addresses_table
    
    order_id = Column(Integer, primary_key=True)
    shipping_address_id = Column(Integer, primary_key=True)
    first_name = Column(String)
    last_name = Column(String)
    company = Column(String)
    street_1 = Column(String)
    street_2 = Column(String)
    city = Column(String)
    state = Column(String)
    postal_code = Column(String)
    country = Column(String)
    country_code = Column(String)
    phone = Column(String)
    email = Column(String)
    last_updated_datetime = Column(DateTime)

    def __repr__(self):
        return f'OrderShippingAddress {self.order_id}'


class OrderLineItemDiscount(db.Base):
    __tablename__ = db.order_line_item_discounts_table
    
    order_id = Column(Integer, primary_key=True)
    order_line_item_id = Column(Integer, primary_key=True)
    discount_id = Column(String)
    discount_amount = Column(Float)
    discount_name = Column(String)
    discount_code = Column(String)
    discount_level = Column(String)
    last_updated_datetime = Column(DateTime)

    def __repr__(self):
        return f'OrderLineItemDiscount {self.order_id}'

class OrderLineItems(db.Base):
    __tablename__ = db.order_line_items_table

    order_id = Column(Integer, primary_key=True)
    order_line_item_id = Column(Integer, primary_key=True)
    parent_order_product_id = Column(Integer)
    order_address_id = Column(Integer)
    product_id = Column(Integer)
    variant_id = Column(Integer)
    option_set_id = Column(Integer)
    external_id = Column(String)
    quantity = Column(Integer)
    quantity_shipped = Column(Integer)
    product_price = Column(Float)
    base_cost_price = Column(Float)
    product_price_after_discount = Column(Float)
    price_ex_tax = Column(Float)
    price_inc_tax = Column(Float)
    price_tax = Column(Float)
    base_total = Column(Float)
    product_total_cost = Column(Float)
    total_ex_tax = Column(Float)
    total_inc_tax = Column(Float)
    total_tax = Column(Float)
    is_refunded = Column(Boolean)
    refund_amount = Column(Float)
    return_id = Column(String)
    fixed_shipping_cost = Column(Float)
    base_wrapping_cost = Column(Float)
    wrapping_cost_ex_tax = Column(Float)
    wrapping_cost_inc_tax = Column(Float)
    wrapping_cost_tax = Column(Float)
    is_bundled_product = Column(Boolean)
    last_updated_datetime = Column(DateTime)
    variant_sku = Column(String)
    sv_cost = Column(Float)

    def __repr__(self):
        return f'OrderLineItems {self.order_id}'
    

class NYRestrictedZipcodes(db.Base):
    __tablename__ = db.order_ny_restricted_zipcodes_table
    
    zip_code = Column(Integer, primary_key=True)
    zip_id = Column(BIGINT)
    city_name = Column(String)
    
    def __repr__(self):
        return f'NYRestrictedZipcodes {self.zip_code}'

    @classmethod
    def clear_table(cls, store_id, session=None):
        local_session = None
        if not session:
            session = db.get_session(store_id)
            local_session = session
        try:
            # Check if the table exists in the current schema
            table_exists_query = text(f"""
                SELECT EXISTS (
                    SELECT 1
                    FROM information_schema.tables 
                    WHERE table_name = '{cls.__tablename__}'
                )
            """)
            result = session.execute(table_exists_query).scalar()

            # Execute a raw SQL TRUNCATE statement
            if result:
                session.execute(text(f'TRUNCATE TABLE {cls.__tablename__} RESTART IDENTITY CASCADE'))
                session.commit()
        finally:
            if local_session:
                local_session.close()