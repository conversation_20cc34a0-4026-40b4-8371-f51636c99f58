import pg_db
from pg_db import products_db
from utils import redis_util, store_util
import task
import logging
import traceback
from pg_db_utils import pg_product_util

logger = logging.getLogger()

VARIANTS_VISIBILITY_RULE_TYPE = "variants_visibility"
PRODUCTS_VISIBILITY_RULE_TYPE = "products_visibility"
PRODUCTS_UNHIDE_RULE_TYPE = "products_unhide"
SEND_BACK_IN_STOCK_MAIL_RULE_TYPE = "send_back_in_stock_mail"

def update_visibility_rules(store_id):
    session = pg_db.get_session(store_id)
    try:
        rules = pg_db.fetch_all(session, products_db.VariantsVisibilityRules)
        new_rules = []
        for rule in rules:
            new_rules.append({
                "variant_id": rule["variant_id"],
                "product_id": rule["product_id"],
                "variant_sku": rule["variant_sku"],
                "out_of_stock_days": rule["out_of_stock_days"],
                "is_active": rule["is_active"]
            })
        redis_util.rebuild_variant_visibility_rules(store_id, new_rules)

        rules = pg_db.fetch_all(session, products_db.ProductsVisibilityRules)
        new_rules = []
        for rule in rules:
            new_rules.append(new_rules.append({
                "product_id": rule["product_id"],
                "promotion_threshold_quantity": rule["promotion_threshold_quantity"],
                "promotion_id": rule["promotion_id"],
                "disable_promotion_triggered": rule["disable_promotion_triggered"],
                "hide_product": rule["hide_product"],
                "hide_out_of_stock_days": rule["hide_out_of_stock_days"],
                "hide_product_name_prefix": rule["hide_product_name_prefix"],
                "hide_category_id": rule["hide_category_id"],
                "is_hide_product_triggered": rule["is_hide_product_triggered"]
            }))
        redis_util.rebuild_product_visibility_rules(store_id, new_rules)
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        session.close()

def process_products(store, products=[]):
    db_conn = pg_db.get_connection(store['id'])
    try:
        _products = {}
        _variants = {}
        product_id_list = []
        variant_id_list = []  
        _unhide_products = {}      
        for product in products:
            product_id = product["id"]
            product_id_list.append(str(product_id))
            _products[product_id] = {
                "product_id": product_id,
                "inventory_level": product["inventory_level"],
                "inventory_warning_level": product.get("inventory_warning_level", 0)
            }
            _unhide_products[product_id] = {
                "product_id": product_id,
                "inventory_level": product["inventory_level"],
                "categories": product["categories"],
                "name": product["name"]                
            }           
            variants = product.get("variants", None)
            if variants:
                for variant in variants:
                    variant_id_list.append(str(variant["id"]))
                    _variants[variant["id"]] = {
                        "variant_id": variant["id"],
                        "product_id": product_id,
                        "inventory_level": variant["inventory_level"],
                        "inventory_warning_level": variant.get("inventory_warning_level", 0)
                    }
        
        product_rules = products_db.ProductsVisibilityRules.fetch_rules(", ".join(product_id_list), db_conn)
        product_rules = product_rules.fetchall() 
        variant_rules = products_db.VariantsVisibilityRules.fetch_rules(", ".join(variant_id_list), db_conn)
        variant_rules = variant_rules.fetchall() 

                
        if len(product_rules) > 0:
            matched_products={}
            for rule in product_rules:            
                matched_products[rule[1]] = _products[rule[1]]
                  
            task.execute_rules(store, PRODUCTS_VISIBILITY_RULE_TYPE, matched_products)
            
        if len(variant_rules) > 0:
            matched_variants={}
            for rule in variant_rules:
                matched_variants[rule[1]] = _variants[rule[1]]
                        
            task.execute_rules(store, VARIANTS_VISIBILITY_RULE_TYPE, matched_variants)
         
        if len(_unhide_products) > 0:    
            task.execute_rules(store, PRODUCTS_UNHIDE_RULE_TYPE, _unhide_products)
        
        if len(_variants) > 0:  
            task.execute_rules(store, SEND_BACK_IN_STOCK_MAIL_RULE_TYPE, _variants)

    except Exception as ex:
        logger.error(str(traceback.format_exc()))
    finally:
        db_conn.commit()
        db_conn.close()
    
def execute_rules(store, rule_type, payload):
    if rule_type == VARIANTS_VISIBILITY_RULE_TYPE:
        from rule_engine import variants_visibility_rule_engine
        variants_visibility_rule_engine.execute_rules(store, payload)
    elif rule_type == PRODUCTS_VISIBILITY_RULE_TYPE:
        from rule_engine import products_visibility_rule_engine
        products_visibility_rule_engine.execute_rules(store, payload)
    elif rule_type == PRODUCTS_UNHIDE_RULE_TYPE:
        from rule_engine import products_unhide_rule_engine
        products_unhide_rule_engine.execute_rules(store, payload)
    elif rule_type == SEND_BACK_IN_STOCK_MAIL_RULE_TYPE:
        from rule_engine import product_back_in_stock_mail_rule_engine
        product_back_in_stock_mail_rule_engine.execute_rules(store, payload)

def run_all_rules(store_id):
    store = store_util.get_store_by_id(store_id)
    pg_product_util.trigger_hide_product_action(store)    