import logging
import datetime
from bson import ObjectId
import mongo_db

logger = logging.getLogger()

class ProductListingType:
    preorder_products = "preorder_products"
    new_products = "new_products"
    featured_products = "featured_products"

STORE_INFO_COLLECTION = "store_info"
REDIRECTS_COLLECTION = "redirects"

def _fetch_product_listings(store, type):
    result = None
    db = mongo_db.get_admin_db_client(store)
    coll = db[STORE_INFO_COLLECTION]
    product_ids = coll.find_one({"type": type})
    if product_ids:
        product_ids = mongo_db.process_data(product_ids)
        result = product_ids.get("product_ids", [])   
    return result

def fetch_preorder_products(store):
    return _fetch_product_listings(store, "preorder_products")

def fetch_new_products(store):
    return _fetch_product_listings(store, "new_products")

def fetch_featured_products(store):
    return _fetch_product_listings(store, "featured_products")