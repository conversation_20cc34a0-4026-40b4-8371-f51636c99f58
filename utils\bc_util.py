import requests
import time
from utils import store_util, order_util
import logging
import traceback
import math

logger = logging.getLogger()

def fetch(api_data, url, filters=None, get_all=False):
    request_body = {"method": "GET", "url": url, "query_params": filters}
    if get_all:
        return fetch_all(api_data, request_body)
    else:
        response = process_api(api_data, request_body)
        data = []
        if "data" in response:
            data = response["data"]
        if "data" in response["data"]:
            data = response["data"]["data"]
        return data

def fetch_all(api_data, req_body):
    resources = []
    page = 1
    limit = 250
    query_params = {}
    if "query_params" in req_body and req_body["query_params"]:
        query_params = req_body["query_params"]

    done = False
    while not done:
        query_params["page"] = page
        query_params["limit"] = limit
        req_body["query_params"] = query_params

        response = process_api(api_data, req_body)
        code = response["status_code"]
        data = response["data"]

        # if response has no content.
        if code == 204:
            done = True

        if code == 200:
            # condition for checking is current page is last page or not.
            if isinstance(data, list) and len(data) == 0:
                done = True

            # iterate through each elements and append in resources list.
            if isinstance(data, list):
                for item in data:
                    resources.append(item)

            page += 1

    return resources

def create_bc_webhook(api_data, url, req_body):
    res = call_api(api_data, "POST", url, None , req_body)
    return res

def find_customer_by_email(api_data, email):
    query_params = {"email": email}
    res = call_api(api_data, "GET", "v2/customers", query_params, {})
    customer = None
    if res and res.status_code == 200:
        customer = res.json()
    return customer

def validate_password(api_data, customer_email, password):
    req_body = {"password": password, "email": customer_email}
    res = call_api(
        api_data, "POST", "v3/customers" + "/validate-credentials", {}, req_body
    )
    result = False
    if res and res.status_code == 200:
        res = res.json()
        result = res["is_valid"] == True
    return result

def get_bc_token(api_data, expires_at):
    req_body = {"channel_id": 1, "expires_at": expires_at}
    res = call_api(
        api_data, "POST", "v3/storefront/api-token-customer-impersonation", {}, req_body
    )
    result = None
    if res and res.status_code == 200:
        res = res.json()
        result = res["data"]["token"]
    return result

def get_bc_graphql_token(api_data, expires_at, domain=None):
    req_body = {"channel_id": 1, "expires_at": expires_at}
    # if domain:
    #     req_body["allowed_cors_origins"] = [domain]
    res = call_api(
        api_data, "POST", "v3/storefront/api-token-customer-impersonation", {}, req_body
    )
    result = None
    if res and res.status_code == 200:
        res = res.json()
        result = res["data"]["token"]
    return result

def process_bc_graphql_request(bc_store_url, token, query, customer_id=None):
    headers = {}
    headers["Authorization"] = "Bearer " + token
    headers["Accept"] = "application/json"

    if customer_id:
        headers["X-Bc-Customer-Id"] = str(customer_id)
    if not bc_store_url.endswith("/"):
        bc_store_url = bc_store_url + "/"
    api_url = bc_store_url + "graphql"
    res = requests.post(url=api_url, json={"query": query}, headers=headers)
    content = res.content
    if res.status_code < 500:
        content = res.json()
    return res.status_code, content

def process_bc_api_request(store, req_body, customer_id=None):
    bc_api = store_util.get_bc_api_creds(store)
    res = process_api(api_data=bc_api, req_body=req_body, customer_id=customer_id)
    return res['status_code'], res['data']

def get_bc_api_request_object(url, method="GET", query_param={}, request_body=None):
    return {
        "url": url,
        "query_params": query_param,
        "body": request_body,
        "method": method
    }

# def process_api(api_data, req_body, exclude_meta=True, customer_id=None):
#     query_params = {}
#     if "query_params" in req_body:
#         query_params = req_body["query_params"]

#     body = {}
#     if "body" in req_body:
#         body = req_body["body"]

#     res = call_api(api_data, req_body["method"], req_body["url"], query_params, body, customer_id)

#     response_body = {}
#     retry_count = 0
#     done = False
#     sleep_time = 1
#     while retry_count < 5:
#         try:
#             # While no content ...
#             if res.status_code == 204:
#                 retry_count = 5

#             response_body = res.json()
#             if res.status_code >= 200 and res.status_code <= 299:
#                 retry_count = 5
#         except Exception as e:
#             logger.warning(str(traceback.format_exc()))
#         if retry_count < 4:
#             time.sleep(sleep_time)
#         sleep_time *= 2
#         retry_count += 1
#     if exclude_meta and "meta" in response_body:
#         return {"status_code": res.status_code, "data": response_body["data"]}
#     else:
#         return {"status_code": res.status_code, "data": response_body}

def process_api(api_data, req_body, exclude_meta=True, customer_id=None):
    query_params = {}
    if "query_params" in req_body:
        query_params = req_body["query_params"]

    body = {}
    if "body" in req_body:
        body = req_body["body"]

    retry_count = 0
    max_retries = 5
    sleep_time = 1
    response_body = {}
    res = None

    while retry_count < max_retries:
        try:
            # Make the API call
            res = call_api(api_data, req_body["method"], req_body["url"], query_params, body, customer_id)
            
            # Handle 204 No Content response
            if res.status_code == 204:
                return {"status_code": 204, "data": None}

            # Try to parse JSON response
            try:
                response_body = res.json()
            except requests.exceptions.JSONDecodeError as json_err:
                # Log the error and response content for debugging
                logger.warning(f"Failed to decode JSON response. Status code: {res.status_code}")
                logger.warning(f"Response content: {res.text[:200]}...")  # Log first 200 chars of response
                if retry_count < max_retries - 1:
                    time.sleep(sleep_time)
                    sleep_time *= 2
                    retry_count += 1
                    continue
                else:
                    # On last retry, return empty data instead of raising error
                    return {"status_code": res.status_code, "data": None}

            # Handle successful response
            if 200 <= res.status_code <= 299:
                if exclude_meta and "meta" in response_body:
                    return {"status_code": res.status_code, "data": response_body["data"]}
                else:
                    return {"status_code": res.status_code, "data": response_body}
            
            # Handle non-2xx responses
            if retry_count < max_retries - 1:
                time.sleep(sleep_time)
                sleep_time *= 2
                retry_count += 1
                continue
            else:
                # On last retry, return the error response
                return {"status_code": res.status_code, "data": None}

        except Exception as e:
            logger.error(f"Unexpected error in process_api: {str(e)}")
            logger.error(traceback.format_exc())
            if retry_count < max_retries - 1:
                time.sleep(sleep_time)
                sleep_time *= 2
                retry_count += 1
                continue
            else:
                # On last retry, return error response
                return {"status_code": 500, "data": None}

    # If we get here, all retries failed
    return {"status_code": res.status_code if res else 500, "data": None}

def call_api(api_data, method, url, query_params=None, req_body=None, customer_id=None):
    headers = {}
    if customer_id:
        headers["X-Bc-Customer-Id"] = str(customer_id)
    headers["X-Auth-Client"] = api_data["client_id"]
    headers["X-Auth-Token"] = api_data["access_token"]
    headers["Accept"] = "application/json"
    api_url = "https://api.bigcommerce.com/stores/" + api_data["store_hash"] + "/" + url
    method = method.upper()
    res = None
    if "GET" == method:
        res = requests.get(url=api_url, params=query_params, headers=headers)
    elif "POST" == method or "PUT" == method:
        if "POST" == method:
            res = requests.post(url=api_url, params=query_params, json=req_body, headers=headers)
        else:
            res = requests.put(url=api_url, params=query_params, json=req_body, headers=headers)
    elif "DELETE" == method:
        res = requests.delete(url=api_url, headers=headers)

    return res

def get_order_products_for_audit_report(store, order_id, customer_id):
    url = "v2/orders/" + order_id + "/products"
    req_body = {
        'method': 'get',
        'url': url
    }
    api = store_util.get_bc_api_creds(store)
    order_products = fetch_all(api, req_body)

    variant_ids = []
    if order_products:
        for product in order_products:
            variant_ids.append(product['variant_id'])

        price_list_ids = _get_price_list_ids_to_fetch_accepted_prices(store, customer_id)
        
        accepted_prices = order_util.get_accepted_prices(store['id'], variant_ids, price_list_ids, customer_id) if price_list_ids != [] else {}

    return accepted_prices, order_products

def _get_price_list_ids_to_fetch_accepted_prices(store, customer_id):
    price_list_ids = []
    customer_price_list_and_group = order_util.get_customer_price_list_and_group(store['id'], customer_id)

    if customer_price_list_and_group['status'] == 200:
        customer_data = customer_price_list_and_group['data']
        price_list = customer_data.get('price_list')
        customer_group_id = customer_data.get('customer_group_id')

        # Normalize keys to lowercase for case-insensitive matching
        price_list_mapping = {
            'vip': 52,
            'tcd': 14,
            'mvd': 15,
            'tier pro': 8,
            'tiered 2': 4,
            'tiered 1': 1
        }

        def normalize_price_list(value):
            if not value:
                return ''
            value = value.strip().lower()
            # Handle known typos/alternatives
            if value in ['tired pro', 'tier pro']:
                return 'tier pro'
            elif value in ['tiered 2', 'tier 2']:
                return 'tiered 2'
            elif value in ['tiered 1', 'tier 1']:
                return 'tiered 1'
            return value

        if price_list:
            normalized_price_list = normalize_price_list(price_list)
            if normalized_price_list in price_list_mapping:
                mapped_id = price_list_mapping[normalized_price_list]
                price_list_ids.append(mapped_id)

                # Special case: If VIP (52), also add 13
                if mapped_id == 52:
                    price_list_ids.append(13)
                if mapped_id == 14:
                    price_list_ids.extend([52,13])
                if mapped_id == 15:
                    price_list_ids.extend([52,13])
            else:
                if customer_group_id in [98, 103, 28]:
                    price_list_ids.extend([13, 8])
        else:
            if customer_group_id in [98, 103, 28]:
                price_list_ids.extend([13, 8])

    
    return price_list_ids

def fetch_products_by_ids(store, ids=[]):
    products = []
    include_fields = ["name", "sku", "availability", "is_visible", "price", "inventory_level", "variants"]
    include = ["variants"]
    api = "v3/catalog/products"
    bc_api = store_util.get_bc_api_creds(store)
    # Determine the number of chunks needed
    num_chunks = math.ceil(len(ids) / 50)

    for i in range(num_chunks):
        # Get the current chunk of ids
        chunk_ids = ids[i * 50: (i + 1) * 50]
        query_params = {
            "include": ",".join(include),
            "include_fields": ",".join(include_fields),
            "id:in": ",".join(chunk_ids)
        }
        logger.error(api)
        logger.error(bc_api)
        logger.error(query_params)
        res = call_api(bc_api, "GET", api, query_params=query_params)
        if res.status_code < 299:
            products.extend(res.json()['data'])
        else:
            logger.error(f"Error fetching products for chunk {i+1}/{num_chunks}: {res.status_code}")

    return products

def fetch_bc_product_by_sku(store, sku):        
    url = f"v3/catalog/products"
    bc_api = store_util.get_bc_api_creds(store)
    query_params = { 
        "sku:in": sku, 
        "include":'variants,images,custom_fields',     
        "include_fields": "name,sku,page_title,custom_url,is_visible,images,upc,inventory_level",
                
    }
    product_data = call_api(bc_api, "GET", url,query_params=query_params) 
    product = {}
    if product_data.status_code == 200:  
        product = product_data.json()     
    return product
