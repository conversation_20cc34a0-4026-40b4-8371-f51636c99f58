import time
import datetime
from mongo_db import fetch_one_document_from_storefront_collection
import pg_db_utils
import pandas as pd
import pg_db as db
from pg_db import products_db
import traceback
from plugin import bc_products
import task
from utils import store_util
from sqlalchemy import text
from rule_engine import products_visibility_rule_engine
import logging

logger = logging.getLogger()
PRODUCTS_UNHIDE_RULE_TYPE = "products_unhide"

def fetch_brands(store):
    brands = {}
    page = 1
    while True:
        logger.info(f"Fetching brand page: {page}")
        query_params = {
            "limit": 500,
            "page": page
        }

        req_body = {
            "query_params": query_params,
            "method": "GET",
            "url": "v3/catalog/brands"
        }

        res = pg_db_utils.process_api(req_body, store)

        if res and res['status_code'] == 200 and res['data']:
            if len(res['data']['data']) > 0:
                for row in res['data']['data']:
                    brands[str(row["id"])] = row["name"]
            else:
                break

        page = page + 1

    return brands

def fetch_categories(store):
    categories = {}
    page = 1
    while True:
        logger.info("Fetching categories page: " + str(page))
        query_params = {
            "limit": 500,
            "page": page
        }

        req_body = {
            "query_params": query_params,
            "method": "GET",
            "url": "v3/catalog/categories"
        }

        res = pg_db_utils.process_api(req_body, store)

        if res and res['status_code'] == 200 and res['data']:
            if len(res['data']['data']) > 0:
                for row in res['data']['data']:
                    categories[str(row["id"])] = row["name"]
            else:
                break

        page = page + 1

    return categories

def fetch_variants_shipping_groups_from_bc(store, query_params={}):
    shipping_groups = []
    page = 1
    while True:
        logger.info("Fetching variants shipping groups page: " + str(page))
        query_params["limit"] = 250
        query_params["page"] = page

        req_body = {
            "query_params": query_params,
            "method": "GET",
            "url": "v3/catalog/variants/metafields"
        }

        res = pg_db_utils.process_api(req_body, store)

        if res and res['status_code'] == 200 and res['data']:
            if len(res['data']['data']) > 0:
                
                for row in res['data']['data']:
                    logger.info("Processing data " + str(row["id"]))
                    shipping_groups.append(row)                  
                
                if len(res['data']['data']) < 250:
                    break
            else:
                break
        else:
            break        
        page = page + 1
    process_shipping_groups_data(store['id'], shipping_groups)

def fetch_variant_shipping_group_from_bc(store, product_id=None, variant_id=None, metafield_id=None):
    url = "v3/catalog/products/" + str(product_id) + "/variants/" + str(variant_id) + "/metafields/" + str(metafield_id)
    req_body = {
        "method": "GET",
        "url": url
    }
    res = pg_db_utils.process_api(req_body, store)
    if res and res['status_code'] == 200:
        return res['data']['data']
    else:
        return None

def fetch_product_shipping_group_from_bc(store, product_id=None, metafield_id=None):
    url = "v3/catalog/products/" + str(product_id) + "/metafields/" + str(metafield_id)
    req_body = {
        "method": "GET",
        "url": url
    }
    res = pg_db_utils.process_api(req_body, store)
    if res and res['status_code'] == 200:
        return res['data']['data']
    else:
        return None                      
                        
def _process_bc_product_variants(product, variants_df, variants_visibility_rules_df):
    result = []
    out_of_stock_variants = []
    variants = product.get("variants", [])
    today_date = datetime.date.today()
    today_date_str = str(today_date)
    last_out_of_stock_date = None
    visibility_out_of_stock_variants = []
    visibility_in_stock_variants = []
    for variant in variants:
        variant_sku = variant.get("sku",None)
        variant_id = variant.get("id", None)
        if variant_sku and variant_sku != "":
            variant_options = ""
            flavor = None
            nicotine = None
            coil = None
            if "option_values" in variant:
                for option in variant["option_values"]:
                    option_name = option["option_display_name"]
                    if option_name == "FLAVORS" or option_name == "Choose Your Flavor":
                        flavor = option["label"]
                    elif option_name == "NICOTINE" or option_name == "Choose Your Nicotine":
                        nicotine = option["label"]
                    elif option_name == "Choose Your Coil":
                        coil = option["label"]
                    else:
                        variant_options = option["label"]

            if coil:
                variant_options = coil
            else:
                if flavor:
                    variant_options = flavor
                if nicotine:
                    variant_options = variant_options + ":" + nicotine
            variant['product_id'] = product['id']
            variant['product_name'] = product['name']
            variant['parent_sku'] = product['sku']
            variant['variant_options'] = variant_options

            inventory_level = variant["inventory_level"]
            out_of_stock_date = ""
            
            variant_df = variants_df[variants_df['variants_id']==variant['id']]

            if variant_df.shape[0] > 0:
                ts = variant_df.iloc[0]["out_of_stock_date"]
                if ts:
                    out_of_stock_date = ts.date().isoformat()
                    if out_of_stock_date == "1970-01-01" or str(out_of_stock_date) == 'NaT':
                        out_of_stock_date = ""
            
            osd_days = 0

            if inventory_level == 0:
                if out_of_stock_date == "":
                    out_of_stock_date = today_date_str
                    if not last_out_of_stock_date: 
                        last_out_of_stock_date = today_date
                else:
                    osd = datetime.datetime.strptime(out_of_stock_date, "%Y-%m-%d").date()
                    osd_days = (today_date - osd).days
            elif out_of_stock_date and out_of_stock_date != "" and str(out_of_stock_date) != 'NaT':
                # Add out of stock db
                osd = datetime.datetime.strptime(out_of_stock_date, "%Y-%m-%d").date()
                osd_days = (today_date - osd).days
                if osd_days > 0:
                    logger.info("Inside If")
                    logger.info(f"out_of_stock_date: {out_of_stock_date}")
                    out_of_stock_data = {
                        "product_id": product['id'],
                        "variant_id": variant_id,
                        "sku": variant_sku, 
                        "start_date": out_of_stock_date, 
                        "end_date": today_date_str, 
                        "days": osd_days,
                        "inventory_level": inventory_level}
                    out_of_stock_variants.append(out_of_stock_data)
            
            variant_visibility_rule = variants_visibility_rules_df[variants_visibility_rules_df['variant_id']==variant['id']]            
            check_visibility = False
            if variant_visibility_rule.shape[0] > 0:
                variant_visibility_rule = variant_visibility_rule.iloc[0]
                check_visibility = True
            
            if inventory_level > 0: 
                out_of_stock_date = "" 
                if check_visibility and variant_visibility_rule['is_active']:
                    visibility_in_stock_variants.append(str(variant['id']))
            elif check_visibility and variant_visibility_rule['out_of_stock_days'] <= osd_days and not variant_visibility_rule['is_active']:
                visibility_out_of_stock_variants.append(str(variant['id']))

            variant["out_of_stock_date"] = out_of_stock_date
            result.append(variant)
    return result, out_of_stock_variants, last_out_of_stock_date, visibility_out_of_stock_variants, visibility_in_stock_variants

def process_variant_visibility_rules(store_id, visibility_out_of_stock_variants, visibility_in_stock_variants):
    db_conn = db.get_connection(store_id)
    try:
        if len(visibility_out_of_stock_variants) > 0:
            out_of_stock_variants = ','.join(_variant for _variant in visibility_out_of_stock_variants)  
            products_db.VariantsVisibilityRules.update_out_of_stock_variants(variants=out_of_stock_variants, db_conn=db_conn)

        if len(visibility_in_stock_variants) > 0:
            in_stock_variants = ', '.join(_variant for _variant in visibility_in_stock_variants)  
            products_db.VariantsVisibilityRules.update_in_stock_variants(variants=in_stock_variants, db_conn=db_conn)

    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        db_conn.commit()
        db_conn.close()

def fetch_products(store, last_date_modified=None):
    logger.info("Fetching Categories")
    categories = fetch_categories(store)

    logger.info("Fetching Brands")
    brands = fetch_brands(store)
    
    products = []
    _unhide_products = {}  
    product_categories = []
    custom_fields = []
    variants = []
    out_of_stock_skus = []
    visibility_out_of_stock_variants = []
    visibility_in_stock_variants = []

    db_conn = db.get_connection(store['id'])
    variants_df = None
    variants_visibility_rules_df = None
    try:
        logger.info("Loading variants...")
        variants_df = pd.read_sql_query(f'select * from "{db.variants_table}"',con=db_conn)

        logger.info("Loading variants_visibility_rules...")
        variants_visibility_rules_df = pd.read_sql_query(f'select * from "{db.variants_visibility_rules_table}"',con=db_conn)
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        db_conn.close()
        
    page = 1

    query_params = {
        "limit": 500,
        "page": page,
        "include_fields": "name,sku,upc,categories,brand_id,brand_name,is_visible,availability,price,cost_price,retail_price,sale_price,base_variant_id,inventory_level,total_sold,date_created,date_modified",
        "include": "variants,custom_fields"
    }
    if last_date_modified:
        query_params["date_modified:min"] = last_date_modified

    while True:
        query_params["page"] = page
        req_body = {
            "query_params": query_params,
            "method": "GET",
            "url": "v3/catalog/products"
        }

        res = pg_db_utils.process_api(req_body, store)
        if res and res['status_code'] == 200 and res['data']:
            if len(res['data']['data']) > 0:               
                for row in res['data']['data']:                   
                    brand_name = ""
                    if str(row["brand_id"]) in brands:
                        brand_name = brands[str(row["brand_id"])]

                    row["brand_name"] = brand_name
                    products.append(row)
                    _unhide_products[row["id"]] = {
                        "product_id": row["id"],
                        "inventory_level": row["inventory_level"],
                        "categories": row["categories"],
                        "name": row["name"],
                        "is_visible": row["is_visible"]                
                    } 
                    
                    if "categories" in row:
                        for category in row["categories"]:
                            if str(category) in categories:
                                product_categories.append({
                                    "product_id": row["id"],
                                    "category_id": category,
                                    "category_name":categories[str(category)]
                                })
                    
                    if "custom_fields" in row:
                        for custom_field in row["custom_fields"]:
                            custom_fields.append({
                                "product_id": row["id"],
                                "custom_field_id": custom_field["id"], 
                                "custom_field_name": custom_field["name"], 
                                "custom_field_value": custom_field["value"]
                            })
                    _variants, _out_of_stock_skus, last_out_of_stock_date, _visibility_out_of_stock_variants, _visibility_in_stock_variants \
                          = _process_bc_product_variants(row, variants_df, variants_visibility_rules_df)

                    variants.extend(_variants)
                    visibility_out_of_stock_variants.extend(_visibility_out_of_stock_variants)
                    visibility_in_stock_variants.extend(_visibility_in_stock_variants)
                    out_of_stock_skus.extend(_out_of_stock_skus)
                    row["last_out_of_stock_date"] = last_out_of_stock_date
            else:
                break                

        if len(products) >= 250:                     
            if len(_unhide_products) > 0:                                                            
                task.execute_rules(store, PRODUCTS_UNHIDE_RULE_TYPE, _unhide_products) 

            process_data(store['id'], products, product_categories, custom_fields, variants, out_of_stock_skus)      
            process_variant_visibility_rules(store['id'], visibility_out_of_stock_variants, visibility_in_stock_variants)     
            products = []
            product_categories = []
            custom_fields = []
            variants = []
            out_of_stock_skus = []
            visibility_out_of_stock_variants = []
            visibility_in_stock_variants = []
        time.sleep(0.2)
        page = page + 1
        
    if len(_unhide_products) > 0:                                                            
        # task.submit_task(task.EXECUTE_RULES_TASK, (store, PRODUCTS_UNHIDE_RULE_TYPE, unhide_matched_products))
        task.execute_rules(store, PRODUCTS_UNHIDE_RULE_TYPE, _unhide_products) 
    process_data(store['id'], products, product_categories, custom_fields, variants, out_of_stock_skus)
    process_variant_visibility_rules(store['id'], visibility_out_of_stock_variants, visibility_in_stock_variants)

def build_custom_fields(custom_field):
    return products_db.ProductCustomField(    
        product_id = custom_field["product_id"],
        custom_field_id = custom_field["custom_field_id"],
        custom_field_name = custom_field["custom_field_name"],
        custom_field_value = custom_field["custom_field_value"]
    )

def build_product_category(category):
    return products_db.ProductCategory(    
        category_id = category["category_id"],
        product_id = category["product_id"],
        category_name = category["category_name"]
    )

def build_product_model(product):
    
    date_created = pd.to_datetime(product['date_created'])
    date_modified = pd.to_datetime(product['date_modified'])

    created_date_day = date_created.day
    created_date_year = date_created.year
    created_date_month = date_created.month
    created_date_month_str = date_created.strftime("%b")
    created_date_qtr = int((created_date_month - 1) / 3) + 1
    created_date_qtr_str = 'q' + str(created_date_qtr)
    return products_db.Products(
        product_id = product['id'],
        product_name = product["name"],
        sku = product["sku"],
        universal_product_code = product["upc"],
        brand_id = product["brand_id"],
        brand_name = product["brand_name"],
        is_visible = product["is_visible"],
        availability = product["availability"],
        price = product["price"],
        cost_price = product["cost_price"],
        retail_price = product["retail_price"],
        sale_price = product["sale_price"],
        base_variant_id = product["base_variant_id"],
        inventory_level = product["inventory_level"],
        total_sold = product["total_sold"],
        last_out_of_stock_date = product["last_out_of_stock_date"],
        date_created = date_created,
        date_modified = date_modified,
        created_day = created_date_day,
        created_month = created_date_month,
        created_year = created_date_year,
        created_qtr = created_date_qtr,
        created_month_str = created_date_month_str,
        created_qtr_str = created_date_qtr_str,
        out_of_stock_date = product.get("out_of_stock_date", None),
    )

def build_variant_model(variant):
    out_of_stock_date = None
    if variant["out_of_stock_date"] and variant["out_of_stock_date"] != "":
        out_of_stock_date = pd.to_datetime(variant['out_of_stock_date'])

    return products_db.Variants(
        product_id = variant['product_id'],
        product_name = variant["product_name"],
        variants_id = variant["id"],
        variants_sku = variant["sku"],
        parent_sku = variant["parent_sku"],
        variants_sku_id = variant["sku_id"],
        variant_options = variant["variant_options"],
        variants_price = variant["price"],
        variants_cost_price = variant["cost_price"],
        variants_sale_price = variant["sale_price"],
        variants_retail_price = variant["retail_price"],
        variants_upc = variant["upc"],
        variants_inventory_level = variant["inventory_level"],
        out_of_stock_date = out_of_stock_date
    )

def build_out_of_stock_sku(out_of_stock_sku):
    return products_db.OutOfStockSKU(    
        sku = out_of_stock_sku["sku"],
        out_of_stock_start_date = str(out_of_stock_sku["start_date"]),
        out_of_stock_end_date = str(out_of_stock_sku["end_date"]),
        days = out_of_stock_sku["days"],
        product_id = out_of_stock_sku["product_id"],
        variant_id = out_of_stock_sku["variant_id"],
        inventory_level = out_of_stock_sku["inventory_level"]
    )

def update_out_of_stock_date(store_id):
    session = db.get_session(store_id)
    try:
        products_db.Products.update_out_of_stock_date(session)
        products_db.Products.update_in_stock_date(session)
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        session.commit()
        session.close()

def _products_persist_to_db(store_id, products=[]):
    session = db.get_session(store_id)
    try:
        with session.begin():
            for product in products:
                model = build_product_model(product)
                session.merge(model)
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        session.close()

def remove_product_categories_by_product_ids(store_id, product_ids):
    """
    Remove all product categories for the given product IDs.
    This function can be called from multiple places when you need to clean up product categories.

    Args:
        store_id: The store ID
        product_ids: List of product IDs or single product ID to remove categories for
    """
    if not product_ids:
        return

    session = db.get_session(store_id)
    try:
        products_db.ProductCategory.clear_product_categories_by_product_ids(store_id, product_ids, session)
        logger.info(f"Removed product categories for product IDs: {product_ids}")
    except Exception as ex:
        logger.error(f"Error removing product categories for product IDs {product_ids}: {traceback.format_exc()}")
        raise ex
    finally:
        if session:
            session.close()

def _product_categories_persist_to_db(store_id, product_categories=[]):
    session = db.get_session(store_id)
    try:
        # Extract unique product IDs from the product_categories array
        product_ids = list(set([pc.get("product_id") for pc in product_categories if pc.get("product_id")]))

        # Remove existing product categories for these product IDs
        if product_ids:
            products_db.ProductCategory.clear_product_categories_by_product_ids(store_id, product_ids, session)
            logger.info(f"Removed existing product categories for {len(product_ids)} products")

        # Insert new product categories
        for product_category in product_categories:
            model = build_product_category(product_category)
            session.merge(model)

        logger.info(f"Inserted {len(product_categories)} new product categories")
    except Exception as ex:
        logger.error(traceback.format_exc())
        if session:
            session.rollback()
        raise ex
    finally:
        if session:
            session.commit()
            session.close()

def _product_custom_fields_persist_to_db(store_id, custom_fields=[]):
    session = db.get_session(store_id)
    try:
        with session.begin():
            for custom_field in custom_fields:
                model = build_custom_fields(custom_field)
                session.merge(model)
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        if session:
            session.commit()
            session.close()

def _variants_persist_to_db(store_id, variants=[]):
    session = db.get_session(store_id)
    try:
        with session.begin():
            for variant in variants:
                model = build_variant_model(variant)
                session.merge(model)
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        if session:
            session.commit()
            session.close()

def _out_of_stock_skus_persist_to_db(store_id, out_of_stock_skus=[]):
    session = db.get_session(store_id)
    try:
        with session.begin():
            for out_of_stock_sku in out_of_stock_skus:
                model = build_out_of_stock_sku(out_of_stock_sku)
                session.merge(model)
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        if session:
            session.commit()
            session.close()

def process_data(store_id, products, product_categories, custom_fields, variants, out_of_stock_skus):
    if products and len(products) > 0:
        _products_persist_to_db(store_id, products)
    if product_categories and len(product_categories) > 0:
        _product_categories_persist_to_db(store_id, product_categories)
    if custom_fields and len(custom_fields) > 0:
        _product_custom_fields_persist_to_db(store_id, custom_fields)
    if variants and len(variants) > 0:
        _variants_persist_to_db(store_id, variants)
    if out_of_stock_skus and len(out_of_stock_skus) > 0:
        _out_of_stock_skus_persist_to_db(store_id, out_of_stock_skus)       

def update_product_data(store_id):
    store = store_util.get_store_by_id(store_id)
    last_modified_date = None
    session = db.get_session(store_id)
    try:
        last_modified_date = products_db.Products.get_last_modified_at(store_id, session)
        if last_modified_date:
            last_modified_date = last_modified_date.date().isoformat()
            today = datetime.datetime.now().date().isoformat()
            if last_modified_date == today:
                last_modified_date = (datetime.datetime.utcnow() - datetime.timedelta(days=1)).date().isoformat()
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        session.close()

    fetch_products(store, last_modified_date)
    update_out_of_stock_date(store_id=store_id)
    trigger_hide_product_action(store)

def trigger_hide_product_action(store):
    db_conn = db.get_connection(store['id'])
    today_date = datetime.date.today()
    try:
        rules = products_db.ProductsVisibilityRules.fetch_rules('',db_conn)
        out_of_stock_products = {}
        activate_product_rules = []
        out_of_stock_products_rules = {}
        filtered_out_of_stock_products_rules = {}
        for rule in rules:
            if rule[5] and not rule[11]:
                out_of_stock_products[rule[0]] = rule[6]
                out_of_stock_products_rules[rule[0]] = rule
        
        if len(out_of_stock_products) > 0:
            db_products = products_db.Products.get_out_of_stock_date(','.join(map(str, list(out_of_stock_products.keys()))), db_conn)
            for p in db_products:
                out_of_stock_date = p[1]
                if out_of_stock_date:
                    product_id = p[0]
                    out_of_stock_days = out_of_stock_products[product_id]
                    osd_days = (today_date - out_of_stock_date.date()).days
                    if osd_days > out_of_stock_days:
                        activate_product_rules.append(product_id)                        
                        filtered_out_of_stock_products_rules[product_id] = out_of_stock_products_rules[product_id]

        if len(activate_product_rules) > 0:
            products_visibility_rule_engine.actions_to_do_on_hide_products(store, filtered_out_of_stock_products_rules)
            products_db.ProductsVisibilityRules.update_out_of_stock_products(','.join(map(str, activate_product_rules)), db_conn, False)
   
    except Exception as ex:
        logger.error(str(traceback.format_exc()))
    finally:
        db_conn.commit()
        db_conn.close()

def _variants_shipping_groupds_persist_to_db(store_id, dto_list, dto_to_model_convertor):
    session = db.get_session(store_id)
    try:
        # clean up the table before inserting new data
        products_db.VariantsShippingGroups.clear_table(store_id, session)
        for dto in dto_list:
            if 'value' in dto and isinstance(dto['value'], str):
                # Remove the leading and trailing brackets and quotes
                dto['value'] = dto['value'].strip('["]').replace('\\"', '"')
            if dto['value'] and dto['value'] != '' and dto['value'] in ['ends-liquid-with-flavor', 'ends-liquid-with-flavor-no-nicotine', 'dropship-ends-liquid-with-flavor', 'dropship-ends-liquid-with-flavor-no-nicotine']:    
                model = dto_to_model_convertor(dto)
                session.add(model)
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        session.commit()
        session.close()


def build_variants_shipping_groups_model(line_item):
    return products_db.VariantsShippingGroups(
        shipping_group_id=int(line_item['id']),
        shipping_group_key = line_item['key'],
        shipping_group_value = line_item['value'],
        variant_id = int(line_item['resource_id']),
        resource_type = line_item['resource_type']
    )

def process_shipping_groups_data(store_id, shiping_groups):
    if len(shiping_groups) > 0:
        _variants_shipping_groupds_persist_to_db(store_id, shiping_groups, build_variants_shipping_groups_model) 

def update_variants_shipping_groups_table(store_id):
    store = store_util.get_store_by_id(store_id)
    query_params = {
        "key:in": "shipping-groups",
        "include_fields": "resource_id,key,value,resource_type"
    }
    fetch_variants_shipping_groups_from_bc(store, query_params)

def process_variants_shipping_groups_webhook(store_id, metafield_id, variant_id, operation):
    store = store_util.get_store_by_id(store_id)
    if store:
        if metafield_id and variant_id and operation: 
            if operation == 'CREATE' or operation == 'UPDATE':
                variants_shipping_group_created_webhook(store, variant_id, metafield_id, build_variants_shipping_groups_model)
            elif operation == 'DELETE':
                variants_shipping_group_deleted_webhook(store_id, variant_id)

    else:
        logger.error(f"process_variants_shipping_groups_webhook: Invalid store id: {store_id}")
        raise Exception(f"process_variants_shipping_groups_webhook: Invalid store id: {store_id}")
    
def process_products_shipping_groups_webhook(store_id, metafield_id, product_id, operation):
    store = store_util.get_store_by_id(store_id)
    if store:
        variant_id = None
        if metafield_id and product_id and operation:
            variants = bc_products.fetch_bc_product_variants(store, product_id)
            variant_id = variants[0]['id'] if variants else None
            if operation == 'CREATE' or operation == 'UPDATE':
                products_shipping_group_created_webhook(store, metafield_id, product_id, variant_id)
            elif operation == 'DELETE':
                variants_shipping_group_deleted_webhook(store_id, variant_id)

    else:
        logger.error(f"process_products_shipping_groups_webhook: Invalid store id: {store_id}")
        raise Exception(f"process_products_shipping_groups_webhook: Invalid store id: {store_id}")

# operation to update and create shipping group for variant
def variants_shipping_group_created_webhook(store, variant_id, metafield_id, dto_to_model_convertor):
    session = db.get_session(store['id'])
    try:
        product = fetch_one_document_from_storefront_collection(store['id'], 'products', {'variants.id': int(variant_id)})
        if product:
            product_id = product['id']
    
            shiping_group = fetch_variant_shipping_group_from_bc(store, product_id, variant_id, metafield_id)
            if shiping_group:
                if 'value' in shiping_group and isinstance(shiping_group['value'], str):
                    # Remove the leading and trailing brackets and quotes
                    shiping_group['value'] = shiping_group['value'].strip('["]').replace('\\"', '"')
                if shiping_group['value'] and shiping_group['value'] != '' and shiping_group['value'] in ['ends-liquid-with-flavor', 'ends-liquid-with-flavor-no-nicotine', 'dropship-ends-liquid-with-flavor', 'dropship-ends-liquid-with-flavor-no-nicotine']:    
                    # Check if the variant_id exists
                    existing_data = session.query(products_db.VariantsShippingGroups).filter_by(variant_id=int(shiping_group['resource_id'])).first()
                    
                    if existing_data:
                        # Update existing record
                        existing_data.shipping_group_id = int(shiping_group['id'])
                        existing_data.shipping_group_key = shiping_group['key']
                        existing_data.shipping_group_value = shiping_group['value']
                        existing_data.resource_type = shiping_group['resource_type']
                    else:
                        # Add new record if variant_id does not exist
                        model = dto_to_model_convertor(shiping_group)
                        session.add(model)
                else:
                    existing_data = session.query(products_db.VariantsShippingGroups).filter_by(variant_id=int(shiping_group['resource_id'])).first()
                    if existing_data:
                        session.delete(existing_data)
                    
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        session.commit()  # Commit all changes (insertions/updates)
        session.close()   # Close the session

def variants_shipping_group_deleted_webhook(store_id, variant_id):
    session = db.get_session(store_id)
    try:    
        # If the value is not valid, delete the existing record with this variant_id
        existing_variant = session.query(products_db.VariantsShippingGroups).filter_by(variant_id=int(variant_id)).first()
        if existing_variant:
            session.delete(existing_variant)
                    
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        session.commit()  # Commit all changes (insertions/updates)
        session.close()   # Close the session

def products_shipping_group_created_webhook(store, metafield_id, product_id, variant_id):
    conn = db.get_connection(store['id'])
    try:
        if variant_id:

            # Fetch shipping group data
            shipping_group = fetch_product_shipping_group_from_bc(store, product_id, metafield_id)
            
            if shipping_group:
                if 'value' in shipping_group and isinstance(shipping_group['value'], str):
                    # Clean up value by stripping leading/trailing brackets and quotes
                    shipping_group['value'] = shipping_group['value'].strip('["]').replace('\\"', '"')

                # Only proceed if the value is valid
                if shipping_group['value'] and shipping_group['value'] != '' and shipping_group['value'] in [
                    'ends-liquid-with-flavor', 'ends-liquid-with-flavor-no-nicotine',
                    'dropship-ends-liquid-with-flavor', 'dropship-ends-liquid-with-flavor-no-nicotine'
                ]:
                    # Check if the variant shipping group exists
                    check_query = f"SELECT * FROM variants_shipping_groups WHERE variant_id = :variant_id"
                    existing_data = conn.execute(text(check_query), {'variant_id': variant_id}).fetchone()

                    if existing_data:
                        # Update the existing record with a direct query
                        update_query = f"""
                        UPDATE variants_shipping_groups 
                        SET 
                            shipping_group_id = {int(shipping_group['id'])},
                            shipping_group_key = '{shipping_group['key']}',
                            shipping_group_value = '{shipping_group['value']}',
                            resource_type = '{shipping_group['resource_type']}'
                        WHERE variant_id = {int(variant_id)}
                        """
                        conn.execute(text(update_query))
                        conn.commit()

                    else:
                        # Insert a new record if variant_id does not exist
                        insert_query = f"""
                        INSERT INTO variants_shipping_groups (
                            shipping_group_id, shipping_group_key, shipping_group_value, 
                            variant_id, resource_type
                        ) VALUES (
                            {int(shipping_group['id'])}, '{shipping_group['key']}', 
                            '{shipping_group['value']}', {int(variant_id)}, 
                            '{shipping_group['resource_type']}'
                        )
                        """
                        conn.execute(text(insert_query))
                        conn.commit()
                
                else:
                    # If the value is invalid or empty, delete the existing record (if any)
                    delete_query = f"DELETE FROM variants_shipping_groups WHERE variant_id = {int(variant_id)}"
                    conn.execute(text(delete_query))
                    conn.commit()

    except Exception as ex:
        logger.error(traceback.format_exc())

    finally:
        conn.commit()  # Commit all changes (insertions/updates)
        conn.close()

def update_product_categories_only(store_id):
    store = store_util.get_store_by_id(store_id)
    logger.info("Fetching Categories")
    categories = fetch_categories(store)
    
    products = []
    product_categories = []
        
    page = 1

    query_params = {
        "limit": 500,
        "page": page,
        "include_fields": "name,sku,upc,categories,is_visible,inventory_level",
        "include": "variants"
    }
    while True:
        query_params["page"] = page
        req_body = {
            "query_params": query_params,
            "method": "GET",
            "url": "v3/catalog/products"
        }

        res = pg_db_utils.process_api(req_body, store)
        if res and res['status_code'] == 200 and res['data']:
            logger.info(f"Processing {len(res['data']['data'])} products, page {page}")
            if len(res['data']['data']) > 0:               
                for row in res['data']['data']:                   
                    products.append(row)
                    if "categories" in row:
                        for category in row["categories"]:
                            if str(category) in categories:
                                product_categories.append({
                                    "product_id": row["id"],
                                    "category_id": category,
                                    "category_name":categories[str(category)]
                                })
            else:
                break                

        if len(products) >= 250:       
            if product_categories:
                logger.info(f"Updating {len(product_categories)} product categories")
                _product_categories_persist_to_db(store_id, product_categories)              
            # process_data(store['id'], products, product_categories, custom_fields, variants, out_of_stock_skus)      
            product_categories = []           
            products = []
        time.sleep(0.2)
        page = page + 1
        
    # process_data(store['id'], products, product_categories, custom_fields, variants, out_of_stock_skus)
    if product_categories:
        logger.info(f"Updating {len(product_categories)} product categories")
        _product_categories_persist_to_db(store_id, product_categories)              
        
    return True
