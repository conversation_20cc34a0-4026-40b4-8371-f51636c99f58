from utils import bc_util, store_util

CART_API_URL = "v3/carts"
CART_ITEMS_API_URL = "v3/carts/{cart_id}/items/{item_id}"
ADD_COUPON_API = "v3/checkouts/{cart_id}/coupons"

def delete_cart(store, cart_id):
    url = CART_API_URL + "/" + str(cart_id)
    bc_api = store_util.get_bc_api_creds(store)
    res = bc_util.call_api(bc_api, "DELETE", url)

def fetch_cart(store, cart_id):
    url = CART_API_URL + "/" + cart_id
    bc_api = store_util.get_bc_api_creds(store)
    return bc_util.call_api(bc_api, "GET", url)