from plugin import bc_brands, bc_category, bc_products
from utils import redis_util, product_util, catalog_util, store_util
from mongo_db import customer_db, clear_updated_field, delete_documents_not_updated
from mongo_db import customer_db, catalog_db, store_info_db
import datetime
import logging
import traceback

logger = logging.getLogger()

def periodic_catalog_update(store_id):
    store = store_util.get_store_by_id(store_id)

    if store:
        update_brands(store_id)
        update_categories(store_id)
        
        last_modified_date = catalog_db.fetch_product_last_modified_date(store)
        if last_modified_date:
            last_modified_date = last_modified_date.date().isoformat()
        else:
            last_modified_date = (datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(days=1)).date().isoformat()

        result, data = bc_products.fetch_all_products(store, last_modified_date)
    else:
        raise Exception(f"Store with store_id {store_id} doesn't exist.")

def update_brands(store_id):
    store = store_util.get_store_by_id(store_id)
    if store:
        catalog_db.clear_product_brands_updated_field(store)
        status, message = bc_brands.fetch_all_brands(store)
        catalog_util.update_brand_cache(store)

def update_categories(store_id):
    store = store_util.get_store_by_id(store_id)
    if store:
        start_time = datetime.datetime.now(datetime.timezone.utc)
        bc_category.fetch_all_categories(store)
        #delete_documents_not_updated(store, catalog_db.CATEGORY_COLLECTION, start_time)

def update_products(store_id):
    store = store_util.get_store_by_id(store_id)
    if store:
        start_time = datetime.datetime.now(datetime.timezone.utc)
        result, data = bc_products.fetch_all_products(store)
        if data and "total" in data and data["total"] > 0:
            delete_documents_not_updated(store, catalog_db.PRODUCTS_COLLECTION, start_time)
            catalog_db.create_index(store)
        update_products_complex_rules(store_id)
        return result, data
    
def update_products_complex_rules(store_id):
    store = store_util.get_store_by_id(store_id)
    if store:
        start_time = datetime.datetime.now(datetime.timezone.utc)
        updated_records = bc_products.fetch_product_complex_rules(store)
        if updated_records > 0:
            delete_documents_not_updated(store, catalog_db.COMPLEX_RULES_COLLECTION, start_time)

def update_product_invetory_cache(store_id):
    store = store_util.get_store_by_id(store_id)
    if store:
        sku_inventory = product_util.get_all_product_inventory(store)
        redis_util.update_sku_invetory_cache(store_id, sku_inventory)
        return True, {"message": "Completed successfully"}
    
def get_unique_product_id(*args):
    items = set()
    for products in args:
        for product in products:
            items.add(product["id"])
    return items

def update_product_cache(store_id):
    store = store_util.get_store_by_id(store_id)    
    if store:
        catalog_util.update_brand_cache(store)
        new_product_ids = store_info_db.fetch_new_products(store)        
        if new_product_ids and len(new_product_ids) > 0:
            status, new_products = bc_products.fetch_products_by_id(store, ids=new_product_ids)
        else:
            status, new_products = bc_products.fetch_new_products(store)

        featured_product_ids = store_info_db.fetch_featured_products(store)
        if featured_product_ids and len(featured_product_ids) > 0:
            status, featured_products = bc_products.fetch_products_by_id(store, ids=featured_product_ids)
        else:
            status, featured_products = bc_products.fetch_featured_products(store)

        preorder_product_ids = store_info_db.fetch_preorder_products(store)
        preorder_products = []
        if preorder_product_ids and len(preorder_product_ids) > 0:
            status, preorder_products = bc_products.fetch_products_by_id(store, ids=preorder_product_ids)
        
        status, popular_products = bc_products.fetch_popular_products(store)        
        redis_util.update_product_cache(store_id, new_products, featured_products, popular_products, preorder_products)
        
        logger.info("new_products:" + str(len(new_products)))
        logger.info("featured_products:" + str(len(featured_products)))
        logger.info("popular_products:" + str(len(popular_products)))

        products = get_unique_product_id(new_products, featured_products, popular_products)
        if products and len(products) > 0:
            items = []
            for product_id in products:
                items.append({"product_id": product_id})
            customer_groups = customer_db.fetch_all_customer_group_id(store)
            if customer_groups and len(customer_groups) > 0:
                for g_id in customer_groups:
                    pricing = bc_products.fetch_customer_group_pricing(store, g_id, items)
                    payload = {}
                    for p in pricing:
                        payload[p['product_id']] = p['price']
                    redis_util.update_product_list_pricing(store_id, g_id, payload)    