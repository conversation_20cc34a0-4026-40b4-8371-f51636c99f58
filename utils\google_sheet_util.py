from google.oauth2.service_account import Credentials
from googleapiclient.discovery import build
import mongo_db

def get_google_sheet_service(store_id):
    """
    Fetch and authenticate Google Sheets API client based on the store ID.
    """
    store = mongo_db.get_store_by_id(store_id)
    service_account_info = store['apps']['google']['service_account']
    scopes = store['apps']['google']['google_sheet']['scopes']

    credentials = Credentials.from_service_account_info(
        service_account_info, scopes=scopes
    )
    service = build('sheets', 'v4', credentials=credentials)

    return {
        "service_account_info": service_account_info,
        "service": service.spreadsheets(),
        "spreadsheet_id": store['apps']['google']['google_sheet']['sheet_id'],
        "sheet_name": store['apps']['google']['google_sheet']['sheet_name'],
        "default_range": store['apps']['google']['google_sheet']['default_range']
    }


def fetch_sheet_data(sheet, sheet_id, sheet_name):
    """
    Fetch all data from the Google Sheet to process updates in batch.
    """
    result = sheet.values().get(
        spreadsheetId=sheet_id,
        range=f"{sheet_name}!A:Z"  # Fetch a wide range to include all relevant columns
    ).execute()

    return result.get("values", [])


def prepare_batch_updates(sheet_data, updated_data, sheet_name):
    """
    Prepare batch update requests based on the updated data.
    """
    headers = sheet_data[0]  # Assuming first row contains headers
    data_rows = sheet_data[1:]  # Remaining rows contain data

    batch_requests = []
    sku_index = headers.index("sku") if "sku" in headers else None

    if sku_index is None:
        raise ValueError("SKU column not found in sheet headers.")

    # Create a mapping of SKUs to their respective rows
    sku_to_row = {row[sku_index]: idx + 2 for idx, row in enumerate(data_rows)}  # Row indices start at 2

    for entry in updated_data:
        sku = entry["sku"]

        if sku not in sku_to_row:
            print(f"SKU {sku} not found in sheet.")
            continue

        row_number = sku_to_row[sku]

        # Process each column dynamically from the entry
        for column_name, value in entry.items():
            if column_name.lower() == "sku":  # Skip the SKU column
                continue

            if column_name not in headers:
                print(f"Column '{column_name}' not found in sheet headers.")
                continue

            column_index = headers.index(column_name) + 1  # Convert to 1-based index
            cell_range = f"{sheet_name}!{chr(64 + column_index)}{row_number}"  # Convert column index to letter
            batch_requests.append({
                "range": cell_range,
                "values": [[value]]
            })

    return batch_requests


def execute_batch_updates(sheet, sheet_id, batch_updates):
    """
    Execute batch updates on Google Sheets using batchUpdate API.
    """
    body = {
        "data": batch_updates,
        "valueInputOption": "RAW"
    }
    result = sheet.values().batchUpdate(
        spreadsheetId=sheet_id,
        body=body
    ).execute()

    print(f"{result.get('totalUpdatedCells')} cells updated in batch.")


def sync_batch_updates_to_sheet(updated_data, sheet_info, sheet_name):
    """
    Sync all updates in batch to Google Sheets.
    """
    sheet = sheet_info["service"]
    sheet_id = sheet_info["spreadsheet_id"]

    # Fetch existing sheet data
    sheet_data = fetch_sheet_data(sheet, sheet_id, sheet_name)

    # Prepare batch updates
    batch_updates = prepare_batch_updates(sheet_data, updated_data, sheet_name)

    # Execute batch updates
    if batch_updates:
        execute_batch_updates(sheet, sheet_id, batch_updates)
    else:
        print("No valid updates to process.")


import hashlib
from googleapiclient.discovery import build

def get_latest_revision_id(sheet_id, auth):
    """
    Get the latest revision ID of the Google Sheet.
    """
    drive_service = build("drive", "v3", credentials=auth)
    revisions = drive_service.revisions().list(fileId=sheet_id).execute()
    latest_revision = revisions["revisions"][-1]
    return latest_revision["id"]


def compute_row_hash(row):
    """
    Compute a hash for a given row.
    """
    row_string = "|".join(map(str, row))  # Convert row to a single string
    return hashlib.sha256(row_string.encode()).hexdigest()


def detect_changes_with_hash(sheet_data, previous_hashes):
    """
    Detect changes in sheet data by comparing row hashes using SKU as a key.
    """
    current_hashes = {}
    changes = []

    # Iterate through each row in the sheet data
    for row in sheet_data:
        # Assuming SKU is in the 3rd column (index 2)
        sku = row[2] if len(row) > 2 else None  # Get SKU value (adjust index if necessary)

        if sku is None:
            continue  # Skip if no SKU is found in the row

        row_hash = compute_row_hash(row)  # Compute hash for the row data

        # Store the current hash with SKU as the key
        current_hashes[sku] = row_hash

        # Check if the hash has changed for this SKU
        if sku in previous_hashes:
            if previous_hashes[sku] != row_hash:
                changes.append({"sku": sku, "data": row})  # Mark this row as changed
        else:
            # If SKU is not found in previous_hashes, it's a new row
            changes.append({"sku": sku, "data": row})

    # Return the changed rows based on SKU
    changed_rows = [
        {"sku": change["sku"], "data": {f"column_{i+1}": value for i, value in enumerate(change["data"])}}
        for change in changes
    ]

    return changed_rows, current_hashes


# Initialize previous states
previous_hashes = {}

def track_and_sync_changes(store_id):
    """
    Track and return changes in the Google Sheet using only the Sheets API.
    """
    global previous_hashes  # Declare global variable before use

    # Step 1: Fetch sheet information and authenticate
    sheet_info = get_google_sheet_service(store_id)

    # Step 2: Fetch current sheet data
    sheet_service = sheet_info["service"]
    sheet_data = fetch_sheet_data(sheet_service, sheet_info["spreadsheet_id"], sheet_info["sheet_name"])

    # Step 3: Detect changes using SKU-based hashing
    changes, current_hashes = detect_changes_with_hash(sheet_data, previous_hashes)

    # Step 4: Prepare batch updates if changes exist
    if changes:
        updated_data = [
            {
                "sku": row["sku"],  # Use SKU from the changes
                **row["data"]  # Merge the rest of the data dynamically
            }
            for row in changes
        ]
        # Uncomment the next line to perform the update
        # sync_batch_updates_to_sheet(updated_data, sheet_info, sheet_info["sheet_name"])

    # Step 5: Update hashes
    previous_hashes = current_hashes

    print(f"Changes detected and synced: {changes}")
    return changes, current_hashes