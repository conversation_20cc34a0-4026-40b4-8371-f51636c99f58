import pg_db
from pg_db import products_db
import datetime
import logging
import traceback
from plugin import bc_products
from pg_db_utils import pg_skuvault_util

logger = logging.getLogger()

def execute_rules(store, variants):
    db_conn = pg_db.get_connection(store['id'])
    today_date = datetime.date.today()
    try:
        activate_variant_rules = []
        deactivate_variant_rules = []
        out_of_stock_variants = {}
        out_of_stock_variants_rules = {}
        filtered_out_of_stock_variants_rules = {}

        rules = products_db.VariantsVisibilityRules.fetch_rules(','.join(map(str, list(variants.keys()))), db_conn)

        for rule in rules:                     
            variant_id = rule[1]
            out_of_stock_days = rule[2]
            is_active = rule[5]
            is_executed = rule[6]
            executed_at = rule[7]
            variant = variants.get(variant_id, None)            
            if variant:                
                if variant['inventory_level'] > 0 and is_executed:
                    deactivate_variant_rules.append(variant['variant_id'])
                elif is_active and variant['inventory_level'] == 0 and not is_executed:
                    out_of_stock_variants[variant['variant_id']] = out_of_stock_days 
                    out_of_stock_variants_rules[variant['variant_id']] = rule                   
       
        if len(out_of_stock_variants) > 0:
            db_variants = products_db.Variants.get_out_of_stock_date(','.join(map(str, list(out_of_stock_variants.keys()))), db_conn)
            for v in db_variants:
                out_of_stock_date = v[1]
                if out_of_stock_date:
                    variant_id = v[0]
                    out_of_stock_days = out_of_stock_variants[variant_id]
                    osd_days = (today_date - out_of_stock_date.date()).days
                    if osd_days > out_of_stock_days:
                        activate_variant_rules.append(variant_id)
                        filtered_out_of_stock_variants_rules[variant_id] = out_of_stock_variants_rules[variant_id]
         
        if len(deactivate_variant_rules) > 0:
            products_db.VariantsVisibilityRules.update_in_stock_variants(','.join(map(str, deactivate_variant_rules)), db_conn)

        if len(activate_variant_rules) > 0:
            actions_to_do_on_disable_variants(store, filtered_out_of_stock_variants_rules)
            products_db.VariantsVisibilityRules.update_out_of_stock_variants(','.join(map(str, activate_variant_rules)), db_conn)

    except Exception as ex:
        logger.error(str(traceback.format_exc()))
    finally:
        db_conn.commit()
        db_conn.close()
    

def actions_to_do_on_disable_variants(store, filtered_variants):
    for rule in filtered_variants.values():        
        updated_name = rule[4] + ' ' +  rule[8]                           
                
        # add name prefix and change category
        bc_products.change_visibility_of_variant(store, rule[1], rule[9], True)         

        # set classifictions
        change_classifications(store, rule[3], rule[0], rule[9], updated_name)          


def change_classifications(store, classification, variant_sku, product_id, updated_name):  
    db_conn = pg_db.get_connection(store['id'])
    try:  
        items = []                       
        s_vault = {}
        s_vault['Sku'] = variant_sku
        s_vault['Classification'] = classification
        s_vault['Description'] = updated_name
        items.append(s_vault)

        pg_skuvault_util.update_sku_vault_classifications(str(store["id"]), items) 
    except Exception as ex:
        logger.error(str(traceback.format_exc()))
    finally:
        db_conn.commit()
        db_conn.close()



