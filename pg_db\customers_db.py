from sqlalchemy import Column, DateTime, String, Integer, Float, BigInteger
from sqlalchemy import func, text
import pg_db as db

class Customers(db.Base):
    __tablename__ = db.customers_table
    
    customer_id = Column(Integer, primary_key=True)
    first_name = Column(String)
    last_name = Column(String)
    company = Column(String)
    email = Column(String)
    phone = Column(String)
    notes = Column(String)
    accepts_product_review_abandoned_cart_emails = Column(String)
    tax_exempt_category = Column(String)
    registration_ip_address = Column(String)
    store_credit_in_USD = Column(Float)
    customer_group_id = Column(Integer)
    customer_group_name = Column(String)
    date_created = Column(DateTime)
    date_modified = Column(DateTime)
    created_day = Column(Integer)
    created_month = Column(String)
    created_year = Column(Integer)
    created_qtr = Column(String)

    def __repr__(self):
        return f'Customer {self.customer_id}'
    
    @classmethod
    def get_last_modified_at(cls, store_id, session=None):
        local_session = None
        if not session:
            session = db.get_session(store_id)
            local_session = session
        try:
            q = session.query(func.max(Customers.date_modified)).first()
            last_modified_at = None
            if q and len(q) > 0:
                last_modified_at = q[0]
            return last_modified_at
        finally:
            if local_session:
                local_session.close()
        

    @classmethod
    def get_customer(cls, store_id, customer_id, session=None):
        local_session = None
        if not session:
            session = db.get_session(store_id)
            local_session = session
        try:
            customer = session.query(Customers).get(customer_id)
            return customer
        finally:
            if local_session:
                local_session.close()
        

class CustomerAddresses(db.Base):
    __tablename__ = db.customer_addresses_table
    
    customer_id = Column(Integer)
    customer_address_id = Column(Integer, primary_key=True)
    first_name = Column(String)
    last_name = Column(String)
    company = Column(String)
    address_line_1 = Column(String)
    address_line_2 = Column(String)
    city = Column(String)
    state = Column(String)
    postal_code = Column(String)
    country_code = Column(String)
    address_type = Column(String)
    phone = Column(String)

    def __repr__(self):
        return f'CustomerAddresses {self.customer_address_id}'

    @classmethod
    def get_customer_address(cls, store_id, customer_address_id, session=None):
        local_session = None
        if not session:
            session = db.get_session(store_id)
            local_session = session
        try:
            customer_address = session.query(CustomerAddresses).get(customer_address_id)
            return customer_address
        finally:
            if local_session:
                local_session.close()
        

class CustomerFormFields(db.Base):
    __tablename__ = db.customer_form_fields_table
    
    customer_id = Column(Integer, primary_key=True)
    custom_field_name = Column(String, primary_key=True)
    custom_field_value = Column(String)
    
    def __repr__(self):
        return f'CustomerFormFields {self.customer_id} {self.custom_field_name}'

    @classmethod
    def get_custom_field(cls, store_id, customer_id, field_name, session=None):
        local_session = None
        if not session:
            session = db.get_session(store_id)
            local_session = session
        try:
            field = session.query(CustomerFormFields).get({"customer_id": customer_id, "custom_field_name": field_name})
            return field
        finally:
            if local_session:
                local_session.close()

class CustomerLoginSessions(db.Base):
    __tablename__ = db.customer_login_sessions_table
    
    customer_id = Column(Integer, primary_key=True)
    login_timestamp = Column(DateTime, primary_key=True)
    ip_address = Column(String)
    city = Column(String)
    state = Column(String)
    country = Column(String)
    time_zone = Column(String)


class CustomerCarts(db.Base):
    __tablename__ = db.customer_carts
    
    customer_id = Column(Integer, primary_key=True) 
    channel = Column(String, primary_key=True)    
    bc_cart_id = Column(String)
    cart_line_items = Column(Integer)
    cart_value = Column(Float)
    total_quantity = Column(Integer)
    last_activity_timestamp = Column(DateTime)

class CartLineItemsInventory(db.Base):
    __tablename__ = db.cart_line_items_inventory
    
    id = Column(Integer, primary_key=True)
    variant_id = Column(BigInteger)
    product_id = Column(BigInteger)
    customer_id = Column(BigInteger)
    variant_sku = Column(String)
    product_name = Column(String, nullable=True)
    cart_quantity = Column(Integer, default=0)
    bc_cart_id = Column(String, nullable=True)

    def __repr__(self):
        return f'CartLineItems {self.id}'
    
    @classmethod
    def clear_table(cls, store_id, session=None):
        local_session = None
        if not session:
            session = db.get_session(store_id)
            local_session = session
        try:
            # Check if the table exists in the current schema
            table_exists_query = text(f"""
                SELECT EXISTS (
                    SELECT 1
                    FROM information_schema.tables 
                    WHERE table_name = '{cls.__tablename__}'
                )
            """)
            result = session.execute(table_exists_query).scalar()

            # Execute a raw SQL TRUNCATE statement
            if result:
                session.execute(text(f'TRUNCATE TABLE {cls.__tablename__} RESTART IDENTITY CASCADE'))
                session.commit()
        finally:
            if local_session:
                local_session.close()
