import logging
import mongo_db
from pymongo import TEXT
import traceback
from flask import json
from bson import json_util
from bson import ObjectId

logger = logging.getLogger()

PRODUCTS_COLLECTION = "my_products"
PRODUCT_COLLECTION = "products"
MSDA_PMTA_ATTACHMENTS_COLLECTION = "msda_pmta_attachments"

def fetch_product_details_by_id(store, product_id):
    db = mongo_db.get_store_db_client(store)
    product_details = db[PRODUCTS_COLLECTION].find_one({"_id": ObjectId(product_id)})
    return product_details

def update_product_document(store, query={}, update_data={}):
    dbclient = mongo_db.get_store_db_client(store)
    return dbclient[PRODUCTS_COLLECTION].update_one(query, update_data)

def get_product_images_by_id(store, ids):
    db = mongo_db.get_store_db_client(store)
    cur = db[PRODUCT_COLLECTION].find(
        {"id": {"$in": ids}},
        {
            "id": 1,
            "images": 1
        }
    )

    document_list = []
    for doc in cur:
        product = {
            "id": doc.get("id"),
            "images": doc.get("images", [])
        }
        document_list.append(product)

    return document_list