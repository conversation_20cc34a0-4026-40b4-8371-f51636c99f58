import logging
import mongo_db
from pymongo import TEXT
import traceback
from flask import json
from bson import json_util
from datetime import datetime, timezone

logger = logging.getLogger()

CUSTOMERS_COLLECTION = "customers"
CUSTOMER_GROUPS_COLLECTION = "customer_groups"
LOYALTY_HISTORY_COLLECTION = "loyalty_history"
CUSTOMER_COUPONS_COLLECTION = "customer_coupons"
CUSTOMER_GROUPS_COLLECTION = "customer_groups"

def clear_customers_update_field(store):
    mongo_db.clear_updated_field(store, CUSTOMERS_COLLECTION)

def clear_customer_groups_update_field(store):
    mongo_db.clear_updated_field(store, CUSTOMER_GROUPS_COLLECTION)

def fetch_customer_by_id(store, customer_id):
    db = mongo_db.get_store_db_client(store)
    customer = db[CUSTOMERS_COLLECTION].find_one({"_id": customer_id})
    return customer

def fetch_customer_group_name(store, customer_group_id):
    db = mongo_db.get_store_db_client(store)
    customer_group = db[CUSTOMER_GROUPS_COLLECTION].find_one({"id": int(customer_group_id)})
    return customer_group['name']

def fetch_all_customer_group_id(store):
    db = mongo_db.get_store_db_client(store)
    customer_groups = db[CUSTOMER_GROUPS_COLLECTION].find({},{"_id":1})
    groups = []
    for g_id in customer_groups:
        groups.append(g_id['_id'])
    return groups

def insert_into_customer(store, data):
    db = mongo_db.get_store_db_client(store)
    doc = db[CUSTOMERS_COLLECTION].insert_one(data)

def remove_customer_by_id(store, customer_id):
    db = mongo_db.get_store_db_client(store)
    doc = db[CUSTOMERS_COLLECTION].delete_one({'_id': int(customer_id)})

def add_customer_loyalty_history(store, data):
    db = mongo_db.get_store_db_client(store)
    doc = db[LOYALTY_HISTORY_COLLECTION].insert_one(data)    
    
def create_search_index(store):
    try:
        db = mongo_db.get_store_db_client(store)
        index = db[CUSTOMERS_COLLECTION].create_index([('customer_search_field', TEXT)], default_language='english')
    except Exception as ex:
        logger.error(traceback.format_exc())

def fetch_customer_coupon_by_id(store, customer_id):
    db = mongo_db.get_store_db_client(store)
    customer_coupons = db[CUSTOMER_COUPONS_COLLECTION].find_one({"customer_id": int(customer_id)})
    return customer_coupons

def update_customer_points_by_id(store, data):
    customer_id = data['customer_id']
    db = mongo_db.get_store_db_client(store)
    doc = db[CUSTOMER_COUPONS_COLLECTION].update_one({"customer_id": int(customer_id)}, { "$set": data }, upsert=True)    

def update_customer_coupon_status(store, data):   
    try:     
        customer_id = data['customer_id']
        coupon_id = data['coupon_id']
        new_enabled_value = False    
        db = mongo_db.get_store_db_client(store)
        coupon_data = db[CUSTOMER_COUPONS_COLLECTION].find_one({"customer_id": int(customer_id), "coupons.coupon_id": int(coupon_id)})        
        if coupon_data and coupon_data is not None:
            update_result  = db[CUSTOMER_COUPONS_COLLECTION].update_one({"customer_id": int(customer_id), "coupons.coupon_id": int(coupon_id)}, { "$set": {"coupons.$.enabled": new_enabled_value, "coupons.$.usage_date": int(datetime.now(timezone.utc).timestamp())} }, upsert=True)              
            updated_count = update_result.modified_count            
            if updated_count > 0:                
                logger.info(f"Coupon status updated for customer {customer_id}, coupon {coupon_id}")
            else:                
                logger.warning(f"No coupons updated for customer {customer_id}, coupon {coupon_id}")
        else:            
            logger.warning(f"No matching coupon found for customer {customer_id}, coupon {coupon_id}")
    except Exception as e:
        logger.error(str(traceback.format_exc()))

def update_customer_loyalty_history(store, query, update):            
    db = mongo_db.get_store_db_client(store)
    doc = db[LOYALTY_HISTORY_COLLECTION].update_one(query, update, upsert=True)    

def get_customer_loyalty_history(store, query):     
    db = mongo_db.get_store_db_client(store)
    history = db[LOYALTY_HISTORY_COLLECTION].find(query)  
    data = []
    if history:
        data = json.loads(json_util.dumps(history))  
    return data

def create_customer_with_coupon(store, data):
    db = mongo_db.get_store_db_client(store)
    result = db[CUSTOMER_COUPONS_COLLECTION].insert_one(data)
    return result


