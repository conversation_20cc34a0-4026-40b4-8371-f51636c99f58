from flask import Flask
import logging

logger = logging.getLogger()

class AppConfig:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            logger.info('Creating the AppConfig')
            cls._instance = super(AppConfig, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        logger.info("Entering AppConfig")
        self.app = Flask(__name__)
        self.app.config.from_object("config")
        logger.info("Exiting AppConfig")
    
    def get_app(self):
        return self.app

appConfig = AppConfig()

def get_api_prefix():
    return appConfig.get_app().config["API_PREFIX"]

def get_celery_broker_redis_host():
    return appConfig.get_app().config["CELERY_BROKER_REDIS_HOST"]

def get_celery_broker_redis_port():
    return appConfig.get_app().config["CELERY_BROKER_REDIS_PORT"]

def get_celery_broker_redis_db():
    return appConfig.get_app().config["CELERY_BROKER_REDIS_DB"]

def get_celery_broker_url():
    broker_url = "redis://" + get_celery_broker_redis_host() + ":" + get_celery_broker_redis_port() + "/" + get_celery_broker_redis_db()
    return broker_url

def get_celery_result_backend():
    return get_celery_broker_url()

def get_tenant_db_name():
    return appConfig.get_app().config["TENANT_DB_NAME"]

def get_mongodb_conn_str():
    return appConfig.get_app().config["MONGODB_CONN_STRING"]

def get_celery_app_name():
    return appConfig.get_app().config["CELERY_APP_NAME"]

def get_app_profile():
    return appConfig.get_app().config["PROFILE"]

def get_app_env():
    return appConfig.get_app().config["APP_ENV"]