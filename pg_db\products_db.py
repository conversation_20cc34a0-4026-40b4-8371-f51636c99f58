import datetime
from sqlalchemy import Column, DateTime, String, Integer, Boolean, Float, UniqueConstraint, Date, BigInteger, ForeignKey
from sqlalchemy import func, text
import pg_db as db
from sqlalchemy.exc import IntegrityError
import logging
import traceback

logger = logging.getLogger()

class Products(db.Base):
    __tablename__ = db.products_table
    
    product_id = Column(Integer, primary_key=True)
    product_name = Column(String)
    sku = Column(String)
    universal_product_code = Column(String)
    brand_id = Column(Integer)
    brand_name = Column(String)
    is_visible = Column(Boolean)
    availability = Column(String)
    price = Column(Float)
    cost_price = Column(Float)
    retail_price = Column(Float)
    sale_price = Column(Float)
    base_variant_id = Column(Integer)
    inventory_level = Column(Integer)
    total_sold = Column(Integer)
    last_out_of_stock_date = Column(DateTime)
    date_created = Column(DateTime)
    date_modified = Column(DateTime)
    created_day = Column(Integer)
    created_month = Column(Integer)
    created_month_str = Column(String)
    created_year = Column(Integer)
    created_qtr = Column(Integer)
    created_qtr_str = Column(String)
    out_of_stock_date = Column(DateTime)
    
    def __repr__(self):
        return f'Products {self.product_id}'
    
    @classmethod
    def get_last_modified_at(cls, store_id, session=None):
        local_session = None
        if not session:
            session = db.get_session(store_id)
            local_session = session
        try:
            q = session.query(func.max(Products.date_modified)).first()
            last_modified_at = None
            if q and len(q) > 0:
                last_modified_at = q[0]
            return last_modified_at
        finally:
            if local_session:
                local_session.close()
        
    
    @classmethod
    def get_products(cls, store_id, product_ids=(), session=None):
        local_session = None
        if not session:
            session = db.get_session(store_id)
            local_session = session
        try:
            products = session.query(Products).filter(Products.product_id.in_(product_ids)).all()
            return products
        finally:
            if local_session:
                local_session.close()
        
    
    @classmethod
    def update_out_of_stock_date(cls, session):
        update_sql = f"""
            UPDATE {cls.__tablename__} 
            SET out_of_stock_date = NOW()
            WHERE inventory_level = 0 AND out_of_stock_date IS NULL;
        """    
        return session.execute(text(update_sql))
        
    @classmethod
    def update_in_stock_date(cls, session):
        update_sql = f"""
            UPDATE {cls.__tablename__} 
            SET out_of_stock_date = NULL
            WHERE inventory_level > 0 AND out_of_stock_date IS NOT NULL;
        """    
        return session.execute(text(update_sql))
    
    @classmethod
    def get_out_of_stock_date(cls, product_id_list, db_conn):
        query = f"""
                    SELECT product_id, out_of_stock_date FROM {cls.__tablename__} 
                    WHERE product_id IN ({product_id_list});
                """        
        products = db_conn.execute(text(query))
        return products

class Variants(db.Base):
    __tablename__ = db.variants_table
    
    variants_id = Column(Integer, primary_key=True) 
    product_id = Column(Integer)
    variants_sku = Column(String)
    variants_sku_id = Column(String)
    product_name = Column(String)
    parent_sku = Column(String)
    variant_options = Column(String)
    variants_price = Column(Float)
    variants_cost_price = Column(Float)
    variants_sale_price = Column(Float)
    variants_retail_price = Column(Float)
    variants_upc = Column(String)
    variants_inventory_level = Column(Integer)
    out_of_stock_date = Column(DateTime)
    
    def __repr__(self):
        return f'Variants {self.variants_id}'

    @classmethod
    def get_variant(cls, store_id, variant_id, session=None):
        local_session = None
        if not session:
            session = db.get_session(store_id)
            local_session = session
        try:
            variant = session.query(Variants).get(variant_id)
            return variant
        finally:
            if local_session:
                local_session.close()

    @classmethod
    def get_variants(cls, store_id, variants_ids=(), session=None):
        local_session = None
        if not session:
            session = db.get_session(store_id)
            local_session = session
        try:
            variants = session.query(Variants).filter(Variants.variants_id.in_(variants_ids)).all()
            return variants
        finally:
            if local_session:
                local_session.close()

    
    @classmethod
    def get_out_of_stock_date(cls, variant_id_list, db_conn):
        query = f"""
                    SELECT variants_id, out_of_stock_date FROM {cls.__tablename__} 
                    WHERE variants_id IN ({variant_id_list});
                """        
        variants = db_conn.execute(text(query))
        return variants
    
    @classmethod
    def get_product_variants(cls, product_id, db_conn):
        query = f"""
                    SELECT variants_id, variants_sku, product_id, parent_sku FROM {cls.__tablename__} 
                    WHERE product_id IN ({product_id});
                """        
        variants = db_conn.execute(text(query))
        return variants

class ProductCategory(db.Base):
    __tablename__ = db.product_categories_table

    category_id = Column(Integer, primary_key=True)
    product_id = Column(Integer, primary_key=True)
    category_name = Column(String)

    def __repr__(self):
        return f'ProductCategory {self.category_id}'

    @classmethod
    def clear_product_categories_by_product_ids(cls, store_id, product_ids, session=None):
        """Remove all product categories for the given product IDs"""
        local_session = None
        if not session:
            session = db.get_session(store_id)
            local_session = session
        try:
            if product_ids:
                # Convert product_ids to a list if it's not already
                if not isinstance(product_ids, list):
                    product_ids = [product_ids]

                # Create placeholders for the IN clause
                placeholders = ','.join([str(pid) for pid in product_ids])

                # Execute delete query
                delete_query = text(f"DELETE FROM {cls.__tablename__} WHERE product_id IN ({placeholders})")
                session.execute(delete_query)
                session.commit()
        except Exception as ex:
            if session:
                session.rollback()
            raise ex
        finally:
            if local_session:
                local_session.close()

class ProductCustomField(db.Base):
    __tablename__ = db.product_custom_fields_table
    
    custom_field_id = Column(Integer, primary_key=True) 
    product_id = Column(Integer, primary_key=True)
    custom_field_name = Column(String)
    custom_field_value = Column(String)
    
    def __repr__(self):
        return f'ProductCustomField {self.custom_field_id}'

class OutOfStockSKU(db.Base):
    __tablename__ = db.out_of_stock_sku_table
    
    sku = Column(String, primary_key=True) 
    out_of_stock_start_date = Column(DateTime, primary_key=True)
    out_of_stock_end_date = Column(DateTime)
    days = Column(Integer)
    product_id = Column(Integer)
    variant_id = Column(Integer)
    inventory_level = Column(Integer)
    
    def __repr__(self):
        return f'OutOfStockSKU {self.sku}'
    

class VariantsVisibilityRules(db.Base):
    __tablename__ = db.variants_visibility_rules_table
        
    variant_sku = Column(String, primary_key=True)
    variant_id= Column(Integer, unique=True)
    product_id = Column(Integer)    
    product_name = Column(String)
    out_of_stock_days = Column(Integer)
    hide_classification = Column(String)
    hide_product_name_prefix = Column(String)
    is_active = Column(Boolean, default=False)
    is_executed = Column(Boolean, default=False)
    executed_at = Column(DateTime)
    last_executed_at = Column(DateTime)
    created_at = Column(DateTime)
    modified_at = Column(DateTime)
    created_by = Column(String)
    modified_by = Column(String)

    __table_args__ = (
        UniqueConstraint('variant_id'),
    )
    
    def __repr__(self):
        return f'OutOfStockSKU {self.sku}'
    
    @classmethod
    def fetch_rules_all(cls, db_conn):
        query = f"""
                    SELECT variant_sku 
                    FROM {cls.__tablename__} ;"""        
        return db_conn.execute(text(query))

    @classmethod
    def fetch_rules(cls, variants, db_conn):
        query = f"""
                    SELECT variant_sku, variant_id, out_of_stock_days, hide_classification, hide_product_name_prefix, is_active, is_executed, executed_at, product_name, product_id  FROM {cls.__tablename__} 
                    WHERE variant_id IN ({variants});
                """        
        return db_conn.execute(text(query))

    @classmethod
    def update_out_of_stock_variants(cls, variants, db_conn):
        update_sql = f"""
                    UPDATE {cls.__tablename__} 
                    SET is_executed = true, executed_at = NOW()
                    WHERE variant_id IN ({variants});
                """        
        db_conn.execute(text(update_sql))

    @classmethod
    def update_in_stock_variants(cls, variants, db_conn):       
        update_sql = f"""
                    UPDATE {cls.__tablename__} 
                    SET is_executed = false, last_executed_at = executed_at, executed_at = NULL
                    WHERE variant_id IN ({variants});
                """        
        db_conn.execute(text(update_sql))


class ProductsVisibilityRules(db.Base):
    __tablename__ = db.products_visibility_rules_table
    
    product_sku = Column(String, primary_key=True)
    product_id= Column(Integer, unique=True)
    product_name = Column(String)
    disable_promotion = Column(Boolean, default=False)
    promotion_threshold_quantity = Column(Integer)    
    promotion_id = Column(Integer)
    promotion_disable_date = Column(Date)
    disable_promotion_triggered = Column(Boolean, default=False)
    disable_promotion_triggered_at = Column(DateTime)
    hide_product = Column(Boolean, default=False)
    hide_out_of_stock_days = Column(Integer)
    hide_product_name_prefix = Column(String)
    hide_category_id = Column(String)
    hide_classification = Column(String)
    customer_rep = Column(String)
    is_hide_product_triggered = Column(Boolean, default=False)
    hide_product_triggered_at = Column(DateTime)
    last_hide_product_triggered_at = Column(DateTime)
    is_active = Column(Boolean, default=True)
    activated_at = Column(DateTime)
    created_at = Column(DateTime)
    modified_at = Column(DateTime)
    created_by = Column(String)
    modified_by = Column(String)

    __table_args__ = (
        UniqueConstraint('product_id'),
    )

    @classmethod
    def fetch_rules_all(cls, db_conn):
        query = f"""
                    SELECT product_sku  
                    FROM {cls.__tablename__} ;"""        
        return db_conn.execute(text(query))

    @classmethod
    def fetch_rules(cls, products, db_conn):
        if products == '':
            query = f"""
                        SELECT product_sku, product_id, product_name, disable_promotion, promotion_threshold_quantity, promotion_id, 
                        disable_promotion_triggered, hide_product, hide_out_of_stock_days,
                        hide_product_name_prefix, hide_category_id, hide_classification, customer_rep, is_hide_product_triggered, is_active, promotion_disable_date 
                        FROM {cls.__tablename__}                        
                    """        
        else:
            query = f"""
                        SELECT product_sku, product_id, product_name, disable_promotion, promotion_threshold_quantity, promotion_id, 
                        disable_promotion_triggered, hide_product, hide_out_of_stock_days,
                        hide_product_name_prefix, hide_category_id, hide_classification, customer_rep, is_hide_product_triggered, is_active, promotion_disable_date 
                        FROM {cls.__tablename__} 
                        WHERE product_id IN ({products});
                    """        
        return db_conn.execute(text(query))
    
    @classmethod
    def update_out_of_stock_products(cls, products, db_conn, is_promotion):
        if is_promotion:
            update_sql = f"""
                    UPDATE {cls.__tablename__} 
                    SET disable_promotion_triggered = true, disable_promotion_triggered_at = NOW()
                    WHERE product_id IN ({products});
                """
        else:
            update_sql = f"""
                    UPDATE {cls.__tablename__} 
                    SET is_hide_product_triggered = true, hide_product_triggered_at = NOW()
                    WHERE product_id IN ({products});
                """        
        db_conn.execute(text(update_sql))

    @classmethod
    def update_in_stock_products(cls, products, db_conn):         
        update_sql = f"""
                UPDATE {cls.__tablename__} 
                SET is_hide_product_triggered = false, last_hide_product_triggered_at = hide_product_triggered_at, hide_product_triggered_at = NULL
                WHERE product_id IN ({products});
            """        
        db_conn.execute(text(update_sql))

class ProductsUnhideRules(db.Base):
    __tablename__ = db.products_unhide_rules_table
    
    product_id = Column(Integer, primary_key=True)
    created_at = Column(DateTime, primary_key=True)    
    product_name = Column(String)
    category = Column(String)
    is_executed = Column(Boolean, default=False)
    executed_date = Column(DateTime)
    is_active = Column(Boolean, default=True)
    created_by = Column(String)

    @classmethod
    def fetch_unhide_rules_all(cls, db_conn):
        query = f"""
                    SELECT product_id  
                    FROM {cls.__tablename__} ;"""        
        return db_conn.execute(text(query))

    @classmethod
    def fetch_unhide_rules(cls, products, db_conn):
        if products == '':
            query = f"""
                        SELECT product_id, product_name, category, is_executed, 
                        executed_date, is_active, created_by, created_at
                        FROM {cls.__tablename__}                        
                    """        
        else:
            query = f"""
                        SELECT product_id, product_name, category, is_executed, 
                        executed_date, is_active, created_by, created_at 
                        FROM {cls.__tablename__} 
                        WHERE product_id IN ({products});
                    """        
        return db_conn.execute(text(query))
    
    @classmethod
    def update_unhide_products(cls, products, db_conn):               
        update_sql = f"""
                UPDATE {cls.__tablename__} 
                SET is_executed = true, executed_date = NOW()
                WHERE product_id IN ({products});
            """        
        db_conn.execute(text(update_sql))

    @classmethod
    def insert_unhide_products(cls, db_conn, product_id, product_name, category, is_executed=True, is_active=True):
        try:
            current_date = datetime.datetime.now()                    
            insert_sql = f"""
                INSERT INTO {cls.__tablename__} 
                (product_id, product_name, category, is_executed, executed_date, is_active, created_by, created_at) 
                VALUES (:product_id, :product_name, :category, :is_executed, :executed_date, :is_active, :created_by, :created_at)
            """
            db_conn.execute(
                text(insert_sql),
                {
                    'product_id': product_id,
                    'product_name': product_name,
                    'category': category,
                    'is_executed': is_executed,
                    'executed_date': current_date,
                    'is_active': is_active,
                    'created_by': None,
                    'created_at': current_date
                }
            )
        except IntegrityError as e:
            logger.error("Duplicate key violation: This product already exists in the rules. " + traceback.format_exc())

class VariantsShippingGroups(db.Base):
    __tablename__ = db.variants_shipping_groups_table
    
    shipping_group_id = Column(Integer, primary_key=True)
    shipping_group_key = Column(String)
    shipping_group_value = Column(String)
    variant_id = Column(Integer) 
    resource_type = Column(String)
    
    def __repr__(self):
        return f'VariantsShippingGroups {self.shipping_group_id}'

    @classmethod
    def clear_table(cls, store_id, session=None):
        local_session = None
        if not session:
            session = db.get_session(store_id)
            local_session = session
        try:
            # Check if the table exists in the current schema
            table_exists_query = text(f"""
                SELECT EXISTS (
                    SELECT 1
                    FROM information_schema.tables 
                    WHERE table_name = '{cls.__tablename__}'
                )
            """)
            result = session.execute(table_exists_query).scalar()

            # Execute a raw SQL TRUNCATE statement
            if result:
                session.execute(text(f'TRUNCATE TABLE {cls.__tablename__} RESTART IDENTITY CASCADE'))
                session.commit()
        finally:
            if local_session:
                local_session.close()

class ProductCustomerPriceMapping(db.Base):
    __tablename__ = db.product_customer_price_mapping_table
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    product_id = Column(BigInteger, nullable=False)
    customer_id = Column(BigInteger, nullable=False)
    price = Column(Float)
    created_at = Column(DateTime, server_default=func.now())
    created_by = Column(String)
    updated_at = Column(DateTime, server_default=func.now())
    updated_by = Column(String)

    def __repr__(self):
        return f'ProductCustomerPriceMapping {self.product_id}'
    
    @classmethod
    def clear_table(cls, store_id, session=None):
        local_session = None
        if not session:
            session = db.get_session(store_id)
            local_session = session
        try:
            # Check if the table exists in the current schema
            table_exists_query = text(f"""
                SELECT EXISTS (
                    SELECT 1
                    FROM information_schema.tables 
                    WHERE table_name = '{cls.__tablename__}'
                )
            """)
            result = session.execute(table_exists_query).scalar()

            # Execute a raw SQL TRUNCATE statement
            if result:
                session.execute(text(f'TRUNCATE TABLE {cls.__tablename__} RESTART IDENTITY CASCADE'))
                session.commit()
        finally:
            if local_session:
                local_session.close()
