cart_form_query = """
query ProductsWithOptionSelections(
  $productIds: [Int!] = product_ids
) {
  site {
    products: products(entityIds: $productIds, first: 50) {
      edges {
        node {
          ...ProductFields
        }
      }
    }
  }
}

fragment ProductFields on Product {
  defaultImage {
    url(width: 300)
  }
  entityId
  sku
  name
  availabilityV2 {
    status
  }
  path
  inventory {
            aggregated {
              warningLevel
              availableToSell
            }
  }
  customFields {
            edges {
              node {
                entityId
                name
                value
              }
            }
  }
  images {
        edges{
          node{
            url(width: 300)
          }
        }
  }
  minPurchaseQuantity
  maxPurchaseQuantity
  
}
"""


def get_query(product_ids):
    x = cart_form_query.replace("product_ids", str(product_ids))
    # x = x.replace("varaint_ids", str(varaint_ids))

    return x
