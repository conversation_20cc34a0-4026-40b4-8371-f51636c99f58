import time
import datetime
from mongo_db import catalog_db
import mongo_db
import pg_db_utils
import pg_db as db
from pg_db import skuvault_db
import pandas as pd
import logging
import traceback
import task
from utils import store_util
from sqlalchemy import update, and_, MetaData, Table
from collections import defaultdict
from bson import ObjectId
from pymongo import UpdateOne
from itertools import islice, chain

logger = logging.getLogger()

# Function to generate one-week intervals
def generate_date_ranges(start, end, delta):
    current = start
    while current < end:
        yield current, min(current + delta, end)
        current += delta

def _sales_model(obj):
    sale_date = None
    date_format = '%Y-%m-%dT%H:%M:%S'

    if obj['SaleDate']:
        dt = obj['SaleDate'].split(".")[0]
        sale_date = datetime.datetime.strptime(dt, date_format)
    
    current_date = datetime.datetime.now(datetime.timezone.utc)

    return skuvault_db.SkuvaultSales(
        skuvault_id = obj['Id'],
        market_place = obj['Marketplace'],
        status = obj['Status'],
        bc_order_id = int(obj['MarketplaceId']),
        sale_date = sale_date,
        created_date = current_date
    )

def process_sales(store_id, sales_data):
    session = db.get_session(store_id)
    filtered_sales = []
    skuvault_sales_ids = {}
    if sales_data:
        for i in sales_data:
            market_place = i["Marketplace"]
            status = i["Status"]
            if market_place == "BigCommerce":
                filtered_sales.append(i)

        skuvault_sales_ids = {str(i["Id"]): int(i["MarketplaceId"]) for i in sales_data if i["Marketplace"] == "BigCommerce"}
    try:
        with session.begin():
            for sales in filtered_sales:
                model = _sales_model(sales)
                session.merge(model)
        
        task.submit_task('update_skuvault_sales_cost', (store_id, {'mapping_ids': skuvault_sales_ids}))
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        if session:
            session.commit()
            session.close()

def update_sku_vault_sales(store_id, days_range=1):
    # session = db.get_session(store_id)
    api_info = store_util.get_skuvault_api_info(store_id)
    tenant_token = api_info['tenant_token']
    user_token = api_info['user_token']

    end_date = datetime.datetime.now(datetime.timezone.utc)
    start_date = end_date - datetime.timedelta(days=days_range)

    # Iterate over each one-week interval
    for from_date, to_date in generate_date_ranges(start_date, end_date, datetime.timedelta(weeks=1)):
        logger.info(f"Processing sales from {from_date} to {to_date}")
        page_number = 0
        retry_count = 0
        page_size = 10000
        while True:
            try:
                body = {
                    "DateField": "Sale",
                    "PageNumber": page_number,
                    "PageSize": page_size,
                    "FromDate": from_date.isoformat(),
                    "ToDate": to_date.isoformat(),
                    "TenantToken": tenant_token,
                    "UserToken": user_token
                }
                res = pg_db_utils.call_api("POST", "https://app.skuvault.com/api/sales/getSalesByDate",
                        {}, body, False)

                response_body = res.json()
                if response_body:
                    _sales = response_body
                    logger.info("sales size: " + str(len(_sales)))
                    if len(_sales) == 0:
                        if retry_count < 3:
                            time.sleep(30)
                            retry_count = retry_count + 1
                            continue
                        else:
                            break

                    retry_count = 0
                    process_sales(store_id, _sales)
                        
                    if len(_sales) < page_size:
                        break
                else:
                    break
                page_number = page_number + 1
            except Exception as e:
                logger.error(traceback.format_exc())
                if retry_count < 3:
                    retry_count = retry_count + 1
                    logger.error(f"Retrying with count {retry_count}")
                else:
                    break


def update_sku_vault_sales_cost(store_id, payload):
    saleIds = []
    if payload:
        # Extracting the keys into a list
        saleIds = list(payload['mapping_ids'].keys())

    if saleIds:
        # Iterate over each one-week interval
        _sales_id = []
        for sale_id in saleIds:
            _sales_id.append(sale_id)
            if len(_sales_id) >= 500:
                _sales_cost = fetch_skuvault_sales_cost(store_id, _sales_id)
                process_sales_cost(store_id, _sales_cost, payload)
                _sales_id = []
        if len(_sales_id) > 0:
            _sales_cost = fetch_skuvault_sales_cost(store_id, _sales_id)
            process_sales_cost(store_id, _sales_cost, payload)

def fetch_skuvault_sales_cost(store_id, sale_ids=[]):
    api_info = store_util.get_skuvault_api_info(store_id)
    tenant_token = api_info['tenant_token']
    user_token = api_info['user_token']
    body = {
        "SaleIds": sale_ids,                
        "TenantToken": tenant_token,
        "UserToken": user_token
    }
    retry_count = 0
    while True:
        try:
            #logger.info(f"Fetching data from sku vault, body {body}")
            res = pg_db_utils.call_api("POST", "https://app.skuvault.com/api/sales/getSaleItemCost",
                {}, body, False)
            response_body = res.json()
            if response_body and 'Sales' in response_body:
                _sales_cost = response_body['Sales']
                logger.info("sales size: " + str(len(_sales_cost)))
                if len(_sales_cost) == 0:
                    if retry_count < 3:
                        time.sleep(30)
                        retry_count = retry_count + 1
                        continue
                    else:   
                        break
            return _sales_cost
        except Exception as e:
            logger.error(traceback.format_exc())
            if retry_count < 3:
                retry_count = retry_count + 1
                logger.error(f"Retrying with count {retry_count}")
            else:
                raise e
    return None

def process_sales_cost(store_id, cost_data, mapping_data):
    session = db.get_session(store_id)
    try:
        mapping_ids = mapping_data['mapping_ids']
        metadata = MetaData()
        order_line_items_table = Table(db.order_line_items_table, metadata, autoload_with=session.get_bind())
        with session.begin():
            if cost_data:
                for data in cost_data:
                    saleId = data['SaleId']
                    bc_id = mapping_ids.get(saleId, None)
                    if bc_id:
                        for item in data['Items']:
                            # model = _cost_model(item, saleId, bc_id)
                            # session.merge(model)

                            # Update sv_cost in orders table
                            session.execute(
                                update(order_line_items_table)
                                .where(
                                    and_(
                                        order_line_items_table.c.order_id == bc_id,
                                        order_line_items_table.c.variant_sku == item['Sku']
                                    )
                                )
                                .values(
                                    sv_cost=item['Cost']['a']
                                )
                            )
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        if session:
            session.commit()
            session.close()

def update_sku_vault_completed_purchase_orders(store_id, status):
    completed_po_data = []
    api_info = store_util.get_skuvault_api_info(store_id)
    tenant_token = api_info['tenant_token']
    user_token = api_info['user_token']

    # Get the latest PO date from MongoDB
    store = store_util.get_store_by_id(store_id)
    db = mongo_db.get_store_db_client(store)
    skuvault_completed_pos = db["skuvault_completed_pos"]
    
    latest_update_pipeline = [
        {"$match": {"Status": status}},  # Filter by status first
        {"$sort": {"ad_updated_at": -1}},         # Then sort by date
        {"$limit": 1},                            # Limit to most recent
        {"$project": {"ad_updated_at": 1}}        # Project only the field you want
    ]
    
    latest_update_result = list(skuvault_completed_pos.aggregate(latest_update_pipeline))
    
    # If we found a document with ad_updated_at, use that as our starting point
    if latest_update_result and "ad_updated_at" in latest_update_result[0]:
        latest_completed_po_date = latest_update_result[0]["ad_updated_at"]
        logger.info(f"Using latest ad_updated_at date: {latest_completed_po_date}")
    else:
        # If no ad_updated_at found, fall back to finding the latest created_at in pos
        # Use a more efficient aggregation approach
        latest_completed_po_pipeline = [
            {
                "$match": {
                    "Status": status
                }
            },
            {
                "$project": {
                    "latest_created_at": {"$toDate": "$CreatedDate"}
                }
            },
            {"$sort": {"latest_created_at": -1}},
            {"$limit": 1}
        ]

        
        latest_completed_po_result = list(skuvault_completed_pos.aggregate(latest_completed_po_pipeline))
        
        if latest_completed_po_result and "latest_created_at" in latest_completed_po_result[0]:
            latest_completed_po_date = latest_completed_po_result[0]["latest_created_at"]
            # logger.info(f"Using latest created_at date from pos: {latest_completed_po_date}")
        else:
            # If no POs exist, use a default date (2023-01-01)
            latest_completed_po_date = datetime.datetime(2023, 1, 1)
            # logger.info("No existing POs found, using default date: 2023-01-01")
    
    # Convert to ISO format if it's a datetime object
    if isinstance(latest_completed_po_date, datetime.datetime):
        latest_completed_po_date = latest_completed_po_date.isoformat()

    page_number = 0
    retry_count = 0
    page_size = 10000
    while True:
        try:
            body = {
                "ModifiedAfterDateTimeUtc": latest_completed_po_date,
                "Status": status,
                "IncludeProducts": False,
                "PageNumber": page_number,
                "TenantToken": tenant_token,
                "UserToken": user_token
            }
            res = pg_db_utils.call_api("POST", "https://app.skuvault.com/api/purchaseorders/getPOs", {}, body, False)

            response_body = res.json()
            if response_body:
                _pos = response_body
                if 'PurchaseOrders' in response_body:
                    completed_po_data.append(response_body['PurchaseOrders'])
                if len(_pos) == 0:
                    if retry_count < 3:
                        time.sleep(30)
                        retry_count = retry_count + 1
                        continue
                    else:
                        break

                retry_count = 0
                    
                if len(response_body['PurchaseOrders']) < page_size:
                    break
            else:
                break
            page_number = page_number + 1
        except Exception as e:
            logger.error(traceback.format_exc())
            if retry_count < 3:
                retry_count = retry_count + 1
                logger.error(f"Retrying with count {retry_count}")
            else:
                break
    all_completed_records = list(chain.from_iterable(completed_po_data))
    filtered_records, excluded_records = _exclude_purchase_orders(all_completed_records)
    if filtered_records:
        process_completed_purchase_orders(store_id, filtered_records)



def update_sku_vault_received_purchase_orders(store_id):
    received_po_data = []
    api_info = store_util.get_skuvault_api_info(store_id)
    tenant_token = api_info['tenant_token']
    user_token = api_info['user_token']

    # Get the latest PO date from MongoDB
    store = store_util.get_store_by_id(store_id)
    db = mongo_db.get_store_db_client(store)
    skuvault_received_pos = db["skuvault_received_pos"]
    
    latest_update_pipeline = [
        {"$sort": {"ad_updated_at": -1}},
        {"$limit": 1},
        {"$project": {"ad_updated_at": 1}}
    ]
    
    latest_update_result = list(skuvault_received_pos.aggregate(latest_update_pipeline))
    
    # If we found a document with ad_updated_at, use that as our starting point
    if latest_update_result and "ad_updated_at" in latest_update_result[0]:
        latest_received_po_date = latest_update_result[0]["ad_updated_at"]
        logger.info(f"Using latest ad_updated_at date: {latest_received_po_date}")
    else:
        # If no ad_updated_at found, fall back to finding the latest created_at in pos
        # Use a more efficient aggregation approach
        latest_received_po_pipeline = [
                    {
                        "$project": {
                            "latest_created_at": {"$toDate": "$ReceivedDate"}
                        }
                    },
                    {"$sort": {"latest_created_at": -1}},
                    {"$limit": 1}
                ]

        
        latest_received_po_result = list(skuvault_received_pos.aggregate(latest_received_po_pipeline))
        
        if latest_received_po_result and "latest_created_at" in latest_received_po_result[0]:
            latest_received_po_date = latest_received_po_result[0]["latest_created_at"]
            # logger.info(f"Using latest created_at date from pos: {latest_received_po_date}")
        else:
            # If no POs exist, use a default date (2023-01-01)
            latest_received_po_date = datetime.datetime(2023, 1, 1)
            # logger.info("No existing POs found, using default date: 2023-01-01")
    
    # Convert to ISO format if it's a datetime object
    if isinstance(latest_received_po_date, datetime.datetime):
        latest_received_po_date = latest_received_po_date.isoformat()

    page_number = 0
    retry_count = 0
    page_size = 10000

    while True:
        try:
            body = {
                "ReceivedAfterDateTimeUTC": latest_received_po_date,
                "PageNumber": page_number,
                "TenantToken": tenant_token,
                "UserToken": user_token
            }
            res = pg_db_utils.call_api("POST", "https://app.skuvault.com/api/purchaseorders/getReceivesHistory", {}, body, False)

            response_body = res.json()
            if response_body:
                _pos = response_body
                if 'Receives' in response_body:
                    received_po_data.append(response_body['Receives'])
                if len(_pos) == 0:
                    if retry_count < 3:
                        time.sleep(30)
                        retry_count = retry_count + 1
                        continue
                    else:
                        break

                retry_count = 0
                    
                if len(response_body['Receives']) < page_size:
                    break
            else:
                break
            page_number = page_number + 1
        except Exception as e:
            logger.error(traceback.format_exc())
            if retry_count < 3:
                retry_count = retry_count + 1
                logger.error(f"Retrying with count {retry_count}")
            else:
                break

    all_received_records = list(chain.from_iterable(received_po_data))

    if all_received_records:
        process_received_purchase_orders(store_id, all_received_records)


def _exclude_purchase_orders(po_records):
    excluded_supplier_names = {"midwest goods returns dept.", "cnc po by packing dept", "checking desk"}
    included_pos = []
    excluded_pos = []

    for po in po_records:
        supplier = (po.get("SupplierName") or "").strip().lower()
        if supplier in excluded_supplier_names:
            excluded_pos.append(po)
        else:
            included_pos.append(po)

    return included_pos, excluded_pos


def process_received_purchase_orders(store_id, po_data):
    store = store_util.get_store_by_id(store_id)
    if po_data:
        for doc in po_data:
            if '_id' not in doc:
                doc['_id'] = ObjectId()
        import task
        task.upsert_documents(store, catalog_db.SKUVAULT_RECEIVED_POS_COLLECTION, po_data)

def process_completed_purchase_orders(store_id, po_data):
    store = store_util.get_store_by_id(store_id)
    if po_data:
        for doc in po_data:
            if '_id' not in doc:
                doc['_id'] = ObjectId()
        import task
        task.upsert_documents(store, catalog_db.SKUVAULT_COMPLETED_POS_COLLECTION, po_data)



def update_sku_vault_purchase_orders(store_id):
    BATCH_SIZE = 1000 # number of SKUs to process per batch

    db = mongo_db.get_store_db_client_for_store_id(store_id)
    update_sku_vault_received_purchase_orders(store_id)
    
    statuses = ["Completed", "PartiallyReceived"]
    for status in statuses:
        update_sku_vault_completed_purchase_orders(store_id, status)

    received_collection = db[catalog_db.SKUVAULT_RECEIVED_POS_COLLECTION]
    completed_collection = db[catalog_db.SKUVAULT_COMPLETED_POS_COLLECTION]
    pos_collection = db[catalog_db.SKUVAULT_POS_COLLECTION]

    # Step 1: Fetch unique SKUs from received POs
    unique_skus = list(received_collection.distinct("SKU"))
    if not unique_skus:
        return
    
    # Step 1.5: Create PO+SKU -> ReceivedDate map from skuvault_received_pos
    received_cursor = received_collection.find(
        {"SKU": {"$in": unique_skus}},
        {"PONumber": 1, "SKU": 1, "ReceivedDate": 1}
    )

    po_sku_received_date_map = {}
    for doc in received_cursor:
        po_num = str(doc.get("PONumber"))
        sku = doc.get("SKU")
        received_date = doc.get("ReceivedDate")
        if po_num and sku and received_date:
            po_sku_received_date_map[(po_num, sku)] = received_date

    def chunked(iterable, size):
        it = iter(iterable)
        while True:
            batch = list(islice(it, size))
            if not batch:
                break
            yield batch


    for sku_batch in chunked(unique_skus, BATCH_SIZE):
        # Step 2: Get completed PO documents with any matching SKU in LineItems.SKU
        completed_pos = list(completed_collection.find(
            {"LineItems.SKU": {"$in": sku_batch}},
            {"PoId": 1, "CreatedDate": 1, "LineItems": 1, "PoNumber": 1}
        ))

        # Step 3: Build mapping of SKU -> PoId -> PO info
        sku_po_map = defaultdict(dict)
        for po_doc in completed_pos:
            po_id = po_doc.get("PoId")
            line_items = po_doc.get("LineItems", [])
            po_number = po_doc.get("PoNumber")

            for item in line_items:
                sku = item.get("SKU")
                if sku not in sku_batch:
                    continue
                if not all(k in item for k in ("Quantity", "ReceivedQuantity", "Cost")) or not po_id:
                    continue
                if item["Quantity"] != item["ReceivedQuantity"]:
                    continue  # Skip if quantity and received quantity are not equal
                
                # Get ReceivedDate from received pos collection
                received_date = po_sku_received_date_map.get((po_number, sku))
                created_at = received_date or po_doc.get("CreatedDate")

                sku_po_map[sku][po_id] = {
                    "created_at": created_at,
                    "quantity": item["Quantity"],
                    "received_quantity": item["ReceivedQuantity"],
                    "cost": item["Cost"]
                }

        # Step 4: Fetch existing skuvault_pos docs to check for already stored PoIds
        existing_docs = list(pos_collection.find({"_id": {"$in": list(sku_po_map.keys())}}, {"pos": 1}))        
        existing_map = {doc["_id"]: doc.get("pos", {}) for doc in existing_docs}

        # Step 5: Prepare bulk operations
        bulk_operations = []

        for sku, po_data in sku_po_map.items():
            existing_pos = existing_map.get(sku, {})
            update_data = {}

            for po_id, details in po_data.items():
                if po_id in existing_pos:
                    continue  # already exists, skip
                update_data[po_id] = details

            if update_data:
                bulk_operations.append(UpdateOne(
                    {"_id": sku},
                    {"$set": {f"pos.{k}": v for k, v in update_data.items()}},
                    upsert=True
                ))

        # Step 6: Execute bulk updates
        if bulk_operations:
            pos_collection.bulk_write(bulk_operations, ordered=False)

    logger.info(f"SKU vault PO update complete")



def update_sku_vault_purchase_ordersold(store_id):
    po_data = []
    api_info = store_util.get_skuvault_api_info(store_id)
    tenant_token = api_info['tenant_token']
    user_token = api_info['user_token']

    # Get the latest PO date from MongoDB
    store = store_util.get_store_by_id(store_id)
    db = mongo_db.get_store_db_client(store)
    skuvault_pos_collection = db["skuvault_pos"]
    
    latest_update_pipeline = [
        {"$sort": {"ad_updated_at": -1}},
        {"$limit": 1},
        {"$project": {"ad_updated_at": 1}}
    ]
    
    latest_update_result = list(skuvault_pos_collection.aggregate(latest_update_pipeline))
    
    # If we found a document with ad_updated_at, use that as our starting point
    if latest_update_result and "ad_updated_at" in latest_update_result[0]:
        latest_po_date = latest_update_result[0]["ad_updated_at"]
        logger.info(f"Using latest ad_updated_at date: {latest_po_date}")
    else:
        # If no ad_updated_at found, fall back to finding the latest created_at in pos
        # Use a more efficient aggregation approach
        latest_po_pipeline = [
            {"$project": {
                "latest_created_at": {
                    "$max": {
                        "$map": {
                            "input": {"$objectToArray": "$pos"},
                            "as": "po",
                            "in": "$$po.v.created_at"
                        }
                    }
                }
            }},
            {"$sort": {"latest_created_at": -1}},
            {"$limit": 1}
        ]
        
        latest_po_result = list(skuvault_pos_collection.aggregate(latest_po_pipeline))
        
        if latest_po_result and "latest_created_at" in latest_po_result[0]:
            latest_po_date = latest_po_result[0]["latest_created_at"]
            # logger.info(f"Using latest created_at date from pos: {latest_po_date}")
        else:
            # If no POs exist, use a default date (2023-01-01)
            latest_po_date = datetime.datetime(2023, 1, 1)
            # logger.info("No existing POs found, using default date: 2023-01-01")
    
    # Convert to ISO format if it's a datetime object
    if isinstance(latest_po_date, datetime.datetime):
        latest_po_date = latest_po_date.isoformat()

    page_number = 0
    retry_count = 0
    page_size = 10000
    while True:
        try:
            body = {
                "ModifiedAfterDateTimeUtc": latest_po_date,
                "Status": "Completed",
                "PageNumber": page_number,
                "IncludeProducts": False,
                "TenantToken": tenant_token,
                "UserToken": user_token
            }
            res = pg_db_utils.call_api("POST", "https://app.skuvault.com/api/purchaseorders/getPOs", {}, body, False)

            response_body = res.json()
            if response_body:
                _pos = response_body
                if 'PurchaseOrders' in response_body:
                    po_data.append(response_body['PurchaseOrders'])
                logger.info("sales size: " + str(len(_pos)))
                if len(_pos) == 0:
                    if retry_count < 3:
                        time.sleep(30)
                        retry_count = retry_count + 1
                        continue
                    else:
                        break

                retry_count = 0
                    
                if len(_pos) < page_size:
                    break
            else:
                break
            page_number = page_number + 1
        except Exception as e:
            logger.error(traceback.format_exc())
            if retry_count < 3:
                retry_count = retry_count + 1
                logger.error(f"Retrying with count {retry_count}")
            else:
                break
    if len(po_data) > 0 and len(po_data[0]) > 0:
        process_purchase_ordersold(store_id, po_data[0])

def process_purchase_ordersold(store_id, po_data):
    grouped_data = defaultdict(dict)
    store = store_util.get_store_by_id(store_id)
    if po_data:
        for po in po_data:
            po_id = po["PoId"]
            created_date = po["CreatedDate"]
            
            for line_item in po.get("LineItems", []):
                sku = line_item["SKU"]
                grouped_data[sku][po_id] = {
                    "created_at": created_date,
                    "quantity": line_item["Quantity"],
                    "received_quantity": line_item["ReceivedQuantity"],
                    "cost": line_item["Cost"]
                }
                
    formatted_data = [{"_id": sku, "pos": pos_dict} for sku, pos_dict in grouped_data.items()]
    if formatted_data:
        import task
        task.upsert_documents(store, catalog_db.SKUVAULT_POS_COLLECTION, formatted_data)

def _cost_model(obj, skuvault_id, bc_order_id):    
    current_date = datetime.datetime.now(datetime.timezone.utc)
    return skuvault_db.SkuvaultCost(
        skuvault_id = skuvault_id,
        bc_order_id = bc_order_id,
        sku = obj['Sku'],
        cost = obj['Cost']['a'],
        created_date = current_date
    )



#==================== below is the R&D code ====================#
def process_and_store_sales_cost(store_id, cost_data, mapping_data):
    session = db.get_session(store_id)
    try:
        if cost_data:
            metadata = MetaData()
            order_line_items_table = Table(db.order_line_items_table, metadata, autoload_with=session.get_bind())
            with session.begin():
                for data in cost_data:
                    saleId = data['SaleId']
                    bc_id = mapping_data.get(saleId, None)
                    if bc_id:
                        for item in data['Items']:
                            # model = _cost_model(item, saleId, bc_id)
                            # session.merge(model)

                            # Update sv_cost in orders table
                            session.execute(
                                update(order_line_items_table)
                                .where(
                                    and_(
                                        order_line_items_table.c.order_id == bc_id,
                                        order_line_items_table.c.variant_sku == item['Sku']
                                    )
                                )
                                .values(
                                    sv_cost=item['Cost']['a']
                                )
                            )
    except Exception as ex:
        logger.error(traceback.format_exc())
    finally:
        if session:
            session.commit()
            session.close()            

def fetch_and_update_sales_cost(store_id, sales={}):
    # session = db.get_session(store_id)
    sale_ids = []
    response_body = None
    for sv_sale_id, bc_order_id in sales.items():
        sale_ids.append(sv_sale_id)
        if len(sale_ids) >= 500:
            _sales_cost = fetch_skuvault_sales_cost(store_id, sale_ids)
            process_and_store_sales_cost(store_id, _sales_cost, sales)
            sale_ids = []
    if len(sale_ids) > 0:
        _sales_cost = fetch_skuvault_sales_cost(store_id, sale_ids)
        process_and_store_sales_cost(store_id, _sales_cost, sales)

def get_skuvault_sales(store_id, start_date, end_date):
    res = {}
    session = db.get_session(store_id)
    with session.begin():
        sales = session.query(skuvault_db.SkuvaultSales).filter(skuvault_db.SkuvaultSales.sale_date >= start_date, skuvault_db.SkuvaultSales.sale_date <= end_date).all()
        for _sale in sales:
            res[_sale.skuvault_id] = _sale.bc_order_id
    return res

def fetch_skuvault_sales_cost_by_date_range(store_id, start_date, end_date):
    sales = get_skuvault_sales(store_id, start_date, end_date)
    if sales and len(sales) > 0:
        fetch_and_update_sales_cost(store_id, sales)