from datetime import datetime
from pymongo import MongoClient
import logging
from utils import get_db_config_from_store, get_current_timestamp
from bson import ObjectId
from config import appconfig
import threading
import traceback

logger = logging.getLogger()


STORE_TASK_MASTER = "task_master"
STORE_COLLECTION = "stores"
EMAIL_TEMPLATE_COLLECTION = "email_templates"
CART_COLLECTION = "cart"
ORDERS_COLLECTION = "orders"
USER_COLLECTION = "users"
EMAIL_NOTIFICATION_MASTER = "email_notification_master"


class DBConfigKey:
    ADMIN_DB_KEY = "admin_db"
    STORE_DB_KEY = "store_db"

class UpdateKeys:
    UPDATED_AT = "ad_updated_at"
    UPDATED = "ad_updated"

def process_data(data):
    if data is not None:
        if '_id' in data:
            data['id'] = str(data['_id'])
            del data['_id']

        for key, value in data.items():
            if isinstance(value, ObjectId):
                data[key] = str(value)
            if isinstance(value, datetime):
                data[key] = str(key)
    return data

class DBClientPool:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            logger.info('Creating the DBClientPool')
            cls._instance = super(DBClientPool, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        logger.info("Entering DBClientPool")
        self.lock = threading.Lock()
        self.connection_pool = {}
        self._stores_by_id = {}
        self._stores_by_storehash = {}
        self._stores_by_domain = {}
        self.update_stores()
        logger.info("Exiting DBClientPool")

    def get_db_client(self, conn_str, db_name):
        db_client = self.connection_pool.get(db_name, None)
        if db_client is None:
            self.lock.acquire()
            try:
                db_client = self.connection_pool.get(db_name, None)
                if db_client is None:
                    db_client = MongoClient(conn_str)[db_name]
                    self.connection_pool[db_name] = db_client
            finally:
                self.lock.release()
        return db_client
    
    def get_app_db_client(self, store, app_name):
        db_client = None
        if store:
            app_db_info = get_db_config_from_store(store, app_name)
            if app_db_info is not None:
                db_name = app_db_info.get("db_name", None)
                host_name = app_db_info.get("host_name", None)
                port = app_db_info.get("port", None)
                if db_name and host_name and port:
                    conn_str = f"mongodb://**************:27017/"
                    db_client = self.get_db_client(conn_str, db_name)
        return db_client
    
    def get_store_db_client(self, store):
        return self.get_app_db_client(store, DBConfigKey.STORE_DB_KEY)
    
    def get_admin_db_client(self, store):
        return self.get_app_db_client(store, DBConfigKey.ADMIN_DB_KEY)
    
    def get_tenant_db_client(self):
        tenant_db_name = appconfig.get_tenant_db_name()
        tenant_db_conn_str = appconfig.get_mongodb_conn_str()
        return self.get_db_client(tenant_db_conn_str, tenant_db_name)
    
    def update_stores(self):
        db_client = self.get_tenant_db_client()
        coll = db_client[STORE_COLLECTION]
        cur = coll.find({"status": "active"})
        for row in cur:
            data = process_data(row)
            self._stores_by_id[data['id']] = data
            self._stores_by_domain[data['domain']] = data
            self._stores_by_storehash[data['bc_config']['store_hash']] = data

    def get_store_by_id(self, store_id):
        return self._stores_by_id.get(store_id, None)
    
    def get_store_by_store_hash(self, store_hash):
        return self._stores_by_storehash.get(store_hash, None)
    
    def get_store_by_domain(self, domain):
        return self._stores_by_domain.get(domain, None)
    
    def get_active_stores(self):
        return self._stores_by_id

db_client_pool = DBClientPool()

def get_admin_db_client(store):
    db_client = None
    if store:
        db_client = db_client_pool.get_admin_db_client(store)
    return db_client

def get_store_db_client(store):
    db_client = None
    db_client = db_client_pool.get_store_db_client(store)
    return db_client

def get_admin_db_client_for_store_id(store_id):
    store = db_client_pool.get_store_by_id(store_id)
    return get_admin_db_client(store)

def get_store_db_client_for_store_id(store_id):
    store = db_client_pool.get_store_by_id(store_id)
    return get_store_db_client(store)

def upsert_documents(store, collection, documents):
    db_client = get_store_db_client(store)
    for document in documents:
        document[UpdateKeys.UPDATED_AT] = get_current_timestamp()
        document[UpdateKeys.UPDATED] = True
        db_client[collection].replace_one({"_id": document['_id']},document,upsert=True)
        
def get_active_stores():
    stores = db_client_pool.get_active_stores()
    result = []
    for store_id, store in stores.items():
        result.append(store)
    return result
    
def get_store_by_id(store_id):
    return db_client_pool.get_store_by_id(store_id)

def get_store_by_store_hash(store_hash):
    return db_client_pool.get_store_by_store_hash(store_hash)

def get_store_by_domain(store_domain):
    return db_client_pool.get_store_by_domain(store_domain)

def get_tenant_db_client():
    return db_client_pool.get_tenant_db_client()

def fetch_store_tasks():
    db_client = get_tenant_db_client()
    coll = db_client[STORE_TASK_MASTER]
    cur = coll.find({})
    store_task_master = {}
    for row in cur:
        data = process_data(row)
        store_task_master[data['id']] = data["tasks"]
    return store_task_master

def clear_updated_field(store, collection_name):   
    try:          
        db = get_store_db_client(store)
        data = {
            UpdateKeys.UPDATED: False
        }
        db[collection_name].update_many({}, { "$set": data })
    except Exception as e:
        logger.error("mongo_db.clear_updated_field: " + collection_name + " : " + str(traceback.format_exc()))


def delete_documents_not_updated(store, collection_name, date_time):   
    pass
    try:          
        db = get_store_db_client(store)
        query = {
            UpdateKeys.UPDATED_AT: {
                "$lt": date_time
            }
        }
        db[collection_name].delete_many(query)
    except Exception as e:
        logger.error("mongo_db.delete_documents_not_updated: " + collection_name + " : " + str(traceback.format_exc()))

def fetch_one_document_from_storefront_collection(store_id, collection_name, query={}, projection=None):
    dbclient = get_store_db_client_for_store_id(store_id)
    if projection:
        res = dbclient[collection_name].find_one(query, projection)
        return process_data(res)
    else:
        res = dbclient[collection_name].find_one(query)
        return process_data(res)
    
def fetch_one_document_from_admin_collection(store_id, collection_name, query={}, projection=None):
    dbclient = get_admin_db_client_for_store_id(store_id)
    if projection:
        return dbclient[collection_name].find_one(query, projection)
    else:
        return dbclient[collection_name].find_one(query)
    
def delete_documents_from_admin_collection(store_id, collection_name, query={}):
    dbclient = get_admin_db_client_for_store_id(store_id)
    return dbclient[collection_name].delete_one(query)

def insert_document_in_admin_collection(store_id, collection, document):
    db_client = get_admin_db_client_for_store_id(store_id)
    result = db_client[collection].insert_one(document)
    return str(result.inserted_id)
    
def fetchall_documents_from_storefront_collection(store_id, collection_name, query={}, projection=None):
    dbclient = get_store_db_client_for_store_id(store_id)
    if projection:
        return dbclient[collection_name].find(query, projection)
    else:
        return dbclient[collection_name].find(query)
    
def fetchall_documents_from_admin_collection(store_id, collection_name, query={}, projection=None):
    dbclient = get_admin_db_client_for_store_id(store_id)
    if projection:
        return dbclient[collection_name].find(query, projection)
    else:
        return dbclient[collection_name].find(query)
