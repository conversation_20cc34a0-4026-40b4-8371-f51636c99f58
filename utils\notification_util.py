import time
from datetime import datetime
from utils import store_util, twillio_util, email_util
from mongo_db import notification_db
from plugin import bc_order, bc_systemlogs
import uuid
import json
import requests
import logging
import traceback

logger = logging.getLogger()

NO_ORDER_ERROR_TYPE = "no_order"
PAYMENT_AUTH_ERROR_TYPE = "payment_auth"

def filter_payment_declined_log(log):
    message = "Message: invalid_user_authentication - User authentication failed due to invalid authentication values"
    if log["severity"] == "errors" and log["type"] == "payment" and message in log["message"]:
        return True
    return False

def process_logs_for_notification(logs):
    result = []
    for log in logs:
        if filter_payment_declined_log(log):
            result.append(log)
    return result

def payment_error_check(store_id):
    store = store_util.get_store_by_id(store_id)
    bc_api = store_util.get_bc_api_creds(store)
    max_page = 100
    retry_count = 0
    max_retry = 5
    last_fetched_log = notification_db.get_latest_store_log(store)
    last_fetched_log_id = 0
    all_logs = []
    if last_fetched_log:
        last_fetched_log_id = last_fetched_log["_id"]
    else:
        max_page = 1
    exit_flag = False

    page = 1

    while not exit_flag:
        errors = bc_systemlogs.fetch_payment_errors(bc_api, page=page)
        if errors:
            if len(errors) == 0:
                break
            else:
                logs = []
                for row in errors:
                    if row["id"] > last_fetched_log_id:
                        row["_id"] = row["id"]
                        if row["severity"] != "errors" and len(row["message"]) > 100:
                            row["message"] = row["message"][:100]
                        del row["id"]
                        all_logs.append(row)
                        logs.append(row)
                    else:
                        exit_flag = True
                        break
                if len(logs) > 0:
                    notification_db.insert_store_logs(store, logs)
                else: 
                    exit_flag = True
                if page >= max_page:
                     exit_flag = True
                page = page + 1
        elif retry_count < max_retry:
            retry_count = retry_count + 1
            time.sleep(20)
        else:
            exit_flag = True

    errs = process_logs_for_notification(all_logs)
    if len(errs) > 0:
        api_creds = store_util.get_twillio_api_info(store_id)
        receivers = api_creds.get("receivers", [])
        message = "Midwestgoods store error. There is an error with your Payment System. Please fix it immediately."
        twillio_util.send_twillio_notification(api_creds, receivers, message)
        notification_db.insert_notifcation(store, PAYMENT_AUTH_ERROR_TYPE, message, errs[0])


def no_order_check(store_id):
    store = store_util.get_store_by_id(store_id)
    last_order = bc_order.fetch_latest_order(store)
    if last_order:
        last_order_time = datetime.strptime(last_order["date_created"], "%a, %d %b %Y %H:%M:%S %z")
        cur_time = datetime.utcnow()
        diff = cur_time - last_order_time.replace(tzinfo=None)
        #logger.info("Order time diff: " + str(diff) + ", Last order time: " + last_order["date_created"] + ", cur time: " + str(cur_time))
        print("Order time diff: " + str(diff.seconds) + ", Last order time: " + last_order["date_created"] + ", cur time: " + str(cur_time))
        if diff.seconds > 10800:
            send_notification = True
            last_notification = notification_db.get_latest_notification(store, NO_ORDER_ERROR_TYPE)
            if last_notification:
                notf_time = last_notification['notified_at']
                if (cur_time.timestamp() - notf_time) < 7200:
                    send_notification = False
            
            if send_notification:
                notification_template = store_util.get_email_template(store_id, store_util.Template.no_order_alert.value)
                send_notification_message(store_id, notification_template)
                notification_db.insert_notifcation(store, NO_ORDER_ERROR_TYPE, notification_template["name"])
    # else:
    #     message = "Failed to execute no order notification task. Couldn't fetch records from BigCommerce"
    #     logger.error(message)
    #     raise Exception(message)

def send_sms_notification(store_id, notification_template):
    api_creds = store_util.get_sms_api_info(store_id)
    message = notification_template.get("message", None)
    receivers = notification_template.get("receivers", None)
    uuid_code = api_creds.get("uuid_code", None)
    if message and receivers:
        uuid_exchange = str(uuid.uuid4()).replace("-", "") + uuid_code
        api_url = api_creds.get("base_url", None)
        access_token = api_creds.get("access_token", None)
        payload = json.dumps({
            "to": receivers,
            "contacts": [],
            "groups": [],
            "text": message,
            "oid": uuid_exchange
        })
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'access-token': access_token
        }
        
        output = requests.request("POST", api_url, headers=headers, verify=False, data=payload).json()


def send_notification_message(store_id, notification_template):
    twillio_template = notification_template.get("twillio", None)
    if twillio_template:
        twillio_message = twillio_template.get("message", None)
        if twillio_message:
            api_creds = store_util.get_twillio_api_info(store_id)
            receivers = twillio_template.get("receivers", None)
            if not receivers or len(receivers) == 0:
                receivers = api_creds.get("receivers", [])
            twillio_util.send_twillio_notification(api_creds, receivers, twillio_message)

    email_util.send_text_email_notification(store_id, notification_template.get("email", None))
    send_sms_notification(store_id, notification_template.get("sms", None))


def twillio_notification_check(store_id):
    notification_template = store_util.get_email_template(store_id, store_util.Template.system_health_check.value)
    send_notification_message(store_id, notification_template)


